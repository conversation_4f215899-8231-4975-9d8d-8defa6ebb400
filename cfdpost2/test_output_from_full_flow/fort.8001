 ppflag   1     1
     1      0.92474E+01       0.40820E+01      0.13329E+02                                                              
 ppflag  16     6
                                                                                                                        
***** BEGINNING INITIALIZATION *****                                                                                    
                                                                                                                        
 reading grid     1 of dimensions (I/J/K) :     2   421   101                                                           
   creating coarser block     2 of dimensions (I/J/K) :     2   211    51                                               
   creating coarser block     3 of dimensions (I/J/K) :     2   106    26                                               
 ppflag  17     4
                                                                                                                        
 computing metrics for block     1  (grid     1)                                                                        
   computing metrics for coarser block     2                                                                            
   computing metrics for coarser block     3                                                                            
 ppflag  18     4
                                                                                                                        
 computing cell volumes for block     1  (grid     1)                                                                   
   computing cell volumes for coarser block     2                                                                       
   computing cell volumes for coarser block     3                                                                       
 ppflag  19     4
                                                                                                                        
 setting blank array to 1.0 for block      1  (grid     1)                                                              
   setting blank array to 1.0 for coarser block     2                                                                   
   setting blank array to 1.0 for coarser block     3                                                                   
 ppflag  20     4
                                                                                                                        
 setting initial conditions as freestream for block     1  (grid     1)                                                 
   setting initial conditions as freestream for coarser block     2                                                     
   setting initial conditions as freestream for coarser block     3                                                     
 ppflag  25     1
                                                                                                                        
 ppflag  26     5
                                                                                                                        
 setting up for minimum distance calculation using recursive-box algorithm                                              
 computing smin (min dist func) for field eqn turb model for block     1                                                
 computing smin (min dist func) for field eqn turb model for block     2                                                
 computing smin (min dist func) for field eqn turb model for block     3                                                
 ppflag  26     0
 ppflag  26     0
 ppflag  27     2
                                                                                                                        
 setting turbulent initial conditions, block     1                                                                      
 ppflag  27     1
 setting turbulent initial conditions, block     2                                                                      
 ppflag  27     1
 setting turbulent initial conditions, block     3                                                                      
 ppflag  28     0
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     3 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   i=idim  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   16,   91                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  106                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  106,   91                                       
           connects to k =    1 of block    3                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   16                                       
           connects to k =    1 of block    3                                                                           
 ppflag   3     0
 ppflag   4    35
                                                                                                                        
 initial cfl number, icyc=   0.150E+01    1                                                                             
                                                                                                                        
 computing time step distribution for block    3                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.12057E+00   0.97861E-05   0.54013E+00                                                             
                                                                                                                        
 computing vorticity magnitude                                                                                          
                                                                                                                        
 evaluating turbulence model                                                                                            
   computing turbulent viscosity using Spalart, block =   3                                                             
     Freestream tur10 =      0.13419460E+01                                                                             
     1st order advection on RHS                                                                                         
   SA constants:                                                                                                        
     cw2=        0.30000                                                                                                
     cw3=        2.00000                                                                                                
     cv1=        7.10000                                                                                                
     ct3=        1.20000                                                                                                
     ct4=        0.50000                                                                                                
     cb1=        0.13550                                                                                                
     cb2=        0.62200                                                                                                
     sigma=        0.66667                                                                                              
     akarman=        0.41000                                                                                            
   block    3 in S-A turb model has no laminar regions                                                                  
   NOTE:  This particular model <<transitions>> on its own, but there is                                                
   no guarantee that it will transition at all.  Check vist3d levels if unsure.                                         
                                                                                                                        
 computing residuals for block     3                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     0
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     3                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag  13     4
 interpolating solution on coarser block   3  to   finer block   2 (grid   1)                                           
   jdim,kdim,idim (finer grid)=  211   51    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  106   26    2                                                                          
   interpolating turb quantities from coarser to finer block                                                            
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4    35
                                                                                                                        
 initial cfl number, icyc=   0.150E+01    1                                                                             
                                                                                                                        
 computing time step distribution for block    2                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.67606E-01   0.29366E-05   0.34110E+00                                                             
                                                                                                                        
 computing vorticity magnitude                                                                                          
                                                                                                                        
 evaluating turbulence model                                                                                            
   computing turbulent viscosity using Spalart, block =   2                                                             
     Freestream tur10 =      0.13419460E+01                                                                             
     1st order advection on RHS                                                                                         
   SA constants:                                                                                                        
     cw2=        0.30000                                                                                                
     cw3=        2.00000                                                                                                
     cv1=        7.10000                                                                                                
     ct3=        1.20000                                                                                                
     ct4=        0.50000                                                                                                
     cb1=        0.13550                                                                                                
     cb2=        0.62200                                                                                                
     sigma=        0.66667                                                                                              
     akarman=        0.41000                                                                                            
   block    2 in S-A turb model has no laminar regions                                                                  
   NOTE:  This particular model <<transitions>> on its own, but there is                                                
   no guarantee that it will transition at all.  Check vist3d levels if unsure.                                         
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     0
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     2                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4     8
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     0
 ppflag   9     2
                                                                                                                        
 restricting variables and residual from finer block   2 to coarser block   3                                           
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     3 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   i=idim  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   16,   91                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  106                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  106,   91                                       
           connects to k =    1 of block    3                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   16                                       
           connects to k =    1 of block    3                                                                           
 ppflag   3     0
 ppflag   4    13
                                                                                                                        
 computing time step distribution for block    3                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.12055E+00   0.13724E-04   0.53998E+00                                                             
                                                                                                                        
 computing residuals for block     3                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     3 from finer block     2; kode=  2                                            
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     3                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag  11     3
 interpolating correction from coarser block   3 to   finer block   2 (grid   1)                                        
   jdim,kdim,idim (finer grid)=  211   51    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  106   26    2                                                                          
 ppflag  13     4
 interpolating solution on coarser block   2  to   finer block   1 (grid   1)                                           
   jdim,kdim,idim (finer grid)=  421  101    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  211   51    2                                                                          
   interpolating turb quantities from coarser to finer block                                                            
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     1 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  421  k=    1,  101                                       
   i=idim  symmetry plane                 type 1001  j=    1,  421  k=    1,  101                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,  101                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,  101                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   61,  361                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  421                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  421,  361                                       
           connects to k =    1 of block    1                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   61                                       
           connects to k =    1 of block    1                                                                           
 ppflag   3     0
 ppflag   4    35
                                                                                                                        
 initial cfl number, icyc=   0.150E+01    1                                                                             
                                                                                                                        
 computing time step distribution for block    1                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.36121E-01   0.21985E-06   0.19475E+00                                                             
                                                                                                                        
 computing vorticity magnitude                                                                                          
                                                                                                                        
 evaluating turbulence model                                                                                            
   computing turbulent viscosity using Spalart, block =   1                                                             
     Freestream tur10 =      0.13419460E+01                                                                             
     1st order advection on RHS                                                                                         
   SA constants:                                                                                                        
     cw2=        0.30000                                                                                                
     cw3=        2.00000                                                                                                
     cv1=        7.10000                                                                                                
     ct3=        1.20000                                                                                                
     ct4=        0.50000                                                                                                
     cb1=        0.13550                                                                                                
     cb2=        0.62200                                                                                                
     sigma=        0.66667                                                                                              
     akarman=        0.41000                                                                                            
   block    1 in S-A turb model has no laminar regions                                                                  
   NOTE:  This particular model <<transitions>> on its own, but there is                                                
   no guarantee that it will transition at all.  Check vist3d levels if unsure.                                         
                                                                                                                        
 computing residuals for block     1                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     0
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     1                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     1 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  421  k=    1,  101                                       
   i=idim  symmetry plane                 type 1001  j=    1,  421  k=    1,  101                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,  101                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,  101                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   61,  361                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  421                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  421,  361                                       
           connects to k =    1 of block    1                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   61                                       
           connects to k =    1 of block    1                                                                           
 ppflag   3     0
 ppflag   4     8
                                                                                                                        
 computing residuals for block     1                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     0
 ppflag   9     2
                                                                                                                        
 restricting variables and residual from finer block   1 to coarser block   2                                           
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4    13
                                                                                                                        
 computing time step distribution for block    2                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.67605E-01   0.10005E-05   0.34108E+00                                                             
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     2 from finer block     1; kode=  2                                            
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     2                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4     8
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     2 from finer block     1; kode=  1                                            
 ppflag   9     2
                                                                                                                        
 restricting variables and residual from finer block   2 to coarser block   3                                           
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     3 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   i=idim  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   16,   91                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  106                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  106,   91                                       
           connects to k =    1 of block    3                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   16                                       
           connects to k =    1 of block    3                                                                           
 ppflag   3     0
 ppflag   4    13
                                                                                                                        
 computing time step distribution for block    3                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.12055E+00   0.56434E-05   0.53995E+00                                                             
                                                                                                                        
 computing residuals for block     3                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     3 from finer block     2; kode=  2                                            
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     3                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag  11     3
 interpolating correction from coarser block   3 to   finer block   2 (grid   1)                                        
   jdim,kdim,idim (finer grid)=  211   51    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  106   26    2                                                                          
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4    13
                                                                                                                        
 computing time step distribution for block    2                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.67605E-01   0.10004E-05   0.34108E+00                                                             
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     2 from finer block     1; kode=  1                                            
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     2                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     2 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   i=idim  symmetry plane                 type 1001  j=    1,  211  k=    1,   51                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   51                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   31,  181                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  211                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  211,  181                                       
           connects to k =    1 of block    2                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   31                                       
           connects to k =    1 of block    2                                                                           
 ppflag   3     0
 ppflag   4     8
                                                                                                                        
 computing residuals for block     2                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     2 from finer block     1; kode=  1                                            
 ppflag   9     2
                                                                                                                        
 restricting variables and residual from finer block   2 to coarser block   3                                           
 ppflag   2    14
                                                                                                                        
 boundary conditions for block     3 (grid      1)                                                                      
   i=   1  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   i=idim  symmetry plane                 type 1001  j=    1,  106  k=    1,   26                                       
   j=   1  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   j=jdim  characteristic inflow/outflow  type 1003  i=    1,    2  k=    1,   26                                       
   k=   1  viscous wall                   type 2004  i=    1,    2  j=   16,   91                                       
           Tw/Tinf    = adiabatic wall                                                                                  
           C_q        =   0.0000                                                                                        
   k=kdim  characteristic inflow/outflow  type 1003  i=    1,    2  j=    1,  106                                       
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=  106,   91                                       
           connects to k =    1 of block    3                                                                           
   k=   1  1-1 blocking                   type    0  i=    1,    2  j=    1,   16                                       
           connects to k =    1 of block    3                                                                           
 ppflag   3     0
 ppflag   4    13
                                                                                                                        
 computing time step distribution for block    3                                                                        
   summary of time step data                                                                                            
   I      cfl          dtrms         dtmin         dtmax                                                                
   1  0.15000E+01   0.12055E+00   0.56433E-05   0.53995E+00                                                             
                                                                                                                        
 computing residuals for block     3                                                                                    
   computing inviscid fluxes, J-direction - flux-difference splitting                                                   
   computing viscous  fluxes, J-direction - central differencing                                                        
   computing inviscid fluxes, K-direction - flux-difference splitting                                                   
   computing viscous  fluxes, K-direction - central differencing                                                        
   computing inviscid fluxes, I-direction - flux-difference splitting                                                   
   computing viscous  fluxes, I-direction - central differencing                                                        
 ppflag   8     2
                                                                                                                        
 adding residual correction for block     3 from finer block     2; kode=  2                                            
 ppflag   9     6
                                                                                                                        
 updating with 3-D 3-factor AF scheme for block     3                                                                   
   using first order time differencing                                                                                  
   diagonal inversion in J-direction                                                                                    
   diagonal inversion in K-direction                                                                                    
   diagonal inversion in I-direction                                                                                    
 ppflag  11     3
 interpolating correction from coarser block   3 to   finer block   2 (grid   1)                                        
   jdim,kdim,idim (finer grid)=  211   51    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  106   26    2                                                                          
 ppflag  11     3
 interpolating correction from coarser block   2 to   finer block   1 (grid   1)                                        
   jdim,kdim,idim (finer grid)=  421  101    2                                                                          
   jj2,kk2,ii2  (coarser grid)=  211   51    2                                                                          
