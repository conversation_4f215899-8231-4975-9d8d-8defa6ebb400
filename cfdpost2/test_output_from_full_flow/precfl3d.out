 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * *                                                               * *
 * *                  PRECFL3D - CFL3D PREPROCESSOR                * *
 * *                                                               * *
 * *   VERSION 6.7 :  Computational Fluids Lab, Mail Stop 128,     * *
 * *                  NASA Langley Research Center, Hampton, VA    * *
 * *                  Release Date:  February  1, 2017.            * *
 * *                                                               * *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

 memory allocation:     0.031104 Mbytes, double precision

input/output files:
  cfl3d.xyz                                                   
  field.g                                                     
  field.q                                                     
  cfl3d.out                                                   
  resid.out                                                   
  cfl3d.turres                                                
  cfl3d.blomax                                                
  cfl3d.2out                                                  
  cfl3d.prt                                                   
  cfl3d.press                                                 
  ovrlp.bin                                                   
  patch.bin                                                   
  cfl3d.restart                                               
>--------------------- begin keyword-driven input section --------------------->
cprec 0
ides 0
iblend 0
iteravg 0
icoarsemovie 0
i2dmovie 0
isample 0
<---------------------- end keyword-driven input section ----------------------<

 CFL3D V6 INPUT FILE GENERATED WITH GRIDGEN                                      
      Mach     alpha      beta      ReUe   Tinf,dR     ialph    ihstry
   0.50000   1.50000   0.00000 0.200E+07 460.00000         1         0

 note: isnd and c2spe no longer used - see notes on bc2004

      sref      cref      bref       xmc       ymc       zmc
     1.000   1.00000   1.00000   0.25000   0.00000   0.00000
        dt     irest   iflagts      fmax     iunst   cfl_tau
  -1.50000         0         0   1.00000         0   7.50000
     ngrid   nplot3d    nprint    nwrest      ichk       i2d    ntstep       ita
        -1         1        -1       500         0         0         1         1

 the input values of Mach, alpha, beta and ialph yield a freestream velocity
 with x,y,z components: u0 =   0.4998,  v0 =   0.0131,  w0 =   0.0000
           ** make sure this is consistent with your grid **

       ncg       iem  iadvance    iforce  ivisc(i)  ivisc(j)  ivisc(k)
         2         0         0       333         5         5         5
      idim      jdim      kdim
         2       421       101
 WARNING: idim=2 for i2d=0 may not be a good idea
    ilamlo    ilamhi    jlamlo    jlamhi    klamlo    klamhi
         0         0         0         0         0         0
     inewg    igridc        is        js        ks        ie        je        ke
         0         0         0         0         0         0         0         0
  idiag(i)  idiag(j)  idiag(k)  iflim(i)  iflim(j)  iflim(k)
         1         1         1         4         4         4
   ifds(i)   ifds(j)   ifds(k)  rkap0(i)  rkap0(j)  rkap0(k)
         1         1         1    0.3333    0.3333    0.3333
      grid     nbci0   nbcidim     nbcj0   nbcjdim     nbck0   nbckdim    iovrlp
         1         1         1         1         1         3         1         0
i0:   grid   segment    bctype      jsta      jend      ksta      kend     ndata
         1         1      1001         1       421         1       101         0
idim: grid   segment    bctype      jsta      jend      ksta      kend     ndata
         1         1      1001         1       421         1       101         0
j0:   grid   segment    bctype      ista      iend      ksta      kend     ndata
         1         1      1003         1         2         1       101         0
jdim: grid   segment    bctype      ista      iend      ksta      kend     ndata
         1         1      1003         1         2         1       101         0
k0:   grid   segment    bctype      ista      iend      jsta      jend     ndata
         1         1         0         1         2         1        61         0
         1         2      2004         1         2        61       361         2
   tw/tinf        cq
   0.00000   0.00000
         1         3         0         1         2       361       421         0
kdim: grid   segment    bctype      ista      iend      jsta      jend     ndata
         1         1      1003         1         2         1       421         0
      mseq    mgflag    iconsf       mtt      ngam
         3         1         0         0         2
      issc epsssc(1) epsssc(2) epsssc(3)      issr epsssr(1) epsssr(2) epsssr(3)
         0    0.3000    0.3000    0.3000         0    0.3000    0.3000    0.3000
      ncyc    mglevg     nemgl     nitfo
       500         1         0         0
      1000         2         0         0
      1000         3         0         0
      mit1      mit2      mit3      mit4      mit5  ...
         1
         1         1
         1         1         1
 1-1 blocking data:
      nbli
         1
  number    grid    ista    jsta    ksta    iend    jend    kend   isva1   isva2
       1       1       1       1       1       2      61       1       1       2
  number    grid    ista    jsta    ksta    iend    jend    kend   isva1   isva2
       1       1       1     421       1       2     361       1       1       2
 patch interface data:
    ninter
         0
 plot3d output:
  grid block iptyp  ista  iend  iinc  jsta  jend  jinc  ksta  kend  kinc
     1     1    -2     1     2     1     1   421     1     1   101     1
 movie
   500
 print out:
  grid block iptyp  ista  iend  iinc  jsta  jend  jinc  ksta  kend  kinc
     1     1     0     1     2     1    61   361     1     1     1     1

 SUMMARY BY GRIDS
      grid     level     block      jdim      kdim      idim   grid pts.
         1         3         1       421       101         2       85042
         1         2         2       211        51         2       21522
         1         1         3       106        26         2        5512

                                                       TOTAL      112076

 SUMMARY BY LEVELS
     level      grid     block
         3         1         1
         2         1         2
         1         1         3

     level     cells
         3     42000
         2     10500
         1      2625

 level of coarsest global mesh=  1

 level of finest global mesh  =  3

 SUMMARY OF GRID SEQUENCES
  sequence   starting level     ending level  cells(finest global+embeded)
         1                1                1             2625
         2                2                1            10500
         3                3                1            42000

 BLOCK TO NODE MAPPING
 no. of blocks =    3
 no. of  nodes =    1
 block    node
    1       1
    2       1
    3       1
   
 ***********************************************
 
     PERMANENT STORAGE REQUIREMENTS - W ARRAY
                 SEQUENTIAL BUILD
 
 ***********************************************

 summary of starting locations for block information on single node

 igrid block  jdim  kdim  idim
     1     1   421   101     2
   lw( 1,  1)=                1
   lw( 2,  1)=           425211
   lw( 3,  1)=           427231
   lw( 4,  1)=           435651
   lw( 5,  1)=          1286071
   lw( 6,  1)=          1498676
   lw( 7,  1)=          1711281
   lw( 8,  1)=          2136491
   lw( 9,  1)=          2179012
   lw(10,  1)=          2221533
   lw(11,  1)=          2306575
   lw(12,  1)=          2391617
   lw(13,  1)=          2476659
   lw(14,  1)=          2561701
   lw(15,  1)=          2603701
   lw(16,  1)=          2603701
   lw(17,  1)=          2603701
   lw(18,  1)=          2603701
   lw(19,  1)=          2688743
   lw(20,  1)=          2858827
   lw(21,  1)=          2858827
   lw(22,  1)=          2858827
   lw(23,  1)=          2858827
   lw(24,  1)=          2859635
   lw(25,  1)=          2863003
   lw(26,  1)=          3203171
   lw(27,  1)=          3203171
   lw(28,  1)=          3203171
   lw(29,  1)=          3203575
   lw(30,  1)=          3205259
   lw(31,  1)=          3375343
   lw(32,  1)=          3375545
   lw(33,  1)=          3376387
   lw(34,  1)=          3461429
   lw(35,  1)=          3461429
   lw(36,  1)=          3461429
   lw(37,  1)=          3461429
   lw(38,  1)=          3461429
   lw(39,  1)=          3461429
   lw(40,  1)=          3461429
   lw(41,  1)=          3461429
   lw(42,  1)=          3461429
   lw(43,  1)=          3461429
   lw(44,  1)=          3468629
   lw(45,  1)=          3468629
   lw(46,  1)=          3468629
   lw(47,  1)=          3468629
   lw(48,  1)=          3846629
   lw(49,  1)=          3846629
   lw(50,  1)=          3847033
   lw(51,  1)=          3848717
   lw(52,  1)=          4018801
   lw(53,  1)=          4018801
   lw(54,  1)=          4018801
   lw(55,  1)=          4018801
   lw(56,  1)=          4018801
   lw(57,  1)=          4018801
   lw(58,  1)=          4018801
   lw(59,  1)=          4018801
   lw(60,  1)=          4018801
   lw(61,  1)=          4018801
   lw(62,  1)=          4018801
   lw(63,  1)=          4018801
   lw(64,  1)=          4018801
   lw(65,  1)=          4018801
   lw(66,  1)=          4018801
   lw(67,  1)=          4954263
   lw(68,  1)=          4954263
   lw(69,  1)=          4958707
   lw(70,  1)=          4977231
   lw(71,  1)=          6848155
   lw(72,  1)=          6848155
   lw(73,  1)=          6848155
   lw(74,  1)=          6848155
   lw(75,  1)=          6848155
   lw(76,  1)=          6848155
   lw(77,  1)=          6848155
   lw(78,  1)=          6848155
   lw(79,  1)=          6848155
   lw(80,  1)=          6848155
   lwdat(  1, 1, 1)=    3468629
   lwdat(  1, 1, 2)=    3468629
   lwdat(  1, 1, 3)=    3461429
   lwdat(  1, 1, 4)=    3461429
   lwdat(  1, 1, 5)=    3461429
   lwdat(  1, 2, 5)=    3461429
   lwdat(  1, 3, 5)=    3468629
   lwdat(  1, 1, 6)=    3468629

 igrid block  jdim  kdim  idim
     1     2   211    51     2
   lw( 1,  2)=          6848155
   lw( 2,  2)=          6955765
   lw( 3,  2)=          6956785
   lw( 4,  2)=          6961005
   lw( 5,  2)=          7176225
   lw( 6,  2)=          7230030
   lw( 7,  2)=          7283835
   lw( 8,  2)=          7391445
   lw( 9,  2)=          7402206
   lw(10,  2)=          7412967
   lw(11,  2)=          7434489
   lw(12,  2)=          7456011
   lw(13,  2)=          7477533
   lw(14,  2)=          7499055
   lw(15,  2)=          7509555
   lw(16,  2)=          7509555
   lw(17,  2)=          7617165
   lw(18,  2)=          7670970
   lw(19,  2)=          7692492
   lw(20,  2)=          7735536
   lw(21,  2)=          7735536
   lw(22,  2)=          7735536
   lw(23,  2)=          7735536
   lw(24,  2)=          7735944
   lw(25,  2)=          7737632
   lw(26,  2)=          7823720
   lw(27,  2)=          7823720
   lw(28,  2)=          7823720
   lw(29,  2)=          7823924
   lw(30,  2)=          7824768
   lw(31,  2)=          7867812
   lw(32,  2)=          7867914
   lw(33,  2)=          7868336
   lw(34,  2)=          7889858
   lw(35,  2)=          7889858
   lw(36,  2)=          7889858
   lw(37,  2)=          7889858
   lw(38,  2)=          7889858
   lw(39,  2)=          7889858
   lw(40,  2)=          7889858
   lw(41,  2)=          7889858
   lw(42,  2)=          7889858
   lw(43,  2)=          7889858
   lw(44,  2)=          7893458
   lw(45,  2)=          7893458
   lw(46,  2)=          7893458
   lw(47,  2)=          7893458
   lw(48,  2)=          7987958
   lw(49,  2)=          7987958
   lw(50,  2)=          7988162
   lw(51,  2)=          7989006
   lw(52,  2)=          8032050
   lw(53,  2)=          8032050
   lw(54,  2)=          8032050
   lw(55,  2)=          8032050
   lw(56,  2)=          8032050
   lw(57,  2)=          8032050
   lw(58,  2)=          8032050
   lw(59,  2)=          8032050
   lw(60,  2)=          8032050
   lw(61,  2)=          8032050
   lw(62,  2)=          8032050
   lw(63,  2)=          8032050
   lw(64,  2)=          8032050
   lw(65,  2)=          8032050
   lw(66,  2)=          8032050
   lw(67,  2)=          8268792
   lw(68,  2)=          8268792
   lw(69,  2)=          8271036
   lw(70,  2)=          8280320
   lw(71,  2)=          8753804
   lw(72,  2)=          8753804
   lw(73,  2)=          8753804
   lw(74,  2)=          8753804
   lw(75,  2)=          8753804
   lw(76,  2)=          8753804
   lw(77,  2)=          8753804
   lw(78,  2)=          8753804
   lw(79,  2)=          8753804
   lw(80,  2)=          8753804
   lwdat(  2, 1, 1)=    7893458
   lwdat(  2, 1, 2)=    7893458
   lwdat(  2, 1, 3)=    7889858
   lwdat(  2, 1, 4)=    7889858
   lwdat(  2, 1, 5)=    7889858
   lwdat(  2, 2, 5)=    7889858
   lwdat(  2, 3, 5)=    7893458
   lwdat(  2, 1, 6)=    7893458

 igrid block  jdim  kdim  idim
     1     3   106    26     2
   lw( 1,  3)=          8753804
   lw( 2,  3)=          8781364
   lw( 3,  3)=          8781884
   lw( 4,  3)=          8784004
   lw( 5,  3)=          8839124
   lw( 6,  3)=          8852904
   lw( 7,  3)=          8866684
   lw( 8,  3)=          8894244
   lw( 9,  3)=          8897000
   lw(10,  3)=          8899756
   lw(11,  3)=          8905268
   lw(12,  3)=          8910780
   lw(13,  3)=          8916292
   lw(14,  3)=          8921804
   lw(15,  3)=          8924429
   lw(16,  3)=          8924429
   lw(17,  3)=          8951989
   lw(18,  3)=          8965769
   lw(19,  3)=          8971281
   lw(20,  3)=          8982305
   lw(21,  3)=          8982305
   lw(22,  3)=          8982305
   lw(23,  3)=          8982305
   lw(24,  3)=          8982513
   lw(25,  3)=          8983361
   lw(26,  3)=          9005409
   lw(27,  3)=          9005409
   lw(28,  3)=          9005409
   lw(29,  3)=          9005513
   lw(30,  3)=          9005937
   lw(31,  3)=          9016961
   lw(32,  3)=          9017013
   lw(33,  3)=          9017225
   lw(34,  3)=          9022737
   lw(35,  3)=          9022737
   lw(36,  3)=          9022737
   lw(37,  3)=          9022737
   lw(38,  3)=          9022737
   lw(39,  3)=          9022737
   lw(40,  3)=          9022737
   lw(41,  3)=          9022737
   lw(42,  3)=          9022737
   lw(43,  3)=          9022737
   lw(44,  3)=          9024537
   lw(45,  3)=          9024537
   lw(46,  3)=          9024537
   lw(47,  3)=          9024537
   lw(48,  3)=          9048162
   lw(49,  3)=          9048162
   lw(50,  3)=          9048266
   lw(51,  3)=          9048690
   lw(52,  3)=          9059714
   lw(53,  3)=          9059714
   lw(54,  3)=          9059714
   lw(55,  3)=          9059714
   lw(56,  3)=          9059714
   lw(57,  3)=          9059714
   lw(58,  3)=          9059714
   lw(59,  3)=          9059714
   lw(60,  3)=          9059714
   lw(61,  3)=          9059714
   lw(62,  3)=          9059714
   lw(63,  3)=          9059714
   lw(64,  3)=          9059714
   lw(65,  3)=          9059714
   lw(66,  3)=          9059714
   lw(67,  3)=          9120346
   lw(68,  3)=          9120346
   lw(69,  3)=          9121490
   lw(70,  3)=          9126154
   lw(71,  3)=          9247418
   lw(72,  3)=          9247418
   lw(73,  3)=          9247418
   lw(74,  3)=          9247418
   lw(75,  3)=          9247418
   lw(76,  3)=          9247418
   lw(77,  3)=          9247418
   lw(78,  3)=          9247418
   lw(79,  3)=          9247418
   lw(80,  3)=          9247418
   lwdat(  3, 1, 1)=    9024537
   lwdat(  3, 1, 2)=    9024537
   lwdat(  3, 1, 3)=    9022737
   lwdat(  3, 1, 4)=    9022737
   lwdat(  3, 1, 5)=    9022737
   lwdat(  3, 2, 5)=    9022737
   lwdat(  3, 3, 5)=    9024537
   lwdat(  3, 1, 6)=    9024537
 
 ***********************************************
 
    PERMANENT STORAGE REQUIREMENTS - W ARRAY    
             SUMMARY FOR ALL BUILDS
 
 ***********************************************

 memory (mw) for w storage (sequential) =    9.2474

 memory (mw) for w storage (nodes)

  node   total points  memory (mw)
     0          55125       7.4860
     1          55125       9.2474

     total =    55125      16.7334

   maximum =    55125       9.2474
 
 
 ***********************************************
 
    TEMPORARY STORAGE REQUIREMENTS - WK ARRAY
         SEQUENTIAL AND PARALLEL BUILDS
 
 ***********************************************
  3(turbs)        itemp, need =        43710       43710
        parallel: itemp, need =        43710       43710
  4(findmin_new)  itemp, need =       460322      460322
        parallel: itemp, need =       460322      460322
  6(resid)        itemp, need =       182194      460322
        parallel: itemp, need =       182194      460322
  1(addx)         itemp, need =       266043      460322
        parallel: itemp, need =       266043      460322
  3(turbs)        itemp, need =       171395      460322
        parallel: itemp, need =       171395      460322
  4(findmin_new)  itemp, need =       460322      460322
        parallel: itemp, need =       460322      460322
  6(resid)        itemp, need =       700234      700234
        parallel: itemp, need =       700234      700234
  7(collq)        itemp, need =       161416      700234
        parallel: itemp, need =       161416      700234
  1(addx)         itemp, need =      1046543     1046543
        parallel: itemp, need =      1046543     1046543
  3(turbs)        itemp, need =       678765     1046543
        parallel: itemp, need =       678765     1046543
  4(findmin_new)  itemp, need =       460322     1046543
        parallel: itemp, need =       460322     1046543
  6(resid)        itemp, need =      2744314     2744314
        parallel: itemp, need =      2744314     2744314
  7(collq)        itemp, need =       637816     2744314
        parallel: itemp, need =       637816     2744314
  11(cellvol)     itemp, need =       637815     2744314
        parallel: itemp, need =       637815     2744314
  12(dird)        itemp, need =       168000     2744314
        parallel: itemp, need =       168000     2744314
  14(diagonal J)  itemp, need =      2744314     2744314
        parallel: itemp, need =      2744314     2744314
  16(diagonal K)  itemp, need =      2744314     2744314
        parallel: itemp, need =      2744314     2744314
  18(diagonal I)  itemp, need =      2744314     2744314
        parallel: itemp, need =      2744314     2744314
  19(metric)      itemp, need =      1936415     2744314
        parallel: itemp, need =      1936415     2744314
  23(plot3d)      itemp, need =      4082016     4082016
        parallel: itemp, need =      4082016     4082016
  23(print)       itemp, need =       180318     4082016
        parallel: itemp, need =       180318     4082016
  -async are requirements for asynchronous message passing, by node
  22(1:1-async)   itemp, need =            1     4082016
 
 ***********************************************
 
       TEMPORARY STORAGE REQUIREMENTS - WK
             SUMMARY FOR ALL BUILDS
 
 ***********************************************

 memory (mw) for wk storage (sequential) =    4.0820

 memory (mw) for wk storage (nodes)

  node   total points  memory (mw)
     1          55125       4.0820

   maximum =    55125       4.0820
 
 ***********************************************
 
     PARAMETER SIZES REQUIRED FOR THIS CASE:
 
 ***********************************************
 
 mwork    =   13329437
 mworki   =      91875
 nplots   =          1
 minnode  =          1
 iitot    =          1
 intmax   =          1
 maxxe    =          1
 mxbli    =          3
 nsub1    =          1
 lbcprd   =          1
 lbcemb   =          1
 lbcrad   =          1
 maxbl    =          3
 maxgr    =          1
 maxseg   =          3
 maxcs    =          1
 ncycmax  =       2500
 intmx    =          1
 mxxe     =          1
 mptch    =          1
 msub1    =          1
 nmds     =          1
 maxaes   =          1
 ibufdim  =       2000
 nbuf     =          4
 nslave   =          1
 nmaster  =         21
 maxsegdg =          1
 maxsw    =          1
 maxsmp   =          1
 
 
 **************************************************************
 
    SUMMARY OF STORAGE REQUIREMENTS - W + WK ARRAYS
 
  sequential version:
 
          permanent array w   requires    9247418 (words)
          temporary array wk  requires    4082019 (words)
          temporary array iwk requires      91875 (words)
 
  parallel version, per node:
 
          permanent array w   requires    9247420 (words)
          temporary array wk  requires    4082019 (words)
          temporary array iwk requires      91875 (words)
   
   
 >>> Estimate for mwork      (sequential)     =   13329437 <<<

 >>> Estimate for mworki     (sequential)     =      91875 <<<

 >>> Estimate for mwork  (per node, parallel) =   13329439 <<<

 >>> Estimate for mworki (per node, parallel) =      91875 <<<

 >>> Parallel code sized for   1 nodes, min. (+host)       <<<
 
 **************************************************************
 

 memory for precfl3d has been deallocated
