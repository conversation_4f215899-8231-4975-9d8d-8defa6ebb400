{"segment-125a8996-1754-41d3-a6b9-c84f1c5e8742": {"type": "main", "title": "# New and Updated Features of Version 6", "index": 0}, "segment-96fbd4e8-f3cc-4250-a21b-9a1fc8ef7671": {"type": "main", "title": "CONTENTS", "index": 1}, "segment-dc45b35d-5579-44c6-8f14-112e6c7f2355": {"type": "main", "title": "NEW FEATURES", "index": 2}, "segment-856bf5fb-76a5-4805-bf30-09a829f50e5b": {"type": "main", "title": "Keyword Driven Input", "index": 3}, "segment-3fcb4af2-0e0e-4a16-a0fb-ee344b7901c8": {"type": "sub", "title": "The following example illustrates the use of the keyword input: INPUT/OUTPUT FILES", "parent_index": 3, "sub_index": 0}, "segment-a481783b-7fab-4316-bf8b-2d02a7cb1b6c": {"type": "sub", "title": "VALID KEYWORDS: Physical Properties", "parent_index": 3, "sub_index": 1}, "segment-04a3ba0a-acb0-46bf-940f-c505e9a3f616": {"type": "sub", "title": "VALID KEYWORDS: Preconditioning Name Description Default Value avn factor multiplying ure \\( {f}^{2} \\) for preconditioning 1.0 cprec relative amount of preconditioning 0.0 uref limiting velocity for preconditioning xmach VALID KEYWORDS: Specified CL Name Description Default Value cltarg target CI 99999. dalim limit of alpha change (deg) per update 0.2 icycupdt number of cycles between alpha updates 1 (if >0; if <0, alpha is never updated)", "parent_index": 3, "sub_index": 2}, "segment-1e226d87-8f05-46ae-9823-76865a218ebb": {"type": "sub", "title": "VALID KEYWORDS: Turbulence Models", "parent_index": 3, "sub_index": 3}, "segment-75085492-a086-42ef-8489-86c028388276": {"type": "main", "title": "VALID KEYWORDS: Deformation/Grid Motion", "index": 4}, "segment-a5642a8b-9fd4-4978-8b52-cdd6c992c5c5": {"type": "sub", "title": "VALID KEYWORDS: Memory Management Name Description Default Value By default, after V6.5, the memory for the velocity-derivative ux array is 0 lowmem_ux always allocated and ux is always (always compute ux computed. Setting to 1 reverts to array) old usage (compute only when needed). additional memory (in words) added 0 memadd to work array (no addition to work) (in case sizer underestimates) additional memory (in words) added 0 memaddi to iwork array (no addition to iwork) (in case sizer underestimates) VALID KEYWORDS: Reference Frame", "parent_index": 4, "sub_index": 0}, "segment-4afd3b74-6954-4c5a-b668-b83a419999c4": {"type": "sub", "title": "Note: the spelling of a keyword is important; if you misspell the name of the keyword, or use a keyword name that is not supported, the code will stop and print out the following message, following the offending keyword", "parent_index": 4, "sub_index": 1}, "segment-8e7c7897-a126-485e-a6f1-0319a66a3821": {"type": "main", "title": "Dynamic Memory Allocation:", "index": 5}, "segment-94ffb197-5164-473e-ac27-040c7dee8f1a": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON><PERSON><PERSON> <PERSON> and <PERSON> of Analytical Services and Materials, Inc., for showing how to implement dynamic memory allocation within FORTRAN 77", "parent_index": 5, "sub_index": 0}, "segment-eaa0b711-0cf7-412f-a056-92121bdd1d09": {"type": "main", "title": "Low Mach Number Preconditioning:", "index": 6}, "segment-b9b37af1-2355-4a01-88e7-97bd4e98239e": {"type": "main", "title": "Sample Results", "index": 7}, "segment-21078136-d3e8-478d-a916-e52af8820ae5": {"type": "sub", "title": "The following definitions apply", "parent_index": 7, "sub_index": 0}, "segment-3fcc3251-0cf4-4f40-9d89-0649705c60ea": {"type": "sub", "title": "The basic preconditioning keyword input is simply", "parent_index": 7, "sub_index": 1}, "segment-77850de6-70a7-4aa2-a9c5-90f2ef5e5560": {"type": "sub", "title": "Note: the default values of uref and avn will be used", "parent_index": 7, "sub_index": 2}, "segment-14e29c2b-7117-4b3b-a87c-07b6fd85b93f": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON> of North Carolina State for the implementation of low Mach number preconditioning within CFL3D", "parent_index": 7, "sub_index": 3}, "segment-29d75645-2a14-4791-a1d3-1335c10135c2": {"type": "main", "title": "Additional Plot3d Output Options:", "index": 8}, "segment-7ce74c1d-75f8-4fad-ba7a-dc02d6c25496": {"type": "main", "title": "Specified CI Option:", "index": 9}, "segment-eda02701-f39f-43d4-84c9-bd79daa5ec6b": {"type": "main", "title": "cltarg", "index": 10}, "segment-781e4ff2-ea32-4b93-bc86-aaeef34c8f69": {"type": "main", "title": "ioalph", "index": 11}, "segment-c450f939-d20c-4132-a87c-f288127e0f69": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON> of Boeing-Seattle for providing the coding of this feature.", "parent_index": 11, "sub_index": 0}, "segment-ce5c4a3a-12cc-4fad-a841-37113d35ca25": {"type": "main", "title": "Block and Input File Splitter:", "index": 12}, "segment-3ff6274f-faf9-43e2-8ad0-569c8d9b166e": {"type": "main", "title": "New Flux Limiter (iflim=4)", "index": 13}, "segment-ee7c05e3-2b9d-4d66-a04b-3f7bb8081e75": {"type": "sub", "title": "Note 1: iflim=3 and iflim=4 will generally give slightly different results even for single block grids,since iflim=3 actually bases the cutoff parameter on the dimension in each direction for which the limiter is applied; iflim=4 uses a cutoff parameter that is isotropic.", "parent_index": 13, "sub_index": 0}, "segment-772433d1-023a-4540-8a6a-7343d186c4f7": {"type": "sub", "title": "Note 2: limiters iflim=1 and iflim=2 (and iflim=0, no limiting) are uneffected by block splitting when fully converged.", "parent_index": 13, "sub_index": 1}, "segment-630bf886-c8f9-4ea4-935b-d73e67981ca8": {"type": "main", "title": "New Ronnie Input Options:", "index": 14}, "segment-44a27e7e-c22f-40da-80eb-031ba731dedd": {"type": "sub", "title": "The differences between the old and new styles are described below", "parent_index": 14, "sub_index": 0}, "segment-6c709e18-cfac-4dfa-acb6-79b494a6b239": {"type": "sub", "title": "Title", "parent_index": 14, "sub_index": 1}, "segment-ff4c760c-1081-4406-976a-c83fcc175b33": {"type": "sub", "title": "New Style", "parent_index": 14, "sub_index": 2}, "segment-5859d837-00f9-4252-88f2-68d3ac825685": {"type": "sub", "title": "Old Style", "parent_index": 14, "sub_index": 3}, "segment-23909b3f-f390-4099-962a-9501c79c1520": {"type": "main", "title": "Error Code Files:", "index": 15}, "segment-04cee185-2605-4af2-b0f4-16ee182e8aff": {"type": "main", "title": "CFL3D ERROR CODES", "index": 16}, "segment-caff704b-1318-483b-abfe-a49a7d27188b": {"type": "main", "title": "New Normal Momentum BC 1006 for Inviscid Flows:", "index": 17}, "segment-15e91053-5d7b-4397-a217-17d75b3372e9": {"type": "main", "title": "More General BC 2008, 2018, and 2028:", "index": 18}, "segment-d30dfff4-a080-41ec-bea1-8c88001eec1c": {"type": "sub", "title": "BC2008: the user specifies density and velocity components, and pressure is extrapolated from the interior. The standard required input data is", "parent_index": 18, "sub_index": 0}, "segment-1b627eff-0a99-4c1e-8a2f-c9c7508cb6f2": {"type": "sub", "title": "Important: previous usage of bc2008 was ndata=5, with u/a_inf, v/a_inf, w/a_inf, turb1 and turb2 specified; this usage is not correct for Version 6; Version 5 still supports the old usage", "parent_index": 18, "sub_index": 1}, "segment-1b12ee36-5701-46c2-9a62-89fb62bf7bb7": {"type": "main", "title": "Imposing wall velocity through BC 2034:", "index": 19}, "segment-c2a19c63-0a4f-4ca9-a1ac-e30e116e6e09": {"type": "main", "title": "Specifying transition through BC 2014:", "index": 20}, "segment-6202fc8f-5f72-4528-9ceb-9342f500ce35": {"type": "sub", "title": "The standard required input data is", "parent_index": 20, "sub_index": 0}, "segment-282c2b8f-a4ec-416b-b9ea-6854771759ac": {"type": "sub", "title": "JO: GRID SEGMENT BCTYPE ISTA IEND KSTA", "parent_index": 20, "sub_index": 1}, "segment-d1806e62-ed47-4a95-be4e-c0d15f0fbf7e": {"type": "sub", "title": "JDIM: GRID SEGMENT BCTYPE ISTA IEND KSTA", "parent_index": 20, "sub_index": 2}, "segment-57d78cdf-5807-4bd3-9e80-798db0fb3c86": {"type": "main", "title": "Turbulence Data Input For 2000 Series BC's:", "index": 21}, "segment-926eb7b9-c067-4c34-ad87-c6099c2d048b": {"type": "sub", "title": "Note: the additional turbulence data must be input as nondimensional, appropriate for the particular turbulence model in use. See Appendix H of the Version 5 manual for details of the turbulence models and the appropriate nondimensionalizations.", "parent_index": 21, "sub_index": 0}, "segment-5c7a21bf-4201-4d86-a32a-4a09afcbc89b": {"type": "main", "title": "New BC 2016 for Suction and Blowing:", "index": 22}, "segment-4e639478-355d-45bc-b052-b15c90c1a4ac": {"type": "sub", "title": "BC 2016 requires a total of seven pieces of auxiliary data", "parent_index": 22, "sub_index": 0}, "segment-20d9a2a6-a8cc-470d-a750-4dad94d8a3df": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON> of Clear Science Corp. for coding this boundary condition.", "parent_index": 22, "sub_index": 1}, "segment-2a80933d-6779-41a3-bef4-ed51f4d9b2a2": {"type": "main", "title": "Solution Derivatives Via Complex Variables", "index": 23}, "segment-c7fe026c-a1ca-4a50-accd-aff122ee87da": {"type": "sub", "title": "A complex version of the parallel code can be generated by typing", "parent_index": 23, "sub_index": 0}, "segment-62e2c2ae-af5b-4ce1-a277-b14542ad61b3": {"type": "main", "title": "Entropy Fix for High Mach Number Conditions", "index": 24}, "segment-e871ece6-d138-4811-8bcd-33a694fca900": {"type": "main", "title": "Sample Results", "index": 25}, "segment-975a6228-b6d8-4898-aeb3-5eb1ffa87621": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON> of NASA-Langley Research Center for providing the LAURA implementation of this feature, which", "parent_index": 25, "sub_index": 0}, "segment-6802db64-b953-4e43-9646-604419a4da9d": {"type": "main", "title": "Turbulence Model Descriptions", "index": 26}, "segment-e803ed0b-be9c-4ffe-9525-e6542a51e661": {"type": "main", "title": "Compared to Version 5", "index": 27}, "segment-dbda4d7f-10fd-41e6-b714-93a54e4f4d84": {"type": "main", "title": "Notes:", "index": 28}, "segment-b00a8709-e388-473f-8b30-c0bbc682e5d1": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON>, <PERSON>, and <PERSON> of North Carolina State University for coding the k -enstrophy model.", "parent_index": 28, "sub_index": 0}, "segment-54588dd2-35e1-4c78-88d7-98f11806c25d": {"type": "main", "title": "Inflow BC 2009, 2010, and 2019:", "index": 29}, "segment-d5907b13-57f9-490d-9f1f-d717838d82da": {"type": "main", "title": "Specified Pressure Ratio 2103:", "index": 30}, "segment-a28be73c-047b-446b-b282-6ea9ab16bb9c": {"type": "sub", "title": "The BC requires", "parent_index": 30, "sub_index": 0}, "segment-4e1709e3-8fb5-4f7e-9abb-e887fc9eb4e9": {"type": "main", "title": "2nd order temporal accuracy for turb models:", "index": 31}, "segment-77d5bcf3-cce0-4e87-aee7-3cc03b99e418": {"type": "main", "title": "Graceful user-requested stop during program execution:", "index": 32}, "segment-f7b08e3f-b48a-4d0c-87ed-d324a0788745": {"type": "main", "title": "Source terms for non-inertial (rotating) reference frames:", "index": 33}, "segment-e20d803d-3db1-41d1-8205-c6f476a1bcf8": {"type": "main", "title": "New ihist option for force/moment convergence tracking:", "index": 34}, "segment-8470b1ec-b530-428c-baea-56e674f41da6": {"type": "main", "title": "DES and DDES Capability:", "index": 35}, "segment-c337a677-37f9-4e0e-9929-1c6c4d9cac21": {"type": "main", "title": "Full Navier-Stokes Capability:", "index": 36}, "segment-4486f938-95b1-4f01-a63b-2ce2a6dedcde": {"type": "sub", "title": "Acknowledgment: Thanks to <PERSON><PERSON><PERSON> of Boeing Commercial Airplane Group, Seattle, for implementation.", "parent_index": 36, "sub_index": 0}, "segment-4bd15c54-67b9-4135-9be4-e8c3840e7c79": {"type": "main", "title": "Curvature correction capability for certain turbulence models:", "index": 37}, "segment-7878ad86-8168-4037-b04c-27c69af6120b": {"type": "main", "title": "Dacles-<PERSON>i Rotation Correction for SA Model:", "index": 38}, "segment-00e46f8e-7035-4293-9da2-19340db1d2ba": {"type": "main", "title": "Keeping track of CL and CD on different bodies:", "index": 39}, "segment-2116c682-a17e-42a8-84ff-fe73d093c545": {"type": "main", "title": "2D and coarse movie output:", "index": 40}, "segment-16fe7cef-4363-4a53-8a92-83cc0e529498": {"type": "main", "title": "2,2,2", "index": 41}, "segment-8844f50a-96af-4303-b885-89de362806db": {"type": "main", "title": "Variable CFLTAU:", "index": 42}, "segment-47c70e53-7395-44fc-a5b9-2e0f424b9a4e": {"type": "main", "title": "Running-average Q-file:", "index": 43}, "segment-7c3a1db1-7a5f-442d-acca-69b89ff170d4": {"type": "main", "title": "More advanced running-average files:", "index": 44}, "segment-edf123a4-f4c6-4033-8b1c-693a1aad115e": {"type": "sub", "title": "The primitive variables and the square of the primitive variables are iteration-averaged at the grid points and written to", "parent_index": 44, "sub_index": 0}, "segment-05b66230-db7b-4aae-8ff4-9c5be8aa9d5a": {"type": "main", "title": "Maintaining ambient turbulence levels without decay:", "index": 45}, "segment-db4da8a4-90bd-430e-9a6d-521cfa690f51": {"type": "sub", "title": "The following relationships are useful for determining how to set the ambient levels for two-equation models: The turbulence intensity (in percent) \\( = \\mathrm{{Tu}} = \\) 100*sqrt(2/3*(k/Uinf**2)). Thus, for example, for Tu=0.1 percent, (k/Uinf**2) = \\( {1.5} \\times  {10}^{* * }\\left( {-6}\\right) \\) . Furthermore,mu_t/mu =", "parent_index": 45, "sub_index": 0}, "segment-d1e82f07-595e-46ee-a169-c4047f4c76e8": {"type": "main", "title": "New unsteady aeroelasticity features:", "index": 46}, "segment-966998ba-cf2d-4030-a878-e1008d8e9e8d": {"type": "main", "title": "Nonlinear Quadratic Constitutive Relation for use with Linear Models:", "index": 47}, "segment-2825eac4-bd2a-4b1c-8a29-236f961c27d5": {"type": "sub", "title": "EXPERIMENTAL: Starting after Aug 2019, a new keyword i_tauijs can be invoked that adds turbulence to the mean flow equations via tauijs, rather than via eddy viscosity (plus nonlinear terms, if any). Like the eddy viscosity method, the 2/3*rho*k term is still ignored in the Boussinesq relation. Also, note that this i_tauijs=1 method is not done accurately at boundaries for multiblock cases; it currently uses one-sided differences. One would need ux derivatives from across boundaries to be strictly the same as for the case of no block boundaries. Therefore, one should currently NOT use i_tauijs=1 for multiblock cases.", "parent_index": 47, "sub_index": 0}, "segment-2bdb1583-c055-4a16-87f8-93f230733f1c": {"type": "main", "title": "3D Axisymmetric 2-plane cases with singular axis:", "index": 48}, "segment-7f627594-26a5-4756-aab3-140eeaa7b063": {"type": "sub", "title": "Note: keyword ifulIns=1 can exhibit problems converging for 3D axisymmetric 2-plane cases with a singular axis. The cure for this issue has not yet been found.", "parent_index": 48, "sub_index": 0}, "segment-00226f45-c108-4ec2-a529-2f1223333cde": {"type": "main", "title": "Three- and four-equation transition models:", "index": 49}, "segment-151bb127-816b-41e9-bc7b-cc971d1830bc": {"type": "main", "title": "3-eqn:", "index": 50}, "segment-6eb58c9f-60a4-4d30-a5e7-3d12bf41f3b9": {"type": "sub", "title": "JO: GRID SEGMENT BCTYPE ISTA IEND KSTA", "parent_index": 50, "sub_index": 0}, "segment-edf71fcb-e8bc-4a92-911f-36df119d1a40": {"type": "main", "title": "Large Eddy Simulation (LES) capability:", "index": 51}, "segment-a28f0dd3-d88d-4d62-92d5-c3f4490b4b1f": {"type": "main", "title": "Method of Manufactured Solution for SA model:", "index": 52}, "segment-c27c1613-7107-40f5-bafc-77f67e1ec254": {"type": "sub", "title": "KEND ISVA1 ISVA2", "parent_index": 52, "sub_index": 0}, "segment-80af2bbb-5fba-4952-93bf-0798a4e378d7": {"type": "sub", "title": "KEND ISVA1 ISVA2", "parent_index": 52, "sub_index": 1}, "segment-f65c81a2-559e-4740-8dfb-044fdeef1aad": {"type": "sub", "title": "KSTART KEND KINC", "parent_index": 52, "sub_index": 2}, "segment-011817ba-a92f-4bab-9e46-a2a134606d84": {"type": "main", "title": "Notes:", "index": 53}, "segment-c80dfaad-7ead-40c9-b41f-c41723d1c185": {"type": "main", "title": "Ad-Hoc Separation Fix:", "index": 54}, "segment-93abfefe-1762-416d-8898-736fde2c8981": {"type": "main", "title": "New \"sweeping jet\" BC 2026:", "index": 55}, "segment-20d0896f-1fea-4bae-ada6-6f64c0672bba": {"type": "main", "title": "Stress-Omega Full Reynolds Stress Models:", "index": 56}, "segment-43b62ff4-5261-4144-a42a-df69b2620555": {"type": "sub", "title": "Acknowledgment: <PERSON><PERSON> of Corvid Technologies, Mooresville, NC implemented this model as part of NASA NRA NNX11AIAI56A, through <PERSON><PERSON> of N. C. State University.", "parent_index": 56, "sub_index": 0}, "segment-d4a852bb-3d79-4b0e-92e7-4e57d894edc4": {"type": "main", "title": "SUMMARY OF INPUT FILE DIFFERENCES FROM EARLIER VERSIONS", "index": 57}, "segment-e456f97d-d401-401e-bce0-2470bc7c9e69": {"type": "main", "title": "LIMITATIONS", "index": 58}, "segment-6d51cd22-9ba6-4582-b3a8-4d07306558c2": {"type": "main", "title": "KNOWN PROBLEMS", "index": 59}, "segment-ec233bde-495b-4fc0-bbee-6ef41d859334": {"type": "main", "title": "Privacy Act Statement", "index": 60}}