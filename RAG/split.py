import json
import json
import json
import re
from typing import List, Dict, Any
from dataclasses import dataclass
import uuid
from pymilvus import (
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
    MilvusClient,
)
import numpy as np
from tqdm import tqdm
import os
from pymilvus.model.hybrid import BGEM3EmbeddingFunction

@dataclass
class Section:
    title: str
    content: str
    segments: List[Dict[str, str]] = None
    keywords: List[str] = None
    summary: str = None

class LLMRequestGenerator:
    def __init__(self, model: str = "deepseek-v3"):
        """初始化LLM请求生成器"""
        self.model = model
        self.requests = []
        
    def generate_segment_request(self, title: str, text: str, request_id: str = None) -> str:
        """生成分段请求"""
        if request_id is None:
            request_id = f"segment-{uuid.uuid4()}"
            
        prompt = f"""
        以下是CFL3D手册的一个章节内容，标题为"{title}"。
        请将这段文本分割成2-5个语义完整、连贯的段落。每个段落应该是一个完整的知识点或概念。
        如果文本很短，可以只分成1个段落。

        章节内容：
        {text}

        请以JSON格式返回，包含以下字段：
        - segments: 语义段落列表（每个段落包含text字段）
        """
        
        messages = [
            {"role": "system", "content": "你是一个专业的科技文档分析助手，擅长分析CFD领域的技术文档。"},
            {"role": "user", "content": prompt}
        ]
        
        request_data = {
            "custom_id": request_id,
            "method": "POST",
            "url": "/v1/chat/completions",
            "body": {
                "model": self.model,
                "messages": messages,
                "temperature": 0.3,
                "response_format": {"type": "json_object"}
            }
        }
        
        self.requests.append(request_data)
        return request_id
        
    def generate_summary_request(self, title: str, segments_text: str, request_id: str = None) -> str:
        """生成摘要和关键词请求"""
        if request_id is None:
            request_id = f"summary-{uuid.uuid4()}"
            
        prompt = f"""
        以下是CFL3D手册中"{title}"的一个段落内容：

        {segments_text}

        请针对这个段落：
        1. 生成一个简洁的总结摘要（30字以内）
        2. 提取1-5个该段落中的关键技术词汇
        
        请使用英文回答

        请以JSON格式返回，包含以下字段：
        - summary: 摘要
        - keywords: 关键词列表
        """
        
        messages = [
            {"role": "system", "content": "你是一个专业的科技文档分析助手，擅长分析CFD领域的技术文档。"},
            {"role": "user", "content": prompt}
        ]
        
        request_data = {
            "custom_id": request_id,
            "method": "POST",
            "url": "/v1/chat/completions",
            "body": {
                "model": self.model,
                "messages": messages,
                "temperature": 0.3,
                "response_format": {"type": "json_object"}
            }
        }
        
        self.requests.append(request_data)
        return request_id
    
    def save_requests(self, filename: str):
        """保存请求到JSONL文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for request in self.requests:
                f.write(json.dumps(request, ensure_ascii=False) + '\n')
        print(f"已保存 {len(self.requests)} 个请求到 {filename}")

class CFLManualProcessor:
    def __init__(self):
        self.sections: List[Section] = []
        self.section_map = {}  # 用于映射请求ID到章节
        self.llm_request_generator = LLMRequestGenerator()
    
    def extract_sections(self, text: str) -> None:
        """使用正则表达式提取所有标题和内容，不考虑层级关系"""
        print("提取所有章节，忽略层级结构...")
        
        # 匹配Markdown标题的正则表达式 (任意级别标题)
        header_pattern = r'^(#{1,6})\s+(.+?)$'
        
        # 按行分割文本
        lines = text.split('\n')
        headers = []
        current_position = 0
        
        # 找出所有标题行及其位置
        for i, line in enumerate(lines):
            match = re.match(header_pattern, line)
            if match:
                title = match.group(2).strip()
                headers.append((title, i, current_position))
            current_position += len(line) + 1  # +1 是换行符
        
        print(f"找到了 {len(headers)} 个Markdown标题")
        
        # 提取每个标题下的内容
        for i, (title, line_idx, _) in enumerate(headers):
            # 计算当前标题的内容范围
            if i < len(headers) - 1:
                next_header_line = headers[i + 1][1]
                content_lines = lines[line_idx + 1:next_header_line]
            else:
                content_lines = lines[line_idx + 1:]
            
            content = '\n'.join(content_lines)
            
            # 创建新章节（平级结构）
            section_obj = Section(
                title=title,
                content=content
            )
            
            # 生成请求
            request_id = self.llm_request_generator.generate_segment_request(title, content)
            self.section_map[request_id] = section_obj
            self.sections.append(section_obj)
            
        print(f"总共创建了 {len(self.sections)} 个章节")
    
    def _get_heading_level(self, metadata):
        """从元数据中获取标题级别"""
        for key, value in metadata.items():
            if key.startswith('heading') and value:
                return int(key.replace('heading', ''))
        return 0
    
    def _extract_title(self, metadata):
        """从元数据中提取标题文本"""
        for level in range(1, 5):  # 支持heading1到heading4
            key = f'heading{level}'
            if key in metadata and metadata[key]:
                return metadata[key]
        return ""
    
    def _process_subsections(self, parent_section: Section) -> None:
        """处理子章节并生成LLM请求"""
        # 使用正则表达式匹配子标题
        subsection_pattern = r'\n(?=[A-Z][\w\s-]+:)'
        subsections = re.split(subsection_pattern, parent_section.content)
        
        if len(subsections) > 1:
            # 设置父章节的内容为第一部分
            parent_section.content = subsections[0]
            
            # 处理剩余的子章节
            for i in range(1, len(subsections)):
                subsection = subsections[i]
                
                # 提取子章节标题和内容
                sub_lines = subsection.split('\n', 1)
                if len(sub_lines) == 2:
                    sub_title, sub_content = sub_lines
                    
                    # 创建子章节对象
                    sub_section = Section(
                        title=sub_title.strip(':'),
                        content=sub_content.strip(),
                        segments=None,
                        keywords=None,
                        summary=None
                    )
                    
                    # 生成分段请求
                    request_id = self.llm_request_generator.generate_segment_request(sub_title.strip(':'), sub_content.strip())
                    self.section_map[request_id] = (sub_section, "sub")
                    
                    parent_section.segments = sub_section.segments
                    parent_section.keywords = sub_section.keywords
                    parent_section.summary = sub_section.summary
        
    def save_section_map(self, filename: str):
        """保存请求ID到章节的映射，包含完整层级路径"""
        mapping = {}
        for request_id, section in self.section_map.items():
            # 这里section是Section对象，但在处理时期望是字典
            # 需要转换成字典
            mapping[request_id] = {
                "title": section.title,
                "content": section.content
            }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2)
    
    def export_to_json(self, filename: str):
        """导出章节结构到JSON文件，包含完整层级信息"""
        sections_data = [{"title": section.title, "content": section.content, "segments": section.segments, "keywords": section.keywords, "summary": section.summary} for section in self.sections]
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(sections_data, f, ensure_ascii=False, indent=2)

class ResultProcessor:
    def __init__(self, section_map_file: str, section_structure_file: str, result_file: str):
        """初始化结果处理器"""
        # 加载章节映射
        with open(section_map_file, 'r', encoding='utf-8') as f:
            self.section_map = json.load(f)
            
        # 加载章节结构（现在是扁平结构）
        with open(section_structure_file, 'r', encoding='utf-8') as f:
            self.sections = json.load(f)
            
        # 加载LLM处理结果
        self.results = {}
        with open(result_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    result = json.loads(line)
                    self.results[result["custom_id"]] = result
    
    def _extract_llm_content(self, result):
        """从LLM结果中提取内容"""
        try:
            # 适应不同的响应格式
            if "choices" in result and result["choices"]:
                content = result["choices"][0]["message"]["content"]
                return content
            elif "response" in result and "body" in result["response"]:
                response_body = result["response"]["body"]
                if "choices" in response_body and response_body["choices"]:
                    content = response_body["choices"][0]["message"]["content"]
                    return content
            return None
        except Exception as e:
            print(f"提取LLM内容时出错: {str(e)}")
            return None

    def process_segments(self):
        """处理分段结果并生成摘要请求"""
        llm_request_generator = LLMRequestGenerator()
        segment_summary_map = {}  # 映射分段请求ID到摘要请求ID列表
        
        print(f"开始处理 {len(self.results)} 个分段结果")
        print(f"section_map包含 {len(self.section_map)} 个条目")
        print(f"sections包含 {len(self.sections)} 个条目")
        
        # 直接打印几个关键数据结构的示例，帮助调试
        if self.results:
            first_result_id = list(self.results.keys())[0]
            print(f"结果示例 {first_result_id}: {self.results[first_result_id].keys()}")
        
        if self.section_map:
            first_section_id = list(self.section_map.keys())[0]
            print(f"章节映射示例 {first_section_id}: {self.section_map[first_section_id]}")
        
        if self.sections:
            print(f"章节结构示例: {self.sections[0].keys() if isinstance(self.sections[0], dict) else '非字典类型'}")
        
        # 处理每个结果
        for request_id, result in self.results.items():
            if request_id not in self.section_map:
                print(f"警告: 未找到请求ID {request_id} 的章节信息")
                continue
            
            # 获取章节信息
            section_info = self.section_map[request_id]
            title = section_info["title"] if isinstance(section_info, dict) else section_info.title
            print(f"处理章节: {title}")
            
            try:
                # 提取LLM响应内容
                content = self._extract_llm_content(result)
                if not content:
                    print(f"警告: 无法提取请求ID {request_id} 的响应内容")
                    continue
                
                # 打印部分内容帮助调试
                print(f"响应内容示例: {content[:100]}...")
                
                # 解析JSON内容
                try:
                    content_json = json.loads(content)
                    segments = content_json.get("segments", [])
                except json.JSONDecodeError as e:
                    print(f"解析JSON时出错: {e}")
                    print(f"响应内容: {content}")
                    continue
                
                if not segments:
                    print(f"警告: 请求ID {request_id} 的响应中没有段落")
                    continue
                    
                print(f"找到 {len(segments)} 个段落")
                
                # 更新章节数据
                section_found = False
                for i, section in enumerate(self.sections):
                    if isinstance(section, dict) and section.get("title") == title:
                        self.sections[i]["segments"] = segments
                        section_found = True
                        break
                
                if not section_found:
                    print(f"警告: 在sections中未找到标题为 '{title}' 的章节")
                    continue
                
                # 为每个段落生成摘要请求
                segment_summary_map[request_id] = []
                for i, segment in enumerate(segments):
                    segment_text = segment["text"]
                    summary_request_id = llm_request_generator.generate_summary_request(
                        f"{title} - 段落{i+1}",
                        segment_text
                    )
                    segment_summary_map[request_id].append({
                        "summary_id": summary_request_id,
                        "segment_index": i
                    })
                    print(f"为段落 {i+1} 生成摘要请求: {summary_request_id}")
                
            except Exception as e:
                print(f"处理分段结果时出错 {request_id}: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        # 保存摘要请求
        llm_request_generator.save_requests("summary_requests.jsonl")
        
        # 保存分段-摘要映射
        with open("segment_summary_map.json", 'w', encoding='utf-8') as f:
            json.dump(segment_summary_map, f, ensure_ascii=False, indent=2)
        
        # 保存更新后的章节结构
        with open("sections_with_segments.json", 'w', encoding='utf-8') as f:
            json.dump(self.sections, f, ensure_ascii=False, indent=2)
        
        print(f"共处理了 {len(segment_summary_map)} 个章节，生成了 {len(llm_request_generator.requests)} 个摘要请求")
    
    def process_summaries(self, segment_summary_map_file: str, sections_file: str, summary_results_file: str):
        """处理摘要结果并支持多层级章节结构"""
        # 加载必要文件
        with open(segment_summary_map_file, 'r', encoding='utf-8') as f:
            segment_summary_map = json.load(f)
        
        # 加载带有段落的章节数据
        with open(sections_file, 'r', encoding='utf-8') as f:
            sections_with_segments = json.load(f)
        
        # 创建标题到章节的映射，便于查找
        title_to_section = {section["title"]: section for section in sections_with_segments if isinstance(section, dict)}
        
        # 加载摘要结果
        with open(summary_results_file, 'r', encoding='utf-8') as f:
            summary_results = {json.loads(line)["custom_id"]: json.loads(line) for line in f if line.strip()}
        
        # 记录已处理的段落，避免重复
        processed_segments = set()
        successful_updates = 0
        failed_updates = 0
        
        # 处理摘要结果
        for segment_id, summary_info_list in segment_summary_map.items():
            if segment_id not in self.section_map:
                print(f"警告: 未找到分段ID {segment_id} 的章节信息")
                continue
            
            # 获取章节信息 - 使用字典访问方式
            section_info = self.section_map[segment_id]
            title = section_info["title"]
            print(f"处理章节: {title}")
            
            # 从已处理的段落章节中查找完整信息
            if title not in title_to_section:
                print(f"警告: 在处理后的章节中未找到标题为 '{title}' 的章节")
                continue
            
            # 使用带有段落的章节数据
            section_with_segments = title_to_section[title]
            
            if "segments" not in section_with_segments or not section_with_segments["segments"]:
                print(f"警告: 章节 '{title}' 没有段落数据")
                continue
            
            print(f"找到章节 '{title}' 的 {len(section_with_segments['segments'])} 个段落")
            
            # 处理所有摘要信息
            for summary_info in summary_info_list:
                summary_id = summary_info["summary_id"]
                segment_index = summary_info["segment_index"]
                
                # 检查是否超出范围
                if segment_index >= len(section_with_segments["segments"]):
                    print(f"警告: 段落索引 {segment_index} 超出章节 '{title}' 的段落数量 {len(section_with_segments['segments'])}")
                    continue
                
                # 跟踪已处理段落
                segment_key = (title, segment_index)
                if segment_key in processed_segments:
                    print(f"跳过已处理的段落: {segment_key}")
                    continue
                processed_segments.add(segment_key)
                
                # 获取摘要结果
                if summary_id not in summary_results:
                    print(f"警告: 未找到摘要ID {summary_id} 的结果")
                    continue
                
                # 处理摘要数据
                try:
                    result = summary_results[summary_id]
                    content = None
                    
                    if "choices" in result and result["choices"]:
                        content = result["choices"][0]["message"]["content"]
                    elif "response" in result and "body" in result["response"]:
                        response_body = result["response"]["body"]
                        if "choices" in response_body and response_body["choices"]:
                            content = response_body["choices"][0]["message"]["content"]
                    
                    if not content:
                        print(f"警告: 摘要ID {summary_id} 的响应格式不支持")
                        continue
                    
                    content_json = json.loads(content)
                    summary = content_json.get("summary", "")
                    keywords = content_json.get("keywords", [])
                    
                    # 更新章节的摘要和关键词
                    if "summaries" not in section_with_segments:
                        section_with_segments["summaries"] = []
                    if "keywords_list" not in section_with_segments:
                        section_with_segments["keywords_list"] = []
                    
                    # 确保列表足够长
                    while len(section_with_segments["summaries"]) <= segment_index:
                        section_with_segments["summaries"].append("")
                    while len(section_with_segments["keywords_list"]) <= segment_index:
                        section_with_segments["keywords_list"].append([])
                    
                    # 更新摘要和关键词
                    section_with_segments["summaries"][segment_index] = summary
                    section_with_segments["keywords_list"][segment_index] = keywords
                    section_with_segments["segments"][segment_index]["summary"] = summary
                    section_with_segments["segments"][segment_index]["keywords"] = keywords
                    
                    successful_updates += 1
                    print(f"成功更新章节 '{title}' 的第 {segment_index+1} 个段落")
                    
                except Exception as e:
                    failed_updates += 1
                    print(f"处理摘要时出错 {summary_id}: {str(e)}")
                    import traceback
                    traceback.print_exc()

        print(f"\n摘要处理完成: 成功更新 {successful_updates} 个段落，失败 {failed_updates} 个段落")
        
        # 保存结果
        with open("processed_manual.json", 'w', encoding='utf-8') as f:
            json.dump(sections_with_segments, f, ensure_ascii=False, indent=2)
        
        with open("processed_manual.jsonl", 'w', encoding='utf-8') as f:
            for section in sections_with_segments:
                f.write(json.dumps(section, ensure_ascii=False) + '\n')

class MilvusManager:
    def __init__(self, collection_name: str = "cfl3d_manual", create_collection: bool = False):
        self.collection_name = collection_name
        self.dim = 1024
        # 初始化嵌入模型
        self.model = BGEM3EmbeddingFunction(
            model_name='/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181',
            device='cuda',
            use_fp16=False
        )

        # 连接到Milvus Lite
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "CFL3D.db")
        self.client = MilvusClient(db_path)

        # 创建collection如果指定了create_collection参数
        if create_collection:
            # 检查集合是否存在，如果存在则删除
            if self.client.has_collection(self.collection_name):
                self.client.drop_collection(self.collection_name)
            self.create_collection()
        # 如果不需要创建但集合不存在，可以打印警告或报错
        # elif not self.client.has_collection(self.collection_name):
        #     print(f"[WARNING] Collection '{self.collection_name}' not found in '{db_path}' and create_collection=False.")
        
    def create_collection(self):
        """创建Milvus Lite collection，支持摘要向量"""
        # 检查集合是否存在，如果存在则删除
        if self.client.has_collection(self.collection_name):
            self.client.drop_collection(self.collection_name)

        # 1. 创建schema
        schema = self.client.create_schema(
            auto_id=True,  # 使用自动生成的ID
            enable_dynamic_field=True  # 允许动态字段
        )

        # 2. 添加字段定义
        schema.add_field(
            field_name="id",
            datatype=DataType.INT64,
            is_primary=True
        )
        schema.add_field(
            field_name="vector",
            datatype=DataType.FLOAT_VECTOR,
            dim=self.dim
        )
        schema.add_field(
            field_name="summary_vector",
            datatype=DataType.FLOAT_VECTOR,
            dim=self.dim
        )

        # 3. 创建集合
        self.client.create_collection(
            collection_name=self.collection_name,
            schema=schema
        )
        
        # 4. 准备索引参数
        index_params = MilvusClient.prepare_index_params()
        
        # 5. 为文本向量添加索引
        index_params.add_index(
            field_name="vector",
            metric_type="COSINE",
            index_type="FLAT",  # Milvus Lite只支持FLAT索引
            index_name="vector_index",
            params={}
        )
        
        # 6. 为摘要向量添加索引
        index_params.add_index(
            field_name="summary_vector",
            metric_type="COSINE",
            index_type="FLAT",  # Milvus Lite只支持FLAT索引
            index_name="summary_vector_index",
            params={}
        )
        
        # 7. 创建索引
        self.client.create_index(
            collection_name=self.collection_name,
            index_params=index_params
        )
        
        # 8. 测试索引是否创建成功
        indexes = self.client.list_indexes(
            collection_name=self.collection_name
        )
        print(f"创建的索引列表: {indexes}")
        print(f"集合 {self.collection_name} 创建完成，包含双向量字段")

    def create_chunks_from_processed(self, processed_file: str) -> List[Dict]:
        """从处理后的文件创建向量数据库所需的文本块"""
        with open(processed_file, 'r', encoding='utf-8') as f:
            sections = json.load(f)
        
        chunks = []
        for section in sections:
            if "segments" in section and section["segments"]:
                for i, segment in enumerate(section["segments"]):
                    chunks.append({
                        "text": segment["text"],
                        "metadata": {
                            "title": section["title"],
                            "keywords": segment.get("keywords", []),
                            "summary": segment.get("summary", ""),
                            "segment_id": i
                        }
                    })
        
        return chunks
    
    def insert_chunks(self, chunks: List[Dict]):
        """插入文本块到Milvus Lite，包括摘要向量"""
        # 准备用于插入的数据
        data_to_insert = []
        
        for chunk in tqdm(chunks, desc="处理数据块"):
            try:
                # 生成文本的嵌入向量
                text = chunk["text"]
                embeddings_dict = self.model.encode_documents([text])
                
                # 确保向量格式正确 - 显式转换为列表
                embedding = embeddings_dict["dense"][0].tolist() if hasattr(embeddings_dict["dense"][0], 'tolist') else embeddings_dict["dense"][0]
                
                # 获取摘要
                summary = chunk["metadata"]["summary"]
                
                # 如果摘要为空，使用文本的前100个字符
                if not summary:
                    summary = text[:100] + "..." if len(text) > 100 else text
                
                # 为摘要生成向量
                summary_embeddings_dict = self.model.encode_documents([summary])
                summary_embedding = summary_embeddings_dict["dense"][0].tolist() if hasattr(summary_embeddings_dict["dense"][0], 'tolist') else summary_embeddings_dict["dense"][0]
                
                # 构建要插入的数据记录
                record = {
                    "vector": embedding,               # 文本向量
                    "summary_vector": summary_embedding,  # 摘要向量
                    "text": text,
                    "title": chunk["metadata"]["title"],
                    "keywords": chunk["metadata"]["keywords"],
                    "summary": summary,
                    "segment_id": chunk["metadata"]["segment_id"]
                }
                data_to_insert.append(record)
            except Exception as e:
                print(f"处理文本块时出错: {e}")
        
        # 批量插入数据
        try:
            if not data_to_insert:
                print("警告: 没有可插入的数据")
                return None
            
            print(f"准备插入 {len(data_to_insert)} 条记录")
            print(f"示例记录: {data_to_insert[0].keys()}")
            
            result = self.client.insert(
                collection_name=self.collection_name,
                data=data_to_insert
            )
            
            print(f"插入结果: {result}")
            
            # 验证插入结果
            self.verify_data_insertion()
            
            return result
        except Exception as e:
            print(f"插入数据时出错: {e}")
            return None
        
    def vector_search(self, query: str, keywords: List[str] = None, field: str = "vector", limit: int = 5):
        """统一的向量搜索方法，支持文本向量或摘要向量
        
        参数:
            query: 查询文本
            keywords: 可选的关键词过滤列表
            field: 要搜索的向量字段，可选 "vector"(文本向量) 或 "summary_vector"(摘要向量)
            limit: 返回结果数量限制
        """
        # 构建过滤条件
        filter_conditions = []
        
        # 关键词过滤条件
        if keywords and len(keywords) > 0:
            keyword_conditions = []
            for keyword in keywords:
                # 1. 在keywords字段中查找 - 主要匹配方式
                keyword_conditions.append(f"keywords like '%{keyword}%'")
                # 2. 在text字段中查找 - 备选匹配方式
                keyword_conditions.append(f"text like '%{keyword}%'")
            
            if keyword_conditions:
                filter_conditions.append("(" + " or ".join(keyword_conditions) + ")")
        
        # 合并所有过滤条件
        filter_str = " and ".join(filter_conditions) if filter_conditions else ""
        
        # 打印生成的过滤条件，便于调试
        if filter_str:
            print(f"生成的过滤条件: {filter_str}")

        # 生成查询向量
        query_vectors = self.model.encode_queries([query])
        
        # 确保向量格式正确
        dense_vectors = query_vectors["dense"]
        if isinstance(dense_vectors, np.ndarray):
            query_data = [dense_vectors.tolist()]
        else:
            query_data = [dense_vectors[0].tolist()]
        
        # 使用向量相似度搜索
        results = self.client.search(
            collection_name=self.collection_name,
            data=query_data,
            anns_field=field,
            search_params={"metric_type": "COSINE", "params": {}},
            limit=limit,
            output_fields=["id", "text", "title", "keywords", "summary", "segment_id"],
            filter=filter_str if filter_str else None
        )
        
        return results
    
    def combined_search(self, query: str, keywords: List[str] = None, limit: int = 5, text_weight: float = 0.6):
        """结合文本向量和摘要向量的综合检索方式
        
        参数:
            query: 查询文本
            keywords: 可选的关键词过滤列表
            limit: 返回结果数量限制
            text_weight: 文本向量的权重比例(0-1)，摘要向量权重为(1-text_weight)
        """
        # 分别执行文本和摘要向量搜索
        text_results = self.vector_search(query, keywords, field="vector", limit=int(limit*2))
        summary_results = self.vector_search(query, keywords, field="summary_vector", limit=int(limit*2))
        #print(len(text_results[0]))
        #print(len(summary_results[0]))
        # 合并去重并加权排序
        combined_results = []
        seen_ids = set()
        
        # 处理文本搜索结果 - 正确处理嵌套结构
        if text_results and len(text_results) > 0:
            # 获取内层列表中的所有结果
            for result in text_results[0]:  # 只有一个外层元素
                if "entity" in result:
                    entity = result["entity"]
                    entity_id = entity.get("id")
                    
                    if entity_id not in seen_ids:
                        seen_ids.add(entity_id)
                        score = result.get("distance", 0) * text_weight  # 文本相似度权重
                        combined_results.append({
                            "entity": entity,
                            "distance": score,
                            "original_distance": result.get("distance", 0),
                            "text_score": score,
                            "summary_score": 0,
                            "source": "text"
                        })
        
        # 处理摘要搜索结果 - 同样正确处理嵌套结构
        summary_weight = 1 - text_weight
        if summary_results and len(summary_results) > 0:
            # 获取内层列表中的所有结果
            for result in summary_results[0]:  # 只有一个外层元素
                if "entity" in result:
                    entity = result["entity"]
                    entity_id = entity.get("id")
                    
                    if entity_id not in seen_ids:
                        seen_ids.add(entity_id)
                        score = result.get("distance", 0) * summary_weight  # 摘要相似度权重
                        combined_results.append({
                            "entity": entity,
                            "distance": score,
                            "original_distance": result.get("distance", 0),
                            "text_score": 0,
                            "summary_score": score,
                            "source": "summary"
                        })
                    else:
                        # 如果已存在，找到对应结果并增加摘要相似度分数
                        for item in combined_results:
                            if item["entity"].get("id") == entity_id:
                                summary_score = result.get("distance", 0) * summary_weight
                                item["summary_score"] = summary_score
                                item["distance"] += summary_score
                                item["source"] = "both"
                                break
        
        # 按总分排序
        combined_results.sort(key=lambda x: x["distance"], reverse=True)
        print(f"合并后的结果数量: {len(combined_results)}")
        print(combined_results)
        # 返回前limit个结果
        return [combined_results[:limit]]

    def describe_collection(self):     
        res = self.client.describe_collection(
            collection_name=self.collection_name
        )
        return res

    def verify_data_insertion(self):
        """验证数据是否成功插入"""
        # 查询集合中的记录数
        stats = self.client.query(
            collection_name=self.collection_name,
            filter="",
            output_fields=["count(*)"]
        )
        print(f"集合中的记录数: {stats}")
        
        # 随机获取几条记录
        sample_data = self.client.query(
            collection_name=self.collection_name,
            filter="",
            limit=3,
            output_fields=["id", "text", "title"]
        )
        print(f"样本数据: {sample_data}")
        return stats, sample_data

def step1_generate_requests(text_file: str = None):
    """步骤1: 生成文本分割请求"""
    print("步骤1: 生成文本分割请求")
    
    # 读取手册文本
    with open(text_file, 'r', encoding='utf-8') as f:
        manual_text = f.read()
    
    # 处理手册
    processor = CFLManualProcessor()
    processor.extract_sections(manual_text)
    
    # 保存请求
    processor.llm_request_generator.save_requests("segment_requests.jsonl")
    
    # 保存章节映射和结构
    processor.save_section_map("section_map.json")
    processor.export_to_json("section_structure.json")
    
    print("分割请求生成完成，请提交 segment_requests.jsonl 文件进行批量推理")

def step2_process_segments():
    """步骤2: 处理分段结果并生成摘要请求"""
    print("步骤2: 处理分段结果并生成摘要请求")
    
    if not os.path.exists("segment_results.jsonl"):
        print("错误: 未找到分段结果文件 segment_results.jsonl")
        return
    
    processor = ResultProcessor(
        "section_map.json",
        "section_structure.json",
        "segment_results.jsonl"
    )
    processor.process_segments()
    
    print("分段处理完成，请提交 summary_requests.jsonl 文件进行批量推理")

def step3_process_summaries():
    """步骤3: 处理摘要结果并准备向量数据库"""
    print("步骤3: 处理摘要结果并准备向量数据库")
    
    if not os.path.exists("summary_results.jsonl"):
        print("错误: 未找到摘要结果文件 summary_results.jsonl")
        return
    
    processor = ResultProcessor(
        "section_map.json",
        "section_structure.json",
        "segment_results.jsonl"
    )
    processor.process_summaries(
        "segment_summary_map.json",
        "sections_with_segments.json",
        "summary_results.jsonl"
    )
    
    print("摘要处理完成，已生成 processed_manual.json 和 processed_manual.jsonl")

def step4_build_vector_db(create_collection: bool = True):
    """步骤4: 构建向量数据库"""
    print("步骤4: 构建向量数据库")
    
    if not os.path.exists("processed_manual.json"):
        print("错误: 未找到处理后的手册文件 processed_manual.json")
        return
    
    # 初始化Milvus管理器并显式创建集合
    milvus_manager = MilvusManager(create_collection=create_collection)
    
    # 创建文本块
    chunks = milvus_manager.create_chunks_from_processed("processed_manual.json")
    print(f"创建了 {len(chunks)} 个文本块")
    
    # 插入数据
    milvus_manager.insert_chunks(chunks)
    print("数据已插入Milvus")
    print("向量数据库构建完成")

def step5_test_search(
    search_type: str = "text", 
    query: str = "ivisc flag options", 
    keywords: List[str] = None, 
    limit: int = 5
):
    """步骤5: 测试搜索功能 - 简化版
    
    参数:
        search_type: 搜索类型，可选 "text", "summary", "combined"
        query: 搜索查询文本
        keywords: 关键词过滤列表
        limit: 返回结果数量
    """
    # 默认超参数设置
    text_weight = 0.4  # 综合搜索中文本向量的权重
    
    print(f"执行搜索: 类型={search_type}, 查询='{query}'")
    if keywords:
        print(f"关键词过滤: {keywords}")
    
    # 初始化Milvus管理器
    milvus_manager = MilvusManager(create_collection=False)
    
    # 执行相应的搜索
    if search_type == "text":
        results = milvus_manager.vector_search(query, keywords, field="vector", limit=limit)
    elif search_type == "summary":
        results = milvus_manager.vector_search(query, keywords, field="summary_vector", limit=limit)
    elif search_type == "combined":
        results = milvus_manager.combined_search(query, keywords, limit=limit, text_weight=text_weight)
    else:
        print(f"未知的搜索类型: {search_type}，使用默认文本搜索")
        results = milvus_manager.vector_search(query, keywords, limit=limit)
    

    if results and len(results) > 0:
        display_results = results[0]  # 提取第一个内层列表
    else:
        display_results = []
    
    # 显示结果
    print(f"\n找到 {len(display_results)} 条结果:")
    for i, result in enumerate(display_results):
        print(f"结果 {i+1}:")
        
        # 根据结果类型显示信息
        if isinstance(result, dict) and "entity" in result:
            entity = result["entity"]
            distance = result.get("distance", "未知")
            source = result.get("source", "文本")
            
            print(f"相似度: {distance:.4f}")
            print(f"来源: {source}")
            
            # 如果是综合搜索，显示详细分数
            if search_type == "combined":
                text_score = result.get('text_score', 0)
                summary_score = result.get('summary_score', 0)
                original_distance = result.get('original_distance', 0)
                
                # 显示原始分数和加权后的分数
                if source == "text":
                    print(f"文本原始分数: {original_distance:.4f}")
                    print(f"文本加权分数(×{text_weight:.2f}): {text_score:.4f}")
                elif source == "summary":
                    print(f"摘要原始分数: {original_distance:.4f}")
                    print(f"摘要加权分数(×{1-text_weight:.2f}): {summary_score:.4f}")
                else:  # source == "both"
                    print(f"文本加权分数(×{text_weight:.2f}): {text_score:.4f}")
                    print(f"摘要加权分数(×{1-text_weight:.2f}): {summary_score:.4f}")
                    print(f"综合分数: {distance:.4f}")
            
            print(f"标题: {entity.get('title', '未知')}")
            print(f"摘要: {entity.get('summary', '未知')}")
            print(f"关键词: {entity.get('keywords', [])}")
            print(f"文本片段: {entity.get('text', '')[:100]}...")
        else:
            # 直接打印结果 - 处理其他可能的结果格式
            try:
                print(f"标题: {result.get('title', result.get('entity', {}).get('title', '未知'))}")
                print(f"摘要: {result.get('summary', result.get('entity', {}).get('summary', '未知'))}")
                print(f"关键词: {result.get('keywords', result.get('entity', {}).get('keywords', []))}")
                text = result.get('text', result.get('entity', {}).get('text', ''))
                print(f"文本片段: {text[:100]}...")
            except:
                print(f"无法解析的结果格式: {result}")
        
        print("-" * 50)
    
    return display_results  # 返回已处理的结果列表

if __name__ == "__main__":
    # 根据需要执行相应步骤
    print("CFL3D手册处理流程")
    print("1. 生成文本分割请求")
    print("2. 处理分段结果并生成摘要请求")
    print("3. 处理摘要结果并准备向量数据库")
    print("4. 构建向量数据库")
    print("5. 测试搜索功能")
    
    step = input("请选择要执行的步骤 (1/2/3/4/5): ")
    
    if step == "1":
        step1_generate_requests(text_file='CFL3DManual.md')
    elif step == "2":
        step2_process_segments()
    elif step == "3":
        step3_process_summaries()
    elif step == "4":
        step4_build_vector_db(create_collection=False)
    elif step == "5":
        # 可以直接设置参数，而不是交互式输入
        search_type = "combined"  # 可选: "text", "summary", "combined"
        query = "ivisc"  # 查询文本
        #keywords = None
        keywords = ["ivisc"]  # 可选关键词列表，设为None则不使用关键词过滤
        limit = 5  # 返回结果数量限制
        
        # 直接调用搜索函数
        step5_test_search(
            search_type=search_type,
            query=query,
            keywords=keywords,
            limit=limit
        )
    else:
        print("无效的选择")