#!/bin/bash
#SBATCH --job-name=cfd_ppo       # 作业名称
#SBATCH --nodes=1                # 使用1个节点
#SBATCH --gres=gpu:8        # 每个节点分配4个GPU
#SBATCH --partition=gpu          # 指定gpu分区


# --- 设置执行目录 ---
# 重要：假设此脚本位于项目根目录下，并且你从项目根目录提交 sbatch run_cfd_ppo.sh
# 或者，取消下面的注释，并将其设置为项目的绝对路径
# PROJECT_DIR="/path/to/your/Agent-R1"
# cd $PROJECT_DIR || exit 1 # 如果更改目录失败则退出

echo "Current working directory: $(pwd)"
echo "Job submitted from: ${SLURM_SUBMIT_HOST}"
echo "Job ID: ${SLURM_JOB_ID}"
echo "GPUs allocated: ${CUDA_VISIBLE_DEVICES:-"Not Set (using all visible)"}" # SLURM通常会设置这个

# --- 环境设置 ---
# 如果anaconda模块不存在，可注释下面这行或加载正确的模块
# module load anaconda/2021.11
export HF_ENDPOINT=https://hf-mirror.com
export PYTHONUNBUFFERED=1
export OMP_NUM_THREADS=1
export DS_TRITON_DISABLE_AUTOTUNE=1
# --- Qwen3-4B 优化环境变量 ---
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # 优化 CUDA 内存分配
export TORCH_NCCL_ASYNC_ERROR_HANDLING=1  # 改善 NCCL 错误处理
export NUM_GPUS=8
export TP_SIZE=2  # Optimized for Qwen3-4B: 2 is better than 4 for 4B model on 8x40G A100

module unload intel/2022.1
module purge
module load cuda/12.4
module load nccl/2.21.5-1-gcc11.3.0-cuda12.4
module load intel/oneapi/2022.1
module load miniforge/24.1.2

eval "$(conda shell.bash hook)"
conda activate verl3
if [ $? -ne 0 ]; then
    echo "Error: Failed to activate conda environment 'verl3'"
    exit 1
fi
echo "Conda environment 'verl3' activated."
echo "Python path: $(which python)"
echo "PYTHONPATH: ${PYTHONPATH:-"Not set"}"

# --- 设置 cgrid 执行权限 ---
CGRID_PATH="/home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d/cgrid"
if [ -f "$CGRID_PATH" ]; then
    echo "Setting execute permission for cgrid at $CGRID_PATH"
    chmod +x "$CGRID_PATH"
    if [ $? -ne 0 ]; then
        echo "Error: Failed to set execute permission for cgrid."
        # 根据您的需求，可以选择在这里退出脚本 exit 1
    else
        echo "Execute permission set for cgrid."
    fi
else
    echo "Warning: cgrid executable not found at $CGRID_PATH. Cannot set permissions."
fi
# --- 权限设置结束 ---

echo "--- Dependency Versions ---"
echo "PyTorch Version:"
python -c "import torch; print(torch.__version__)"
echo "CUDA Version (PyTorch built with):"
python -c "import torch; print(torch.version.cuda)"
echo "NCCL Available:"
python -c "import torch; print(torch.distributed.is_nccl_available())"
echo "NCCL Version (PyTorch uses):"
python -c "import torch; print(torch.cuda.nccl.version() if torch.distributed.is_nccl_available() else 'N/A')"
echo "CUDA Driver Version (from nvidia-smi):"
nvidia-smi --query-gpu=driver_version --format=csv,noheader
echo "--- End Versions ---"

# --- Print Compute Node Diagnostics ---
echo "--- Diagnostics on compute node $(hostname) ---"
echo "User limits (nproc):"
ulimit -u 8192
echo "Memory usage:"
free -h
echo "Number of current processes for user:"
ps -u $(whoami) | wc -l
echo "--- End Diagnostics ---"

# echo "--- Minimal Ray GPU Test ---"
# python -c "
# import ray
# import os
# import time

# # Ensure NUM_GPUS is read correctly from the environment set just above
# num_gpus_env = int(os.environ.get('NUM_GPUS', 0))
# cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES', 'N/A')

# print(f'SLURM allocated CUDA_VISIBLE_DEVICES={cuda_visible}')
# print(f'Environment NUM_GPUS={num_gpus_env}')
# print(f'Attempting ray.init(num_gpus=${NUM_GPUS})')

# try:
#     # Initialize Ray locally, telling it about the GPUs using the shell variable value
#     ray.init(num_gpus=${NUM_GPUS}, ignore_reinit_error=True)
#     print('Ray initialized.')

#     # Give state a moment to potentially stabilize
#     time.sleep(2)

#     # Check resources using preferred API
#     available_resources = ray.available_resources()
#     print(f'ray.available_resources(): {available_resources}')
#     gpu_count_from_available = available_resources.get('GPU', 0)
#     print(f'GPU count from ray.available_resources(): {gpu_count_from_available}')

#     # Check resources using the state API (used by trainer)
#     print('Querying ray.state API...')
#     available_nodes_state = ray.nodes() # Use ray.nodes() as ray.state.available_resources_per_node is deprecated
#     print(f'ray.nodes(): {available_nodes_state}')

#     gpu_count_from_state = 0
#     if available_nodes_state:
#          # Sum GPUs from the 'Resources' dict for each node
#          for node_info in available_nodes_state:
#               gpu_count_from_state += node_info.get('Resources', {}).get('GPU', 0)

#     print(f'GPU count derived from ray.nodes() resources: {gpu_count_from_state}')

#     ray.shutdown()
#     print('Ray shutdown.')
# except Exception as e:
#     print(f'Error during Ray test: {e}')
#     # Attempt shutdown even on error
#     try:
#         ray.shutdown()
#     except Exception:
#         pass # Ignore shutdown errors if init failed badly
# "
# echo "--- End Minimal Ray GPU Test ---"

#export VLLM_NCCL_SO_PATH="/home/<USER>/gpuuser255/.conda/envs/verl3/lib/python3.10/site-packages/nvidia/nccl/lib/libnccl.so.2"
export VLLM_NCCL_SO_PATH=/home/<USER>/apps/nccl/2.21.5-1-cuda12.4/lib/libnccl.so.2

# --- Configuration for Parallel CFL3D Simulations ---
export CFL3D_MAX_PARALLEL_RUNS=20 # Number of concurrent CFL3D simulations (each uses 2 MPI processes)
# export CFL3D_JOB_TIMEOUT=7200   # Optional: Timeout in seconds for a single CFL3D job (default is 7200s = 2 hours in cfl3d_simulation_tool.py)

# --- Configuration for Parallel Mesh Generation ---
export CGRID_MAX_PARALLEL_RUNS=16 # Number of concurrent cgrid processes
# export CGRID_JOB_TIMEOUT=600    # Optional: Timeout for a single cgrid job (default is 600s)

# --- Configuration ---
# Important: Adjust GPU settings based on your hardware!
export RANDOM_SEED=42 # Define a seed value
export WANDB_MODE=offline  # WandB 离线模式
export BASE_MODEL='./model/qwen3_4B' # Your local model path for Qwen3-4B
#export BASE_MODEL="./model/qwen2.5_1.5B_instruct"
export PROJECT_NAME='cfd_agent'        # WandB project name
export EXPERIMENT_NAME='ppo_cfd_qwen3-4b'    # WandB experiment name for Qwen3-4B
#export EXPERIMENT_NAME='ppo_cfd_qwen2.5_1.5B_instruct'    # WandB experiment name



# Dataset and Reward Function
export TRAIN_FILE='./scripts/autocfd/processed/train_cfd_final.parquet'
export VAL_FILE='./scripts/autocfd/processed/val_cfd_final.parquet'
export REWARD_FILE_PATH='./agent_r1/src/reward_score/qa_em_and_format.py' # Path to your reward function file
export REWARD_FUNCTION_NAME='compute_score_cfd'                   # Name of your reward function
export TOOL_ENV_NAME='cfd' # Name of your tool environment (adjust if different)

# Optional: VLLM/CUDA settings (keep defaults unless needed)
# export VLLM_ATTENTION_BACKEND=XFORMERS # Commented out as per VLLM warning
export HYDRA_FULL_ERROR=1
#export CUDA_LAUNCH_BLOCKING=1
#export TORCH_DISTRIBUTED_DEBUG=INFO # 可选值：INFO, DETAIL

# --- 验证配置参数 (基于精确 Token 分析优化) ---
# System Message: 3338 tokens, 完整 Response 需求: 600-1600 tokens
export VAL_MAX_NEW_TOKENS=2048  # Based on analysis: 1600 (response) + 400 (buffer)
export VAL_MAX_TOOL_CALLS=5     # CFD workflow: mesh_generation + cfl3d_simulation + analysis
export VAL_TIMEOUT=1200         # 20 minutes for CFD simulations
export VAL_DO_SAMPLE=True
export VAL_TEMPERATURE=0.7      # Optimized for Qwen3-4B
export VAL_TOP_P=0.9           # Optimized for Qwen3-4B
export VAL_TOP_K=50            # Optimized for Qwen3-4B

# --- Main Training Command ---
echo "Starting PPO training for CFD Agent..."
echo "Model: $BASE_MODEL"
echo "Num GPUs: $NUM_GPUS, TP Size: $TP_SIZE"
echo "Train File: $TRAIN_FILE"
echo "Reward Function: $REWARD_FUNCTION_NAME in $REWARD_FILE_PATH"
echo "Tool Environment: $TOOL_ENV_NAME"
echo "Random Seed: $RANDOM_SEED"
echo "Experiment Name: $EXPERIMENT_NAME"

# --- Set NCCL Network Interface ---
# !! 请根据下面打印出的接口信息，更新这里的接口名 !!
# !! 例如： export NCCL_SOCKET_IFNAME=eth1 或者 export NCCL_SOCKET_IFNAME=ib0 !!
# export NCCL_SOCKET_IFNAME=bond0
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=0
export NCCL_IB_HCA=mlx5_0:1
export NCCL_IB_GID_INDEX=3
export NCCL_P2P_DISABLE=1
export NCCL_SHM_DISABLE=0
export NCCL_ASYNC_ERROR_HANDLING=0
export GLOO_SOCKET_IFNAME=lo

#export RAY_BACKEND_LOG_LEVEL=debug
#export RAY_LOG_TO_STDERR=1
# --- Check paths ---
if [ ! -d "$BASE_MODEL" ]; then
  echo "Error: Model directory not found at relative path $BASE_MODEL (from $(pwd))"
  exit 1
fi
if [ ! -f "$REWARD_FILE_PATH" ]; then
  echo "Error: Reward function file not found at relative path $REWARD_FILE_PATH (from $(pwd))"
  exit 1
fi
echo "Path checks passed."

python3 -m agent_r1.src.main_agent \
    data.train_files=$TRAIN_FILE \
    data.val_files=$VAL_FILE \
    data.use_custom_tool_format_func=True \
    data.train_batch_size=48 `# Further reduced for longer sequences (6144+2048 tokens)` \
    data.truncation=left \
    +data.num_workers=8 \
    data.max_prompt_length=3072 `# Based on precise token analysis: 3338 (system) + 2000 (history) + 150 (user) + buffer` \
    data.max_response_length=1024 `# Based on analysis: 1600 (response) + 400 (buffer)` \
    data.max_start_length=1024 `# Sufficient for CFD conversation starts` \
    data.max_tool_response_length=1024 `# Sufficient for detailed CFD tool responses` \
    actor_rollout_ref.model.path=$BASE_MODEL \
    +actor_rollout_ref.model.override_config.torch_dtype=bfloat16 \
    +actor_rollout_ref.actor.fsdp_config.model_dtype=bfloat16 \
    actor_rollout_ref.actor.optim.lr=1e-6 \
    actor_rollout_ref.model.use_remove_padding=False \
    actor_rollout_ref.rollout.enforce_eager=True \
    actor_rollout_ref.rollout.free_cache_engine=True \
    actor_rollout_ref.actor.ppo_mini_batch_size=32 `# Reduced for Qwen3-4B` \
    actor_rollout_ref.actor.ppo_micro_batch_size_per_gpu=2 `# Reduced for Qwen3-4B memory` \
    actor_rollout_ref.model.enable_gradient_checkpointing=True \
    actor_rollout_ref.actor.fsdp_config.param_offload=True \
    actor_rollout_ref.actor.fsdp_config.optimizer_offload=True \
    actor_rollout_ref.rollout.log_prob_micro_batch_size_per_gpu=2 `# Reduced for Qwen3-4B memory` \
    actor_rollout_ref.rollout.tensor_model_parallel_size=$TP_SIZE `# Must match TP_SIZE` \
    actor_rollout_ref.rollout.name=vllm \
    actor_rollout_ref.rollout.gpu_memory_utilization=0.4 `# Reduced for Qwen3-4B memory requirements` \
    actor_rollout_ref.rollout.dtype=bfloat16 \
    ++actor_rollout_ref.rollout.seed=$RANDOM_SEED `# Add seed to VLLM rollout config` \
    ++vllm.seed=$RANDOM_SEED `# Add seed to VLLM config` \
    actor_rollout_ref.rollout.max_num_batched_tokens=12288 `# Based on token analysis: 6144+2048=8192, with buffer` \
    actor_rollout_ref.rollout.max_num_seqs=192 `# Reduced to accommodate longer sequences` \
    actor_rollout_ref.ref.log_prob_micro_batch_size_per_gpu=2 `# Reduced for Qwen3-4B memory` \
    actor_rollout_ref.ref.fsdp_config.param_offload=True `# Ref model often offloaded` \
    critic.optim.lr=1e-5 \
    critic.model.use_remove_padding=False \
    critic.model.path=$BASE_MODEL \
    +critic.model.override_config.torch_dtype=bfloat16 \
    +critic.model.fsdp_config.model_dtype=bfloat16 \
    critic.model.enable_gradient_checkpointing=True \
    critic.ppo_micro_batch_size_per_gpu=2 `# Reduced for Qwen3-4B memory` \
    critic.model.fsdp_config.param_offload=True \
    critic.model.fsdp_config.optimizer_offload=True \
    algorithm.adv_estimator=gae \
    algorithm.kl_ctrl.kl_coef=0.0005 `# Reduced for Qwen3-4B stability` \
    custom_reward_function.path=$REWARD_FILE_PATH `# Specify reward function file` \
    custom_reward_function.name=$REWARD_FUNCTION_NAME `# Specify reward function name` \
    trainer.critic_warmup=3 \
    trainer.logger=['console','wandb'] `# Changed from ['console','wandb']` \
    trainer.project_name=$PROJECT_NAME \
    trainer.experiment_name=$EXPERIMENT_NAME \
    trainer.n_gpus_per_node=$NUM_GPUS `# Must match NUM_GPUS` \
    trainer.nnodes=1 \
    trainer.save_freq=10  `# Save every 10 steps, adjust as needed (-1 disables)` \
    trainer.test_freq=25 `# Evaluate every 10 steps, adjust as needed` \
    trainer.total_epochs=5 `# Adjust number of epochs` \
    trainer.val_before_train=True \
    tool.env=$TOOL_ENV_NAME `# Use the CFD tool environment` \
    actor_rollout_ref.actor.grad_clip=0.1 \
    critic.grad_clip=0.1 \
    ++trainer.validate_config.max_new_tokens=$VAL_MAX_NEW_TOKENS \
    ++trainer.validate_config.max_tool_calls=$VAL_MAX_TOOL_CALLS \
    ++trainer.validate_config.timeout=$VAL_TIMEOUT \
    ++trainer.validate_config.do_sample=$VAL_DO_SAMPLE \
    ++trainer.validate_config.temperature=$VAL_TEMPERATURE \
    ++trainer.validate_config.top_p=$VAL_TOP_P \
    ++trainer.validate_config.top_k=$VAL_TOP_K \
    $@ # Pass any extra command-line arguments

echo "Training script finished or exited."

# # 尝试复制 Ray 日志 (路径可能需要调整)
# RAY_LOG_DIR_PATTERN="/tmp/ray/session_*" # Ray 会话目录通常在此模式下
# LATEST_RAY_LOG_DIR=$(ls -td $RAY_LOG_DIR_PATTERN | head -n 1)
# TARGET_LOG_DIR="$HOME/ray_job_logs/job_${SLURM_JOB_ID}" # 目标目录

# if [ -d "$LATEST_RAY_LOG_DIR/logs" ]; then
#   echo "Attempting to copy Ray logs from $LATEST_RAY_LOG_DIR/logs to $TARGET_LOG_DIR"
#   mkdir -p "$TARGET_LOG_DIR"
#   cp -r "$LATEST_RAY_LOG_DIR/logs"/* "$TARGET_LOG_DIR/"
#   echo "Ray logs copied (or attempted)."
# else
#   echo "Could not find Ray log directory matching $RAY_LOG_DIR_PATTERN"
# fi
