"""
CFL3D计算工具，用于执行CFD计算
#    python -m agent_r1.tool.tools.cfl3d_simulation_tool
"""

import os
import json
import traceback
from multiprocessing import Pool, TimeoutError
from typing import List, Dict, Any

from agent_r1.tool.tool_base import Tool
from .Function import cfl3d_simulation_func

class CFL3DSimulationTool(Tool):
    """
    用于执行CFL3D计算流体力学分析的工具类，支持多种参数配置方式
    """
    
    def __init__(self):
        """
        初始化CFL3D计算工具
        """
        name = "cfl3d_simulation"
        description = "使用CFL3D执行CFD计算，分析翼型气动性能。需要先调用mesh_generation工具生成计算网格，然后再使用此工具。"
        parameters = {
            "type": "object",
            "properties": {
                "unique_run_dir": { 
                    "type": "string",
                    "description": "先前网格生成步骤 (mesh_generation tool) 返回的唯一工作目录的路径。此目录包含 cfl3d.xyz, cfl3d.inp (模板或由cgrid生成), foil.dat, 和 RangeAndHeight.dat。"
                },
                "mach": {
                    "type": "number",
                    "description": "马赫数"
                },
                "alpha": {
                    "type": "number",
                    "description": "攻角，单位为度"
                },
                "re": {
                    "type": "number",
                    "description": "雷诺数"
                },
                "IVISC": {
                    "type": "integer",
                    "description": "湍流模型选择，一般为0-13，默认为5 (SA模型，基于cfl3d_test.inp)。可通过查看手册IVISC获取更多信息", 
                    "default": 5
                },
                "output_folder_name": { 
                    "type": "string",
                    "description": "在 unique_run_dir 内指定输出结果的子文件夹名称",
                    "default": "output"
                },
                "airfoil_name": {
                    "type": "string",
                    "description": "可选的翼型名称，用于在结果中记录。此名称应与网格生成时使用的名称一致。",
                    "default": None
                },
                "advanced_config": {
                    "type": "object",
                    "description": "可选的高级配置字典，包含更多CFL3D输入参数设置，覆盖默认值或模板文件中的值",
                    "properties": {
                        "beta": {"type": "number", "description": "侧滑角，单位为度", "default": 0.0},
                        "tinf": {"type": "number", "description": "自由流温度，单位为兰肯温度(°R)", "default": 460.0},
                        "dt": {"type": "number", "description": "时间步长，负值表示局部时间步长", "default": -0.8},
                        "irest": {"type": "integer", "description": "重启标志，0表示新计算，1表示从之前结果重启", "default": 0},
                        "iflagts": {"type": "integer", "description": "最大时间步数", "default": 2000},
                        "fmax": {"type": "number", "description": "允许的最大CFL数", "default": 5.0},
                        "cfltau": {"type": "number", "description": "CFL数调整系数", "default": 7.5},
                        "sref": {"type": "number", "description": "参考面积，用于系数计算（二维翼型一般为1）", "default": 1.0},
                        "cref": {"type": "number", "description": "参考弦长，用于力矩计算（二维翼型一般为1）", "default": 1.0},
                        "bref": {"type": "number", "description": "参考展长，用于力矩计算（二维翼型一般为1）", "default": 1.0},
                        "xmc": {"type": "number", "description": "力矩参考点X坐标（二维翼型一般为0.25）", "default": 0.25},
                        "nplot3d": {"type": "integer", "description": "PLOT3D输出频率", "default": 1},
                        "nprint": {"type": "integer", "description": "收敛信息打印频率", "default": -1},
                        "nwrest": {"type": "integer", "description": "重启文件写入频率", "default": 5000},
                        "i2d": {"type": "integer", "description": "二维计算标志(1=2D, 0=3D)", "default": 1},
                        "iem": {"type": "integer", "description": "通量计算方法", "default": 0},
                        "iadvance": {"type": "integer", "description": "推进方法(0=显式, 1=隐式)", "default": 0},
                        "iforce": {"type": "integer", "description": "力和力矩计算频率", "default": 333},
                        "iflim_i": {"type": "integer", "description": "I方向通量限制器类型", "default": 4},
                        "iflim_j": {"type": "integer", "description": "J方向通量限制器类型", "default": 4},
                        "iflim_k": {"type": "integer", "description": "K方向通量限制器类型", "default": 4},
                        "rkap0_i": {"type": "number", "description": "I方向人工黏性系数", "default": 0.3333},
                        "rkap0_j": {"type": "number", "description": "J方向人工黏性系数", "default": 0.3333},
                        "rkap0_k": {"type": "number", "description": "K方向人工黏性系数", "default": 0.3333},
                        "mseq": {"type": "integer", "description": "多重网格层数", "default": 3},
                        "mgflag": {"type": "integer", "description": "多重网格标志(0=禁用, 1=启用)", "default": 1},
                        "ncyc_level1": {"type": "integer", "description": "第一层网格最大迭代次数", "default": 1000},
                        "ncyc_level2": {"type": "integer", "description": "第二层网格最大迭代次数", "default": 1000},
                        "ncyc_level3": {"type": "integer", "description": "第三层网格最大迭代次数", "default": 1000}
                    },
                    "default": None
                }
            },
            "required": ["unique_run_dir", "mach", "alpha", "re"]
        }
        
        super().__init__(name, description, parameters)
    
    def execute(self, args: dict) -> str:
        """
        执行CFL3D计算的主入口点。

        Args:
            args (dict): 包含工具参数的字典，符合 'parameters' 定义。

        Returns:
            str: JSON 格式的执行结果字符串。
        """
        unique_run_dir = args.get("unique_run_dir")
        mach = args.get("mach")
        alpha = args.get("alpha")
        re = args.get("re")
        airfoil_name = args.get("airfoil_name")

        if not unique_run_dir:
            error_message = (
                "缺少必需的参数: 'unique_run_dir'。"
                "提示: 'unique_run_dir' 是由 'mesh_generation' 工具成功执行后生成的。"
                "请确保优先调用 'mesh_generation' 工具，并将其返回的 'unique_run_dir' 路径用于此工具。"
            )
            return json.dumps({"status": "error", "message": error_message})

        if not os.path.isdir(unique_run_dir):
            error_message = (
                f"提供的 'unique_run_dir' ('{unique_run_dir}') 不是一个有效的目录或不存在。"
                f"提示: 'unique_run_dir' 应由 'mesh_generation' 工具创建。"
                f"请检查 'mesh_generation' 工具是否已成功运行，并确认传递的路径是否正确。"
                f"如果 'mesh_generation' 工具未运行或失败，请先调用它。"
            )
            return json.dumps({"status": "error", "message": error_message})

        missing_params = []
        if mach is None: missing_params.append("mach")
        if alpha is None: missing_params.append("alpha")
        if re is None: missing_params.append("re")
        if missing_params:
            return json.dumps({"status": "error", "message": f"缺少必需的参数: {', '.join(missing_params)}"})

        IVISC = args.get("IVISC", 5) 
        output_folder_name = args.get("output_folder_name", "output") 
        advanced_config = args.get("advanced_config", None)

        # print(f"[DEBUG] CFL3DSimulationTool.execute: unique_run_dir='{unique_run_dir}', mach={mach}, alpha={alpha}, re={re}, IVISC={IVISC}, output_folder_name='{output_folder_name}', advanced_config provided: {advanced_config is not None}, airfoil_name='{airfoil_name}'")

        try:
            result_dict = cfl3d_simulation_func(
                mach=mach,
                alpha=alpha,
                re=re,
                unique_run_dir=unique_run_dir, 
                output_folder_name=output_folder_name, 
                IVISC=IVISC,
                advanced_config=advanced_config,
                num_processes=2,  # Force 2 MPI processes
                airfoil_name=airfoil_name
            )
            return json.dumps(result_dict)

        except Exception as e:
            error_msg = f"CFL3D计算工具执行失败 (execute): {str(e)}"
            print(f"[ERROR] {error_msg}")
            print(traceback.format_exc())
            if isinstance(e, FileNotFoundError) and unique_run_dir in str(e):
                 error_msg += (
                     f" 错误可能与 '{unique_run_dir}' 内的文件有关。"
                     f" 请再次确认 'mesh_generation' 工具是否已成功准备好所有必需文件。"
                 )
            return json.dumps({"status": "error", "message": error_msg, "results_directory": None})

    def _execute_single_case_for_batch(self, args_with_index: tuple) -> tuple:
        """
        一个辅助方法，用于在 batch_execute 中并行执行单个CFL3D案例。
        它接收一个包含原始索引和参数的元组，并返回包含索引和结果的元组。
        """
        original_index, case_args = args_with_index
        # print(f"[BATCH_DEBUG] Starting case {original_index} with args: {case_args}")
        try:
            result_str = self.execute(case_args)
            # print(f"[BATCH_DEBUG] Finished case {original_index}")
            return original_index, result_str
        except Exception as e:
            error_msg = f"CFL3D并行计算案例执行失败 (args: {case_args}): {str(e)}"
            print(f"[BATCH_ERROR] {error_msg}")
            print(traceback.format_exc())
            return original_index, json.dumps({"status": "error", "message": error_msg, "details": traceback.format_exc()})

    def batch_execute(self, args_list: List[Dict]) -> List[str]:
        """
        并行执行多个CFL3D模拟。
        """
        if not args_list:
            return []

        num_parallel_processes = int(os.environ.get("CFL3D_MAX_PARALLEL_RUNS", min(os.cpu_count() or 1, 4)))
        num_parallel_processes = min(num_parallel_processes, len(args_list))

        print(f"[INFO] CFL3DSimulationTool.batch_execute: Starting batch of {len(args_list)} CFL3D simulations with up to {num_parallel_processes} parallel processes.")

        indexed_args_list = list(enumerate(args_list))
        
        results_with_indices = [None] * len(args_list)

        try:
            with Pool(processes=num_parallel_processes) as pool:
                async_job_results = [pool.apply_async(self._execute_single_case_for_batch, args=(task,)) for task in indexed_args_list]
                
                timeout_seconds_per_job = int(os.environ.get("CFL3D_JOB_TIMEOUT", 7200))

                for i, async_res in enumerate(async_job_results):
                    original_idx = indexed_args_list[i][0]
                    try:
                        _, result_str = async_res.get(timeout=timeout_seconds_per_job)
                        results_with_indices[original_idx] = result_str
                    except TimeoutError:
                        error_msg = f"CFL3D并行计算案例 (原始索引 {original_idx}, args: {args_list[original_idx]}) 超时 ({timeout_seconds_per_job}s)"
                        print(f"[BATCH_TIMEOUT] {error_msg}")
                        results_with_indices[original_idx] = json.dumps({"status": "error", "message": error_msg})
                    except Exception as e:
                        error_msg = f"CFL3D并行计算案例 (原始索引 {original_idx}, args: {args_list[original_idx]}) 获取结果时发生意外错误: {str(e)}"
                        print(f"[BATCH_ERROR] {error_msg}")
                        print(traceback.format_exc())
                        results_with_indices[original_idx] = json.dumps({"status": "error", "message": error_msg})
                        
        except Exception as e:
            print(f"[FATAL_BATCH_ERROR] CFL3DSimulationTool.batch_execute encountered an error with the multiprocessing pool: {e}")
            print(traceback.format_exc())
            for i in range(len(args_list)):
                if results_with_indices[i] is None:
                    results_with_indices[i] = json.dumps({"status": "error", "message": f"Batch processing pool error: {str(e)}"})
        
        print(f"[INFO] CFL3DSimulationTool.batch_execute: Finished batch of {len(args_list)} simulations.")
        return results_with_indices

    def calculate_reward(self, args: dict, result: str) -> float:
        """
        计算CFL3D模拟工具调用的奖励。
        成功给予小奖励，失败给予较大惩罚。
        """
        try:
            result_data = json.loads(result)
            
            overall_status = result_data.get("status")
            calc_output = result_data.get("calculation_output", {}) 
            results_dir = result_data.get("results_directory")

            if overall_status == "error":
                 print(f"[Reward-Error] CFL3D tool main function reported error: {result_data.get('message')}")
                 return -0.7 

            calc_status = calc_output.get("status") 
            
            if calc_status == "success":
                 coeffs = calc_output.get("force_coefficients", {})
                 cl = coeffs.get("cl")
                 cd = coeffs.get("cd")
                 cp_status = calc_output.get("cp_data_status") # Check cp_data_status from cfl3d_simulation_func

                 if cl is not None and cd is not None:
                     reward = 0.25
                     if cp_status == "success": reward += 0.05 
                     print(f"[Reward-Info] CFL3D success, CL/CD extracted. Reward: {reward}")
                     return reward
                 else:
                     print(f"[Reward-Warning] CFL3D calc_status success, but CL/CD not extracted. Results dir: {results_dir}")
                     return 0.05 
            elif calc_status == "warning":
                print(f"[Reward-Warning] CFL3D calc_status warning: {calc_output.get('message')}. Results dir: {results_dir}")
                return -0.1 
            elif calc_status == "error":
                print(f"[Reward-Error] CFL3D calc_status error: {calc_output.get('message')}. Results dir: {results_dir}")
                return -0.6 
            else:
                print(f"[Reward-Error] Unknown calc_status in CFL3D result. Results dir: {results_dir}")
                return -0.4
        except (json.JSONDecodeError, KeyError, Exception) as e:
            print(f"[Reward-Error] Error parsing CFL3D result for reward: {e}")
            return -0.8

# --- 调试代码 ---
if __name__ == "__main__":
    import os
    import sys
    import shutil
    # 假设此脚本通过 python -m 从项目根目录运行
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
    # sys.path.insert(0, project_root)
    # try:
    #      from agent_r1.tool.tool_base import Tool # 重新导入以防直接运行
    #      from Function import cfl3d_simulation_func, modify_cfl3d_input, run_cfl3d, read_cfl3d_output, extract_force_coefficients, apply_advanced_config, extract_cp_from_cfl3d
    # except ImportError as e:
    #      print(f"导入错误: {e}. 请确保从项目根目录使用 'python -m agent_r1.tool.tools.cfl3d_simulation_tool' 运行，或者项目已正确安装。")
    #      sys.exit(1)

    print("--- 开始测试 CFL3DSimulationTool ---")

    # 预期 cfl3d 目录相对于项目根目录的位置
    cfl3d_base_dir = './cfl3d' # 相对于运行脚本的目录
    cfl3d_exe_path = os.path.join(cfl3d_base_dir, 'cfl3d_Seq_LHR.exe')
    cfl3d_inp_path = os.path.join(cfl3d_base_dir, 'cfl3d.inp')
    cfl3d_grid_path = os.path.join(cfl3d_base_dir, 'cfl3d.xyz')
    test_output_folder = 'test_cfl3d_output' # 测试用的输出目录名
    test_output_path = os.path.join(cfl3d_base_dir, test_output_folder)
    output_file_path = os.path.join(test_output_path, 'cfl3d.out')

    print(f"检查路径:")
    print(f"  预期 cfl3d 目录: {os.path.abspath(cfl3d_base_dir)}")
    print(f"  预期 cfl3d.exe: {os.path.abspath(cfl3d_exe_path)}")
    print(f"  预期 cfl3d.inp: {os.path.abspath(cfl3d_inp_path)}")
    print(f"  预期 cfl3d.xyz (网格): {os.path.abspath(cfl3d_grid_path)}")
    print(f"  测试输出目录: {os.path.abspath(test_output_path)}")

    # 1. 检查 cfl3d 目录是否存在
    if not os.path.isdir(cfl3d_base_dir):
        print(f"[错误] 必要的 '{cfl3d_base_dir}' 目录未在当前工作目录找到。")
        print("请确保从项目根目录运行，并且 'cfl3d' 目录存在。")
        sys.exit(1)
    else:
        print(f"[成功] 找到 '{cfl3d_base_dir}' 目录。")

    # 2. 检查 cfl3d 可执行文件是否存在
    if not os.path.exists(cfl3d_exe_path):
        print(f"[警告] 未找到 CFL3D 可执行文件 '{cfl3d_exe_path}'。计算会失败。")
        can_run_cfl3d = False
    else:
        print(f"[成功] 找到 CFL3D 可执行文件 '{cfl3d_exe_path}'。")
        can_run_cfl3d = True

    # 3. 检查 cfl3d 输入文件是否存在
    if not os.path.exists(cfl3d_inp_path):
        print(f"[错误] 未找到 CFL3D 输入模板文件 '{cfl3d_inp_path}'。")
        sys.exit(1)
    else:
        print(f"[成功] 找到 CFL3D 输入模板文件 '{cfl3d_inp_path}'。")

    # 4. 检查网格文件是否存在
    if not os.path.exists(cfl3d_grid_path):
        print(f"[警告] 未找到 CFL3D 网格文件 '{cfl3d_grid_path}'。计算可能会失败或使用默认网格。")
        grid_available = False
    else:
        print(f"[成功] 找到 CFL3D 网格文件 '{cfl3d_grid_path}'。")
        grid_available = True

    # 5. 尝试实例化工具
    try:
        cfl3d_tool = CFL3DSimulationTool()
        print("[成功] CFL3DSimulationTool 实例化成功。")
    except Exception as e:
        print(f"[错误] CFL3DSimulationTool 实例化失败: {e}")
        sys.exit(1)

    # 6. 准备测试参数
    test_args = {
        "mach": 0.5,
        "alpha": 2.0,
        "re": 3e6,
        "output_folder_name": test_output_folder, # 指定测试输出目录
        "IVISC": 5, # 使用默认的 SA 模型
        "advanced_config": { # 减少迭代次数以加速测试
            "iflagts": 100, # 最大时间步数
            "ncyc_level1": 50,
            "ncyc_level2": 50,
            "ncyc_level3": 50,
            "nwrest": 100 # 减少重启文件写入频率
        }
    }

    print(f"\n--- 执行工具测试 (参数: {test_args}) ---")
    # 清理之前的测试输出目录（如果存在）
    if os.path.exists(test_output_path):
        print(f"清理旧的测试输出目录: {test_output_path}")
        try:
            shutil.rmtree(test_output_path)
        except Exception as e:
            print(f"[警告] 清理旧目录失败: {e}")

    if not can_run_cfl3d:
        print("[跳过] 执行测试，因为 CFL3D 可执行文件不可用。")
    elif not grid_available:
        print("[跳过] 执行测试，因为网格文件不可用。")
    else:
        try:
            # 调用标准的 execute 方法
            result_json = cfl3d_tool.execute(test_args)
            print("\n--- 工具执行结果 (JSON) ---")
            print(result_json)
            # 尝试解析 JSON
            result = json.loads(result_json)

            # 检查关键输出文件是否存在
            print("\n--- 检查输出文件 ---")
            # 稍等片刻让文件系统同步
            import time
            time.sleep(0.5)
            if os.path.exists(output_file_path):
                print(f"[成功] 找到输出文件: {output_file_path}")
                # 读取最后几行
                try:
                    with open(output_file_path, 'r') as f:
                        lines = f.readlines()
                    print("输出文件最后 10 行:")
                    for line in lines[-10:]:
                        print(line.strip())
                    # 检查是否有计算出的 CL/CD
                    calc_output = result.get("calculation_output", {})
                    coeffs = calc_output.get("force_coefficients", {})
                    print(f"提取的力系数: CL={coeffs.get('cl')}, CD={coeffs.get('cd')}")
                except Exception as read_e:
                    print(f"[警告] 读取输出文件时出错: {read_e}")
            else:
                print(f"[失败] 计算报告成功/警告，但未找到输出文件: {output_file_path}")

        except json.JSONDecodeError:
            print("[错误] 工具返回的不是有效的 JSON 字符串:")
            print(result_json)
        except Exception as e:
            print(f"\n[错误] 工具执行过程中出错: {e}")
            import traceback
            traceback.print_exc()

    # 测试完成后可以选择性清理测试输出目录
    # if os.path.exists(test_output_path):
    #     shutil.rmtree(test_output_path)
    #     print(f"\n[清理] 已删除测试输出目录: {test_output_path}")

    print("\n--- CFL3DSimulationTool 测试结束 ---") 