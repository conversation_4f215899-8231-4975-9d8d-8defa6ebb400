import glob
import json
import logging
import os
import re
import shutil
import subprocess
from io import StringIO
from subprocess import Popen, PIPE, STDOUT
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
import sys
from .enhanced_cst import enhanced_cst_foil_with_fit, read_coordinates
from cfdpost.cfdresult import cfl3d  # New import
import time
import uuid # Added import
import argparse


def Aerodynamic_parameters(re=None, airspeed=None, mach=None, alpha=None, a=336.4, rho=1.17, chord_length=1.0,
                           mu=1.81e-5):
    """
    Function to convert between Reynolds number, airspeed, and Mach number.

    :param re: Optional, Reynolds number to convert to airspeed (if given).
    :param airspeed: Optional, airspeed (m/s) to convert to Reynolds number (if given).
    :param mach: Optional, Mach number to convert to airspeed or calculate from airspeed (if given).
    :param a: Speed of sound in m/s. Defaults to 336.4 m/s (standard condition).
    :param rho: Air density in kg/m³. If None, defaults to 1.225 kg/m³ (standard conditions).
    :param chord_length: Characteristic length, such as airfoil chord length in meters. If None, defaults to 1.0 m.
    :param mu: Dynamic viscosity of air in Pa·s. If None, defaults to 1.81e-5 Pa·s (standard for air at 15°C).

    :return: Returns a dictionary with calculated Reynolds number, airspeed, and Mach number.
    """
    # print(re, airspeed, mach)
    if airspeed is not None and airspeed == 0:
        airspeed = None
    if mach is not None and mach == 0:
        mach = None
    if re is not None and re == 0:
        re = None
    result = {}  # To store all calculated values
    print("inpt", re, airspeed, mach)
    if airspeed is None:
        if mach is not None:
            airspeed = mach * a  # 根据马赫数计算空速
        elif re is not None:
            airspeed = re * mu / (rho * chord_length)  # 根据雷诺数计算空速
        else:
            re = 0
            airspeed = 0
            # raise ValueError("At least one of Reynolds number, airspeed, or Mach number must be provided.")

    if mach is None:
        mach = airspeed / a  # 根据空速计算马赫数

    if re is None:
        re = (rho * airspeed * chord_length) / mu

    if alpha is None:
        alpha = 0
    # 如果提供了Reynolds数和空速

    '''if (re != None and 0) and (airspeed != None and 0) and (mach == None or 0):
        # 使用提供的空速计算Reynolds数
        calculated_re = (rho * airspeed * chord_length) / mu

        # 计算提供的Reynolds数和计算Reynolds数之间的相对误差
        relative_error = abs(calculated_re - re) / re
        print(re, airspeed)
        # 如果相对误差超过1%，则抛出错误
        if relative_error > 0.01:
            raise ValueError(f"The relative error between the provided Re and the calculated Re exceeds 1% "
                             f"(Relative Error: {relative_error * 100:.2f}%). "
                             f"Provided Re: {re}, Calculated Re: {calculated_re}.")

        # 计算并存储Reynolds数和空速
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了空速，则计算Reynolds数
    elif (re == None or 0) and (airspeed != None and 0):
        re = (rho * airspeed * chord_length) / mu
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了Reynolds数，则计算空速
    elif (airspeed == None or 0) and (re != None and 0):
        airspeed = (re * mu) / (rho * chord_length)
        result["Airspeed"] = airspeed
        result["Reynolds_number"] = re
        result["Mach"] = airspeed/a

    # 如果提供了马赫数，则根据声速计算空速
    if (mach != None and 0) and (airspeed == None or 0):
        airspeed = mach * a
        result["Airspeed"] = airspeed
        result["Mach"] = mach

    # 如果提供了空速，则计算马赫数
    elif (airspeed != None and 0) and mach == None:
        mach = airspeed / a
        result["Mach"] = mach
        result["Airspeed"] = airspeed'''

    # 如果没有提供足够的参数，则抛出错误
    # if airspeed == None or 0 and re ==None or 0 and mach == None or 0:

    # Include all parameters in the result
    result["Mach"] = mach
    result["Airspeed"] = airspeed
    result["Reynolds_number"] = re
    result["rho"] = rho
    result["chord_length"] = chord_length
    result["mu"] = mu
    result["speed_of_sound"] = a  # 声速
    result["alpha"] = alpha
    print("outpt:", re, airspeed, mach)
    return result


# cfl3d
def cfl3d_simulation_func(mach, alpha, re, unique_run_dir, output_folder_name="output", IVISC=7, advanced_config=None, num_processes=2, airfoil_name: str = None, keep_intermediate_files: bool = False, max_retries_on_cp_failure: int = 2): # Added max_retries_on_cp_failure
    """
    执行CFL3D计算的全流程函数

    Args:
        mach: 马赫数
        alpha: 攻角，单位为度
        re: 雷诺数
        unique_run_dir: cgrid步骤生成并包含cfl3d.xyz, cfl3d.inp, foil.dat, 和 RangeAndHeight.dat的唯一工作目录
        output_folder_name: 在unique_run_dir内创建的输出文件夹名称，默认为"output"
        IVISC: 湍流模型选择(0-13)，默认为7(SA模型)
        advanced_config: 高级配置字典，包含更多参数设置
        num_processes: MPI进程数
        airfoil_name: 翼型名称，用于记录
        keep_intermediate_files: 如果为True，则保留中间计算文件 (例如 surf.g, cfl3d.out) 到永久结果目录
        max_retries_on_cp_failure: 在CL/CD成功但Cp提取失败时，最大重试次数 (默认为1，表示总共尝试2次)

    Returns:
        计算结果
    """
    cfl3d_inp_path_in_run_dir = os.path.join(unique_run_dir, 'cfl3d.inp')
    cfl3d_xyz_path_in_run_dir = os.path.join(unique_run_dir, 'cfl3d.xyz')

    actual_output_dir_path = os.path.join(unique_run_dir, output_folder_name)

    # 创建永久保存结果的目录
    permanent_results_dir = './cal_results'
    os.makedirs(permanent_results_dir, exist_ok=True)

    # 创建与unique_run_dir对应的结果子目录
    run_id = os.path.basename(unique_run_dir)
    permanent_run_results_dir = os.path.join(permanent_results_dir, run_id)
    os.makedirs(permanent_run_results_dir, exist_ok=True)

    # 注意：旧的输出文件夹清理逻辑已移至重试循环内部，以确保每次尝试前都清理

    final_stdout, final_stderr = "", ""
    final_output_data, final_cp_data = {}, {}
    cp_extraction_attempts_count = 0

    try:
        if not os.path.exists(cfl3d_xyz_path_in_run_dir):
            return {"status": "error", "message": f"网格文件 {cfl3d_xyz_path_in_run_dir} 未在指定运行目录中找到。请确保 cgrid 已成功运行并生成此文件。"}

        if not os.path.exists(cfl3d_inp_path_in_run_dir):
            return {"status": "error", "message": f"CFL3D输入文件 {cfl3d_inp_path_in_run_dir} 未在指定运行目录中找到。请确保 cgrid 已成功运行并生成此文件。"}

        input_modification_result = modify_cfl3d_input(
            cfl3d_inp_path_in_run_dir,
            mach,
            alpha,
            re,
            IVISC,
            advanced_config
        )
        if input_modification_result.get("status") == "error":
            return {"status": "error", "message": f"修改输入文件失败: {input_modification_result.get('message')}"}

        base_cfl3d_dir = os.path.dirname(unique_run_dir) # This is './cfl3d'

        for attempt in range(max_retries_on_cp_failure + 1): # +1 for the initial attempt
            cp_extraction_attempts_count = attempt + 1
            print(f"[Info] CFL3D/Cp Extraction - Attempt {cp_extraction_attempts_count} of {max_retries_on_cp_failure + 1} for run_id: {run_id}")

            # 清理并创建本次尝试的输出目录
            if os.path.exists(actual_output_dir_path):
                try:
                    shutil.rmtree(actual_output_dir_path)
                    print(f"[清理] (Attempt {cp_extraction_attempts_count}) 已删除旧的输出文件夹: {actual_output_dir_path}")
                except Exception as e_clean:
                    print(f"[警告] (Attempt {cp_extraction_attempts_count}) 删除旧的输出文件夹 {actual_output_dir_path} 失败: {e_clean}")
            os.makedirs(actual_output_dir_path, exist_ok=True)

            try:
                current_stdout, current_stderr = run_cfl3d(
                    source_dir=unique_run_dir,
                    output_folder_name=output_folder_name,
                    cfl3d_exe_root_dir=base_cfl3d_dir,
                    num_processes=num_processes
                )
                final_stdout, final_stderr = current_stdout, current_stderr # Store latest stdout/stderr

                current_output_data = read_cfl3d_output(actual_output_dir_path)
                current_cp_data = extract_cp_from_cfl3d(actual_output_dir_path)
                final_output_data, final_cp_data = current_output_data, current_cp_data # Store latest output/cp data

            except Exception as run_extract_exception:
                print(f"[Error] (Attempt {cp_extraction_attempts_count}) Exception during run_cfl3d or data extraction: {run_extract_exception}")
                # Update final_output_data to reflect this specific error if it's not already set
                if not final_output_data: # If it was never set
                    final_output_data = {"status": "error", "message": f"CFL3D run/extraction failed on attempt {cp_extraction_attempts_count}: {run_extract_exception}", "force_coefficients": {}}
                if not final_cp_data:
                    final_cp_data = {"status": "error", "message": f"Cp extraction failed due to earlier run error on attempt {cp_extraction_attempts_count}"}

                if attempt < max_retries_on_cp_failure: # If error in run_cfl3d itself, maybe retry still makes sense if it's random
                    print(f"[Warning] (Attempt {cp_extraction_attempts_count}) Exception occurred, retrying if possible...")
                    time.sleep(2) # Optional delay
                    continue
                else:
                    print(f"[Error] (Attempt {cp_extraction_attempts_count}) Exception occurred on final attempt. No more retries.")
                    break # Exit loop, proceed with error state

            cl_cd_valid = isinstance(final_output_data, dict) and \
                          final_output_data.get("status") == "success" and \
                          final_output_data.get("force_coefficients", {}).get("cl") is not None and \
                          final_output_data.get("force_coefficients", {}).get("cd") is not None

            cp_extraction_successful = isinstance(final_cp_data, dict) and \
                                       final_cp_data.get("status") == "success"

            if cl_cd_valid and cp_extraction_successful:
                print(f"[Info] (Attempt {cp_extraction_attempts_count}) CL/CD and Cp extraction successful.")
                break # Successful, exit retry loop
            elif cl_cd_valid and not cp_extraction_successful:
                if attempt < max_retries_on_cp_failure:
                    print(f"[Warning] (Attempt {cp_extraction_attempts_count}) CL/CD valid, but Cp extraction failed: {final_cp_data.get('message', 'Unknown CP error')}. Retrying...")
                    time.sleep(2) # Optional delay
                    # continue to next attempt already handled by loop
                else:
                    print(f"[Error] (Attempt {cp_extraction_attempts_count}) CL/CD valid, but Cp extraction failed after max retries. No more retries.")
                    break # Max retries for CP reached, exit loop
            else: # CL/CD extraction failed or other critical error in output_data
                print(f"[Error] (Attempt {cp_extraction_attempts_count}) CL/CD extraction failed or cfl3d.out reading error: {final_output_data.get('message', 'Unknown CL/CD error')}. Not retrying for this specific condition.")
                break # Critical error, no retry for this, exit loop

        # Result processing using final_stdout, final_stderr, final_output_data, final_cp_data
        output_data = final_output_data # Use the data from the last attempt
        cp_data = final_cp_data         # Use the data from the last attempt

        if cp_data and output_data.get("status") in ["success", "warning"]: # Check status of output_data (CL/CD part)
            if cp_data.get("status") == "success":
                results_file_path = os.path.join(permanent_run_results_dir, 'cfd_results.json')
                try:
                    results_json_data = {
                        "run_id": run_id,
                        "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
                        "cp_extraction_attempts": cp_extraction_attempts_count, # Add attempt count
                        "calculation_parameters": {
                            "mach": mach, "alpha": alpha, "re": re, "IVISC": IVISC,
                            "advanced_config": advanced_config, "airfoil_name": airfoil_name
                        },
                        "force_coefficients": output_data.get("force_coefficients", {}),
                        "cp_data": {
                            "x_coords": cp_data["x_coords"],
                            "cp_values": cp_data["cp_values"]
                        }
                    }
                    with open(results_file_path, 'w') as f:
                        json.dump(results_json_data, f, indent=4)
                    output_data["results_file_path"] = results_file_path
                    output_data["cp_data_status"] = "success"
                    output_data["cp_data"] = {"points_count": len(cp_data["x_coords"])}
                except Exception as e_json:
                    output_data["cp_data_status"] = "error"
                    output_data["cp_data_message"] = f"保存计算结果到 JSON 文件失败: {str(e_json)}"
            else: # cp_data.get("status") != "success"
                output_data["cp_data_status"] = "error"
                output_data["cp_data_message"] = cp_data.get("message", "Cp extraction failed after all attempts.")
        elif not cp_data and output_data.get("status") in ["success", "warning"]: # cp_data might be None or empty if extraction never really ran
             output_data["cp_data_status"] = "error"
             output_data["cp_data_message"] = "Cp data was not successfully extracted or populated."


        # Decide whether to simplify stdout and stderr (using final_stdout, final_stderr)
        simplified_stdout = final_stdout
        simplified_stderr = final_stderr

        calc_output_status_success = isinstance(output_data, dict) and output_data.get("status") == "success"
        force_coeffs = output_data.get("force_coefficients", {})
        cl_cd_valid_final = force_coeffs.get("cl") is not None and force_coeffs.get("cd") is not None
        cp_data_extracted_successfully_final = output_data.get("cp_data_status") == "success"

        if calc_output_status_success and cl_cd_valid_final:
            if cp_data_extracted_successfully_final:
                simplified_stdout = "CFL3D STDOUT (Calculation successful, CP data extracted, content omitted)"
                simplified_stderr = "CFL3D STDERR (CP data extracted, stderr content omitted or considered non-critical)"
            else:
                simplified_stdout = "CFL3D STDOUT (CL/CD extracted, CP data extraction failed, content omitted)"
                if not final_stderr.strip(): # Check final_stderr
                    simplified_stderr = "CFL3D STDERR (No specific errors reported for CP failure, or stderr empty)"
                elif len(final_stderr) > 100:
                    simplified_stderr = f"CFL3D STDERR (CP extraction failed, original length {len(final_stderr)}, truncated): \\n{final_stderr[:50]}\\n...\\n{final_stderr[-50:]}"
        else:
            if len(final_stdout) > 200:
                simplified_stdout = f"CFL3D STDOUT (length {len(final_stdout)}, truncated): \\n{final_stdout[:100]}\\n...\\n{final_stdout[-100:]}"
            if len(final_stderr) > 200:
                simplified_stderr = f"CFL3D STDERR (length {len(final_stderr)}, truncated): \\n{final_stderr[:100]}\\n...\\n{final_stderr[-100:]}"

        if keep_intermediate_files and os.path.isdir(actual_output_dir_path):
            intermediate_save_dir = os.path.join(permanent_run_results_dir, "intermediate_calc_files")
            os.makedirs(intermediate_save_dir, exist_ok=True)
            files_to_keep = ["surf.g", "surf.q", "field.g", "field.q", "cfl3d.out", "cfl3d.term"]
            print(f"[Info] keep_intermediate_files=True. Copying files from {actual_output_dir_path} to {intermediate_save_dir} (Run ID: {run_id})")
            for file_name_to_keep in files_to_keep:
                src_file_to_keep = os.path.join(actual_output_dir_path, file_name_to_keep)
                dest_file_to_keep = os.path.join(intermediate_save_dir, file_name_to_keep)
                if os.path.exists(src_file_to_keep):
                    try:
                        shutil.copy2(src_file_to_keep, dest_file_to_keep)
                        print(f"  Copied: {file_name_to_keep}")
                    except Exception as e_copy:
                        print(f"  Failed to copy {file_name_to_keep}: {e_copy}")
                else:
                    print(f"  Skipped (not found): {file_name_to_keep}")

        return_dict = {
            "status": "success",
            "input_modification": input_modification_result,
            "calculation_output": output_data, # This now contains the final cp_data_status and message
            "run_stdout": simplified_stdout,
            "run_stderr": simplified_stderr,
            "results_directory": actual_output_dir_path,
            "permanent_results_directory": permanent_run_results_dir,
            "results_file_path": output_data.get("results_file_path", None),
            "cp_extraction_attempts": cp_extraction_attempts_count # Add attempt count to return
        }
        # Ensure calculation_output has cp_data_status and cp_data_message if they were set
        if "cp_data_status" not in return_dict["calculation_output"]:
            return_dict["calculation_output"]["cp_data_status"] = final_cp_data.get("status", "unknown")
        if "cp_data_message" not in return_dict["calculation_output"]:
            return_dict["calculation_output"]["cp_data_message"] = final_cp_data.get("message", "No CP message.")

        return return_dict

    except Exception as e:
        import traceback
        # Ensure cp_extraction_attempts_count is reported even in case of early general exception
        error_message = f"CFL3D计算过程中出错: {str(e)}\\n{traceback.format_exc()}"

        # Try to keep intermediate files on error, using the last known actual_output_dir_path
        if keep_intermediate_files and os.path.isdir(actual_output_dir_path): # Check actual_output_dir_path exists
            error_intermediate_save_dir = os.path.join(permanent_run_results_dir, "intermediate_calc_files_on_error")
            os.makedirs(error_intermediate_save_dir, exist_ok=True)
            files_to_keep_on_error = ["surf.g", "surf.q", "field.g", "field.q", "cfl3d.out", "cfl3d.term"]
            print(f"[Info] Error occurred ({str(e)[:50]}...). keep_intermediate_files=True. Attempting to copy files from {actual_output_dir_path} to {error_intermediate_save_dir}")
            for file_name_on_error in files_to_keep_on_error:
                src_file_on_error = os.path.join(actual_output_dir_path, file_name_on_error)
                dest_file_on_error = os.path.join(error_intermediate_save_dir, file_name_on_error)
                if os.path.exists(src_file_on_error):
                    try:
                        shutil.copy2(src_file_on_error, dest_file_on_error)
                        print(f"  Copied on error: {file_name_on_error}")
                    except Exception as e_copy_error:
                        print(f"  Failed to copy {file_name_on_error} on error: {e_copy_error}")
                else:
                    print(f"  Skipped on error (not found): {file_name_on_error}")

        return {
            "status": "error",
            "message": error_message,
            "cp_extraction_attempts": cp_extraction_attempts_count if cp_extraction_attempts_count > 0 else 1 # ensure it's at least 1 if error before loop
        }

    finally:
        # The unique_run_dir (parent of actual_output_dir_path) should be cleaned up.
        # This is because actual_output_dir_path is cleaned inside the loop for retries.
        # And unique_run_dir contains the primary input files like cfl3d.inp copied from cgrid step.
        # If keep_intermediate_files is True, the useful files from the *last* actual_output_dir_path
        # have already been copied to permanent_run_results_dir.
        # So, deleting unique_run_dir at the end is generally desired to clean up the cfl3d/run_xxxx folders.
        if os.path.isdir(unique_run_dir): # Changed from actual_output_dir_path to unique_run_dir
            try:
                # Make sure we are not deleting the permanent_results_dir or its subdirectories by mistake
                if not unique_run_dir.startswith(os.path.abspath(permanent_results_dir)):
                    shutil.rmtree(unique_run_dir)
                    print(f"[清理] 在 finally 块中已删除唯一运行目录: {unique_run_dir}")
                else:
                    print(f"[警告] 在 finally 块中: 跳过删除 {unique_run_dir} 因为它似乎与永久结果目录冲突。")

            except Exception as e_final_clean:
                print(f"[警告] 在 finally 块中删除唯一运行目录 {unique_run_dir} 失败: {e_final_clean}")
        else:
            print(f"[信息] 在 finally 块中: 唯一运行目录 {unique_run_dir} 未找到或不是一个目录，无需清理。")

def modify_cfl3d_input(file_path, mach_value, alpha_value, re_value, IVISC=7, advanced_config=None):
    """
    修改CFL3D输入文件中的参数
    """
    try:
        with open(file_path, 'r') as file:
            lines = file.readlines()

        # --- BEGIN MANUAL MODIFICATIONS BASED ON cfl3d_test/cfl3d.inp ---

        # 1. FILES section modifications
        # Replace plot3d_grid.xyz with field.g and plot3d_sol.bin with field.q
        # Add specific lines after cfl3d.restart
        new_files_section_lines = []
        in_files_section = False
        files_section_processed = False
        cfl3d_restart_found = False

        temp_lines = []
        for i, line in enumerate(lines):
            if "FILES:" in line:
                in_files_section = True
                temp_lines.append(line)
                continue

            if in_files_section and not files_section_processed:
                if "plot3d_grid.xyz" in line:
                    temp_lines.append("field.g\n")
                    continue
                if "plot3d_sol.bin" in line:
                    temp_lines.append("field.q\n")
                    continue
                if "cfl3d.restart" in line:
                    temp_lines.append(line)
                    cfl3d_restart_found = True
                    # Check if the next lines are already the ones we want to add
                    # This is a basic check, might need to be more robust
                    if not (i + 1 < len(lines) and ">" in lines[i+1]):
                        temp_lines.append(">\n")
                        temp_lines.append("cprec 0\n")
                        temp_lines.append("ides 0\n")
                        temp_lines.append("iblend 0\n")
                        temp_lines.append("iteravg 0\n")
                        temp_lines.append("icoarsemovie 0\n")
                        temp_lines.append("i2dmovie 0\n")
                        temp_lines.append("isample 0\n")
                        temp_lines.append("<\n")
                    # Mark files section as processed to avoid adding these lines multiple times
                    # if this function is called again on an already modified file.
                    # However, the logic to properly end the files section is tricky.
                    # We assume "CFL3D V6 INPUT FILE" marks the end.
                elif "CFL3D V6 INPUT FILE" in line and cfl3d_restart_found:
                    files_section_processed = True
                    in_files_section = False
                    temp_lines.append(line) # Add the current line that ends the section
                    continue
                elif not ("CFL3D V6 INPUT FILE" in line) and in_files_section : # keep other files
                    temp_lines.append(line)
                else: # End of files section or other condition
                    in_files_section = False
                    files_section_processed = True # Should be true if no longer in files section
                    temp_lines.append(line) # Add current line
            else:
                temp_lines.append(line)
        lines = temp_lines


        # 2. Modify XMACH, ALPHA, REUE,MIL (Basic Parameters)
        for i, line in enumerate(lines):
            if "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY" in line:
                values = lines[i + 1].split()
                reue = re_value / 1e6

                # Format according to cfl3d_test.inp
                # Assuming mach_value and alpha_value are floats
                values[0] = f"{mach_value:.3f}"    # XMACH (e.g., 0.750)
                values[1] = f"{alpha_value:.3f}"   # ALPHA (e.g., 1.000)
                # BETA (values[2]) is not changed by default by this function
                values[3] = f"{reue:.2f}"      # REUE (e.g., 3.00)
                # TINF,DR (values[4]) is not changed by default
                # IALPH (values[5]) is not changed by default
                # IHSTRY (values[6]) is not changed by default

                # Reconstruct the line with appropriate spacing.
                # CFL3D can be sensitive to spacing. The test file uses varied spacing.
                # A robust way is to ensure fixed-width columns or sufficient spacing.
                # For now, let's try to mimic the test file's visual spacing somewhat.
                lines[i + 1] = f"{values[0]:>9s} {values[1]:>9s} {values[2]:>9s} {values[3]:>9s} {values[4]:>9s} {values[5]:>9s} {values[6]:>9s}\n"
                break

        # 3. Modify DT, IFLAGTS, FMAX
        for i, line in enumerate(lines):
            if "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU" in line:
                values = lines[i + 1].split()
                values[0] = "-1.50"    # DT
                # IREST (values[1]) - not specified as changed from default tool behavior
                values[2] = "000"      # IFLAGTS (using 000 as in test file)
                values[3] = "1.00"     # FMAX
                # IUNST (values[4]) - not specified as changed
                # CFLTAU (values[5]) - not specified as changed
                lines[i + 1] = f"{values[0]:>9s} {values[1]:>9s} {values[2]:>9s} {values[3]:>9s} {values[4]:>9s} {values[5]:>9s}\n"
                break

        # 4. Modify NGRID, NPLOT3D, NPRINT, NWREST, ICHK, I2D, NTSTEP, ITA
        for i, line in enumerate(lines):
            if "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA" in line:
                values = lines[i+1].split()
                # NGRID (values[0]) - not specified
                # NPLOT3D (values[1]) - not specified
                # NPRINT (values[2]) - not specified
                values[3] = "500"      # NWREST
                # ICHK (values[4]) - not specified
                values[5] = "0"        # I2D
                # NTSTEP (values[6]) - not specified (was 1, stays 1 based on comparison)
                values[7] = "-2"       # ITA
                lines[i+1] = f"{values[0]:>9s} {values[1]:>9s} {values[2]:>9s} {values[3]:>9s} {values[4]:>9s} {values[5]:>9s} {values[6]:>9s} {values[7]:>9s}\n"
                break

        # 5. Modify IVISC(I), IVISC(J), IVISC(K)
        # The default IVISC parameter for the function is now 7.
        # This part of the code should correctly use the IVISC parameter (defaulting to 7).
        for i, line in enumerate(lines):
            if "IVISC(I)  IVISC(J)  IVISC(K)" in line: # Title line
                param_line_idx = i + 1
                if param_line_idx < len(lines):
                    values = lines[param_line_idx].split()
                    if len(values) >= 7: # NCG, IEM, IADVANCE, IFORCE, IVISC(I), IVISC(J), IVISC(K)
                        values[4] = str(IVISC)  # IVISC(I)
                        values[5] = str(IVISC)  # IVISC(J)
                        values[6] = str(IVISC)  # IVISC(K)
                        lines[param_line_idx] = f"{values[0]:>9s} {values[1]:>9s} {values[2]:>9s} {values[3]:>9s} {values[4]:>9s} {values[5]:>9s} {values[6]:>9s}\n"
                break

        # 6. Modify BCTYPE for J0, JDIM, KDIM sections
        # This is more complex and requires careful state tracking or regex.
        # Assuming a simple structure for now.
        in_J0 = False
        in_JDIM = False
        in_KDIM_segment1 = False # KDIM has multiple segments, target the first after "KDIM:"

        for i, line in enumerate(lines):
            if "J0:   GRID   SEGMENT    BCTYPE" in line:
                in_J0 = True
                in_JDIM = False
                in_KDIM_segment1 = False
                continue
            if "JDIM: GRID   SEGMENT    BCTYPE" in line:
                in_J0 = False
                in_JDIM = True
                in_KDIM_segment1 = False
                continue
            if "KDIM: GRID   SEGMENT    BCTYPE" in line: # This is the header for the KDIM parameters
                in_J0 = False
                in_JDIM = False
                # We are interested in the BCTYPE for the *first segment* under KDIM
                # The line directly after "KDIM: GRID..." is usually the first data line for KDIM.
                # And the *first* BCTYPE to change here is on its own line.
                # Example: KDIM: ... \n  1  1  1000 ... (target this 1000)
                # We need to find the data line that corresponds to the first segment and change its BCTYPE.
                # The test file has "1 1 1003" for KDIM's first segment BCTYPE.
                # The problematic file has "1 1 1000".
                # Let's search for the line that starts with "1         1      1000" under KDIM section.
                # This needs to be more robust. For now, we look for the first data line after KDIM.
                if i + 1 < len(lines):
                    kdim_data_line_parts = lines[i+1].split()
                    if len(kdim_data_line_parts) > 2 and kdim_data_line_parts[2] == "1000": # Check if BCTYPE is 1000
                         kdim_data_line_parts[2] = "1003"
                         lines[i+1] = "   ".join(kdim_data_line_parts) + "\n" # Basic join, may need f-string for spacing
                in_KDIM_segment1 = True # Set this true after processing the first segment's BCTYPE for KDIM
                continue # Continue to avoid processing this line further by other BCTYPE logic

            if in_J0 and line.strip().startswith("1         1      1000"): # Example from problematic file
                lines[i] = line.replace("1000", "1003", 1)
                in_J0 = False # Assume only one such line per section for simplicity
                continue
            if in_JDIM and line.strip().startswith("1         1      1000"):
                lines[i] = line.replace("1000", "1003", 1)
                in_JDIM = False
                continue
            # KDIM BCTYPE for the first segment is handled above. Other segments might have different BCTYPES.
            # The test file's K0 section has BCTYPE 0, 2004, 0.
            # The KDIM section (after KDIM: header) has BCTYPE 1003. We've handled this.

        # 7. Modify NCYC (first level)
        ncyc_header_found = False
        for i, line in enumerate(lines):
            if "NCYC    MGLEVG     NEMGL     NITFO" in line:
                ncyc_header_found = True
                continue
            if ncyc_header_found and line.strip(): # Check if line is not empty
                values = line.split()
                if len(values) > 0: # Ensure there are values
                    # Assuming this is the first NCYC line (MGLEVG=1)
                    if len(values) >= 4 and values[1] == "1": # Check MGLEVG is 1
                        values[0] = "500" # NCYC for MGLEVG=1
                        lines[i] = f"{values[0]:>9s} {values[1]:>9s} {values[2]:>9s} {values[3]:>9s}\n"
                        break # Modified first level, exit loop for NCYC
                # If we don't break, it might alter subsequent NCYC lines if they also match "strip()"

        # 8. PLOT3D OUTPUT IPTYPE and IMOVIE to UPDATE
        plot3d_output_header_found = False
        imovie_line_idx = -1
        for i, line in enumerate(lines):
            if "PLOT3D OUTPUT:" in line:
                plot3d_output_header_found = True
                continue
            if plot3d_output_header_found and "GRID IPTYPE" in line: # This is the data line header
                if i + 1 < len(lines):
                    data_line_values = lines[i+1].split()
                    if len(data_line_values) > 1: # Grid number, IPTYPE, etc.
                        data_line_values[1] = "-2" # IPTYPE
                        # Reconstruct with potentially fixed spacing
                        lines[i+1] = f"{data_line_values[0]:>7s} {data_line_values[1]:>7s} {data_line_values[2]:>7s} {data_line_values[3]:>7s} {data_line_values[4]:>7s} {data_line_values[5]:>7s} {data_line_values[6]:>7s} {data_line_values[7]:>7s} {data_line_values[8]:>7s} {data_line_values[9]:>7s} {data_line_values[10]:>7s}\n"
                plot3d_output_header_found = False # Processed, reset flag
                # Now look for IMOVIE immediately after or nearby
                # Search for IMOVIE and its value to replace them
                for j in range(i + 2, min(i + 5, len(lines))): # Search a few lines ahead
                    if "IMOVIE" in lines[j]:
                        lines[j] = " UPDATE\n" # Note the leading space to align like test file
                        if j + 1 < len(lines):
                            lines[j+1] = "   500\n" # Value for UPDATE
                        break # Found and replaced IMOVIE section
                break # Exit PLOT3D loop once processed

        # 9. Add SAMPLING POINTS and CONTROL SURFACE at the end if not present
        # Check if the last few lines already contain these sections to avoid duplication
        file_content_str = "".join(lines)
        if "SAMPLING POINTS:" not in file_content_str:
            lines.append(" SAMPLING POINTS:\n")
            lines.append(" NSMP\n")
            lines.append("    0\n") # Note: indentation from test file
            lines.append("  GRID  IPTYPE  ISTART  IEND  IINC  JSTART  JEND  JINC  KSTART  KEND  KINC\n") # Header, actual values usually 0 for NSMP 0

        if "CONTROL SURFACE:" not in file_content_str:
            lines.append(" CONTROL SURFACE:\n")
            lines.append(" NCS\n")
            lines.append("   0\n") # Note: indentation from test file
            lines.append("  GRID  ISTART    IEND  JSTART    JEND  KSTART    KEND   IWALL   INROM\n") # Header

        # --- END MANUAL MODIFICATIONS ---

        # 处理高级配置参数 (original advanced_config logic)
        # This should be called AFTER our manual fixed modifications,
        # in case advanced_config wants to override some of them.
        if advanced_config:
            lines = apply_advanced_config(lines, advanced_config) # Assuming apply_advanced_config exists and works correctly

        with open(file_path, 'w') as file:
            file.writelines(lines)

        return {"status": "success", "message": "输入参数修改成功，并应用了基于 cfl3d_test.inp 的修正。"}

    except Exception as e:
        import traceback
        return {"status": "error", "message": f"修改输入参数失败: {str(e)}\n{traceback.format_exc()}"}

def run_cfl3d(source_dir, output_folder_name, cfl3d_exe_root_dir, num_processes=2):
    """
    运行CFL3D计算 (使用 MPI 版本)

    参数:
        source_dir: 包含 cfl3d.inp, cfl3d.xyz, RangeAndHeight.dat 的目录。
        output_folder_name: 在 source_dir 中创建的输出文件夹的相对名称。
        cfl3d_exe_root_dir: 用于定位 CFL3D MPI 可执行文件的根目录。
    返回:
        计算程序的标准输出和标准错误
    """
    execution_dir_path = os.path.join(source_dir, output_folder_name)
    if not os.path.exists(execution_dir_path):
        os.makedirs(execution_dir_path)
    print(f"[Info] CFL3D execution will occur in: {execution_dir_path}")

    files_to_copy = ['cfl3d.inp', 'cfl3d.xyz', 'RangeAndHeight.dat']
    for file_name in files_to_copy:
        src_file = os.path.join(source_dir, file_name)
        dest_file = os.path.join(execution_dir_path, file_name)
        if os.path.exists(src_file):
            try:
                 shutil.copy(src_file, dest_file)
                 print(f"[DEBUG] Copied {src_file} to {dest_file}")
            except Exception as copy_e:
                 print(f"[Warning] Failed to copy {src_file} to {dest_file}: {copy_e}")
                 if file_name in ['RangeAndHeight.dat', 'cfl3d.xyz', 'cfl3d.inp']:
                      raise IOError(f"Required file '{file_name}' exists at {src_file} but could not be copied to execution directory {execution_dir_path}.") from copy_e
        else:
            error_msg = f"错误：必需的输入文件 '{src_file}' 未在源目录中找到。CFL3D无法运行。"
            print(f"[ERROR] {error_msg}")
            raise FileNotFoundError(error_msg)

    base_run_dir_for_mpi_exe = os.getcwd()
    cfl3d_mpi_relative_path_from_cwd = '../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi'

    # Consider making MPI executable path relative to cfl3d_exe_root_dir or more configurable
    # For now, using the original logic based on CWD:
    cfl3d_mpi_executable_absolute = os.path.abspath(os.path.join(base_run_dir_for_mpi_exe, cfl3d_mpi_relative_path_from_cwd))
    print(f"[DEBUG] run_cfl3d: Looking for MPI executable at absolute path (based on CWD): {cfl3d_mpi_executable_absolute}")

    cmd_input = 'y\n'
    #num_processes = 4

    try:
        if not os.path.exists(cfl3d_mpi_executable_absolute):
             if os.path.exists(cfl3d_mpi_executable_absolute + '.exe'):
                  cfl3d_mpi_executable_absolute += '.exe'
             else:
                  raise FileNotFoundError(f"CFL3D MPI executable not found at the specified path: {cfl3d_mpi_executable_absolute} (derived from relative path '{cfl3d_mpi_relative_path_from_cwd}' and CWD '{base_run_dir_for_mpi_exe}')")

        mpi_command = ['mpirun', '-np', str(num_processes), cfl3d_mpi_executable_absolute]
        print(f"[DEBUG] run_cfl3d: Executing command: {' '.join(mpi_command)} in CWD: {execution_dir_path}")

        p = subprocess.Popen(
            mpi_command,
            cwd=execution_dir_path,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        try:
            # 增加超时时间到1800秒（30分钟），但建议通过配置控制迭代次数
            timeout_seconds = int(os.environ.get("CFL3D_SINGLE_JOB_TIMEOUT", 1800))
            stdout_bytes, stderr_bytes = p.communicate(input=cmd_input.encode('ascii'), timeout=timeout_seconds)
            stdout = stdout_bytes.decode('latin-1', errors='ignore')
            stderr = stderr_bytes.decode('latin-1', errors='ignore')
            print(f"[DEBUG] run_cfl3d: MPI process finished with return code {p.returncode}")
            if p.returncode != 0:
                 print(f"[Warning] run_cfl3d: MPI process exited with non-zero code {p.returncode}.")
                 print(f"  Stderr (decoded with latin-1, errors ignored): {stderr}")
            return stdout, stderr
        except subprocess.TimeoutExpired:
             p.kill()
             stdout_bytes, stderr_bytes = p.communicate()
             stdout = stdout_bytes.decode('latin-1', errors='ignore')
             stderr = stderr_bytes.decode('latin-1', errors='ignore')
             raise TimeoutError(f"CFL3D MPI process timed out after {timeout_seconds} seconds. Last stdout: {stdout}, Last stderr: {stderr}")

    except FileNotFoundError as fnf_error:
         print(f"[ERROR] run_cfl3d: {fnf_error}")
         if 'mpirun' in str(fnf_error):
             print("[ERROR] 'mpirun' command not found. Ensure MPI environment is loaded/configured correctly.")
         raise
    except Exception as e:
        import traceback
        print(f"[ERROR] Exception during MPI subprocess execution in run_cfl3d:")
        traceback.print_exc()
        raise Exception(f"运行CFL3D MPI计算程序时出错: {str(e)}")

def read_cfl3d_output(results_dir_path):
    """
    读取CFL3D输出文件
    Args:
        results_dir_path: The absolute path to the directory containing cfl3d.out
    """
    output_file = os.path.join(results_dir_path, 'cfl3d.out')

    if os.path.exists(output_file):
        try:
            with open(output_file, 'r') as f:
                content = f.readlines()

            start_indices = [i + 1 for i, line in enumerate(content)
                            if "***** FORCE AND MOMENT SUMMARIES - FINAL TIME STEP/MULTIGRID CYCLE *****" in line]

            if not start_indices:
                last_20_lines = ''.join(content[-20:]) if len(content) >= 20 else ''.join(content)
                return {"status": "warning", "message": "未找到结果标记行，可能计算未收敛", "data": last_20_lines, "force_coefficients": {"cl": None, "cd": None}}

            start_index = start_indices[-1]
            extracted_lines = content[start_index:]
            extracted_text = ''.join(extracted_lines)
            cl, cd = extract_force_coefficients(extracted_text)

            result = {
                "status": "success",
                #"data": extracted_text,
                "force_coefficients": {
                    "cl": cl,
                    "cd": cd
                }
            }
            return result

        except Exception as e:
            return {"status": "error", "message": f"读取输出文件失败: {str(e)}", "force_coefficients": {"cl": None, "cd": None}}
    else:
        cfl3d_term_path = os.path.join(results_dir_path, 'cfl3d.term')
        if os.path.exists(cfl3d_term_path):
            with open(cfl3d_term_path, 'r') as f_term:
                term_content = f_term.read()
            return {"status": "error", "message": f"找不到主输出文件 {output_file}，但找到 cfl3d.term。可能是CFL3D调用出现问题。cfl3d.term content: \n{term_content[:1000]}", "force_coefficients": {"cl": None, "cd": None}}
        return {"status": "error", "message": f"找不到输出文件 {output_file}，可能是CFL3D调用出现了问题", "force_coefficients": {"cl": None, "cd": None}}

def extract_force_coefficients(output_text):
    """
    从CFL3D输出文本中提取最终的全局升力系数(CL)和阻力系数(CD)。
    查找 "SUMMARY OF FORCES AND MOMENTS - ALL GLOBAL BLOCKS" 部分。
    """
    cl = None
    cd = None
    in_global_summary = False
    lines = output_text.splitlines() # 按行分割

    for i, line in enumerate(lines):
        # 定位到全局力矩总结部分
        if "SUMMARY OF FORCES AND MOMENTS - ALL GLOBAL BLOCKS" in line:
            in_global_summary = True
            continue # 继续到下一行查找标题

        # 在全局总结部分查找包含 "CL" 和 "CD" 的标题行
        # （注意：这里假设标题行格式相对固定，包含这些大写字母）
        if in_global_summary and "CL" in line and "CD" in line and "CDp" in line:
            # 找到了标题行，下一行应该是数据
            if i + 1 < len(lines):
                data_line = lines[i + 1].strip()
                values = data_line.split()
                # 假设 CL 是第一个值，CD 是第二个值
                if len(values) >= 2:
                    try:
                        cl = float(values[0])
                        cd = float(values[1])
                        # 找到后即可退出循环，因为只需要最终结果
                        print(f"[DEBUG] extract_force_coefficients: Found CL={cl}, CD={cd}")
                        break
                    except ValueError:
                        print(f"[Warning] extract_force_coefficients: Failed to parse values in data line: {data_line}")
                        # 如果解析失败，可能格式有变，继续查找以防万一
                        pass
            else:
                # 找到了标题行但没有下一行数据
                 print("[Warning] extract_force_coefficients: Found header line but no data line followed.")
                 break # 停止查找


        # 如果已经离开了全局总结部分（例如遇到下一个 ***** 分隔符或其他标记），则停止查找
        # （这是一个可选的优化，防止在文件末尾误解析）
        # elif in_global_summary and "*****" in line:
        #    break

    if cl is None or cd is None:
         print("[Warning] extract_force_coefficients: Could not extract CL or CD from the global summary block.")


    return cl, cd

def apply_advanced_config(lines, advanced_config):
    """
    应用高级配置参数到输入文件

    Args:
        lines: 输入文件的行列表
        advanced_config: 高级配置字典

    Returns:
        修改后的行列表
    """
    # 详细的高级配置参数映射
    param_map = {
        # 流动参数
        "beta": {
            "marker": "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY",
            "index": 2,
            "default": 0.0,
            "description": "侧滑角，单位为度"
        },
        "tinf": {
            "marker": "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY",
            "index": 4,
            "default": 460.0,
            "description": "自由流温度，单位为兰肯温度(°R)"
        },

        # 时间步长和求解控制
        "dt": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 0,
            "default": -0.8,
            "description": "时间步长，负值表示局部时间步长"
        },
        "irest": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 1,
            "default": 0,
            "description": "重启标志，0表示新计算，1表示从之前结果重启"
        },
        "iflagts": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 2,
            "default": 2000,
            "description": "最大时间步数"
        },
        "fmax": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 3,
            "default": 5.0,
            "description": "允许的最大CFL数"
        },
        "cfltau": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 5,
            "default": 7.5,
            "description": "CFL数调整系数"
        },

        # 参考值
        "sref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 0,
            "default": 1.0,
            "description": "参考面积，用于系数计算（一般二维翼型即为1）"
        },
        "cref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 1,
            "default": 1.0,
            "description": "参考弦长，用于力矩计算（一般二维翼型即为1）"
        },
        "bref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 2,
            "default": 1.0,
            "description": "参考展长，用于力矩计算（一般二维翼型即为1）"
        },
        "xmc": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 3,
            "default": 0.25,
            "description": "力矩参考点X坐标（一般二维翼型即为0.25）"
        },

        # 网格和计算控制
        "nplot3d": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 1,
            "default": 1,
            "description": "PLOT3D输出频率，正值表示每n步输出一次，0表示不输出"
        },
        "nprint": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 2,
            "default": -1,
            "description": "收敛信息打印频率，负值表示简化输出"
        },
        "nwrest": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 3,
            "default": 5000,
            "description": "重启文件写入频率"
        },
        "i2d": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 5,
            "default": 1,
            "description": "二维计算标志，1表示二维计算，0表示三维计算"
        },

        # 数值方法控制
        "iem": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 1,
            "default": 0,
            "description": "通量计算方法，0表示Roe方法，其他值对应不同的通量计算方案"
        },
        "iadvance": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 2,
            "default": 0,
            "description": "推进方法，0表示显式方法，1表示隐式方法"
        },
        "iforce": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 3,
            "default": 333,
            "description": "力和力矩计算频率"
        },

        # 通量限制器
        "iflim_i": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 3,
            "default": 4,
            "description": "I方向通量限制器类型，4表示van Albada限制器"
        },
        "iflim_j": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 4,
            "default": 4,
            "description": "J方向通量限制器类型，4表示van Albada限制器"
        },
        "iflim_k": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 5,
            "default": 4,
            "description": "K方向通量限制器类型，4表示van Albada限制器"
        },

        # 人工黏性系数
        "rkap0_i": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 3,
            "default": 0.3333,
            "description": "I方向人工黏性系数"
        },
        "rkap0_j": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 4,
            "default": 0.3333,
            "description": "J方向人工黏性系数"
        },
        "rkap0_k": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 5,
            "default": 0.3333,
            "description": "K方向人工黏性系数"
        },

        # 多重网格参数
        "mseq": {
            "marker": "MSEQ    MGFLAG    ICONSF       MTT      NGAM",
            "index": 0,
            "default": 3,
            "description": "多重网格层数，通常为2-3"
        },
        "mgflag": {
            "marker": "MSEQ    MGFLAG    ICONSF       MTT      NGAM",
            "index": 1,
            "default": 1,
            "description": "多重网格标志，1表示启用多重网格"
        },

        # 收敛控制
        "ncyc_level1": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 0,  # 第一个NCYC行
            "default": 1000,
            "description": "第一层网格最大迭代次数"
        },
        "ncyc_level2": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 1,  # 第二个NCYC行
            "default": 1000,
            "description": "第二层网格最大迭代次数"
        },
        "ncyc_level3": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 2,  # 第三个NCYC行
            "default": 1000,
            "description": "第三层网格最大迭代次数"
        }
    }

    # 处理每个高级参数
    for param_name, param_value in advanced_config.items():
        if param_name in param_map:
            config = param_map[param_name]
            marker = config["marker"]
            index = config["index"]
            offset = config.get("offset", 0)  # 处理多行同标记的情况

            # 查找参数所在行
            marker_lines = []
            for i, line in enumerate(lines):
                if marker in line:
                    marker_lines.append(i)

            if marker_lines and len(marker_lines) > offset:
                line_idx = marker_lines[offset]
                values = lines[line_idx + 1].split()
                if index < len(values):
                    # 设置参数值
                    values[index] = f"{param_value}"
                    lines[line_idx + 1] = "   ".join(values) + "\n"

    return lines

def extract_cp_from_cfl3d(results_dir_path):
    """
    从CFL3D计算结果的 surf.g 和 surf.q 文件中提取压力系数(Cp)数据。

    参数:
        results_dir_path: 包含 surf.g 和 surf.q 文件的目录路径

    返回:
        包含x坐标和cp值的字典
    """
    CP_INDEX_IN_SURF_Q = 6  # Cp 在 surf.q 中的索引 (0起始, U_X U_Y U_Z P T M CP ...)
    SURFACE_GRID_FILENAME = "surf.g"
    SURFACE_SOL_FILENAME = "surf.q"

    full_grid_path = os.path.join(results_dir_path, SURFACE_GRID_FILENAME)
    full_sol_path = os.path.join(results_dir_path, SURFACE_SOL_FILENAME)

    if not os.path.exists(full_grid_path):
        return {"status": "error", "message": f"表面网格文件 {full_grid_path} 未找到。"}
    if not os.path.exists(full_sol_path):
        return {"status": "error", "message": f"表面解文件 {full_sol_path} 未找到。"}

    try:
        # 调用 cfdpost.cfdresult.cfl3d.readsurf2d
        # _double_precision 默认为 False, binary 默认为 True
        xyz_blocks, qq_blocks = cfl3d.readsurf2d(
            results_dir_path,
            fname_grid=SURFACE_GRID_FILENAME,
            fname_sol=SURFACE_SOL_FILENAME
        )

        if not xyz_blocks or not qq_blocks:
            return {"status": "error", "message": f"未能从 {SURFACE_GRID_FILENAME} 和 {SURFACE_SOL_FILENAME} 读取到有效数据块。"}

        # 假设我们主要关心第一个数据块
        if not xyz_blocks[0].size or not qq_blocks[0].size:
            return {"status": "error", "message": "第一个数据块为空或无效。"}

        xy_block = xyz_blocks[0]  # Shape: (ni, nj, nk, 3)
        qq_block = qq_blocks[0]  # Shape: (ni, nj, nk, num_vars)

        ni, nj, nk, coord_dims = xy_block.shape
        sol_ni, sol_nj, sol_nk, sol_dims = qq_block.shape

        if sol_dims <= CP_INDEX_IN_SURF_Q:
            return {"status": "error", "message": f"解文件中的变量数量 ({sol_dims}) 不足以包含Cp (期望索引 {CP_INDEX_IN_SURF_Q})。"}

        x_coords_np = None
        y_coords_np = None
        cp_values_np = None

        # nk 和 sol_nk 必须为1，代表是表面数据
        if nk != 1 or sol_nk != 1:
            return {"status": "error", "message": f"表面数据文件的 nk（厚度方向点数）应为1。得到 grid_nk={nk}, sol_nk={sol_nk}。"}

        # 处理常见的二维翼型数据在surf.g/q中的存储方式
        if ni == 1 and nj > 0:  # 点沿 J 方向, 单个 I-profile
            x_coords_np = xy_block[0, :, 0, 0]  # X-coord at k=0
            y_coords_np = xy_block[0, :, 0, 1]  # Y-coord at k=0
            cp_values_np = qq_block[0, :, 0, CP_INDEX_IN_SURF_Q]
        elif nj == 1 and ni > 0:  # 点沿 I 方向, 单个 J-profile
            x_coords_np = xy_block[:, 0, 0, 0]
            y_coords_np = xy_block[:, 0, 0, 1]
            cp_values_np = qq_block[:, 0, 0, CP_INDEX_IN_SURF_Q]
        elif ni > 1 and nj > 0: # 可能代表多个I-profiles (例如，上表面一个I, 下表面一个I)
                                # 或者一个profile沿J方向有nj个点，这样的profile有ni个
                                # 为了奖励函数比较，通常需要一个连续的Cp分布。
                                # 此处取第一个I-profile (i=0)的数据。
            print(f"[Info] extract_cp_from_cfl3d: 块数据维度 ({ni},{nj},{nk}). 提取第一个I-profile (i=0) 的数据。")
            x_coords_np = xy_block[0, :, 0, 0]
            y_coords_np = xy_block[0, :, 0, 1]
            cp_values_np = qq_block[0, :, 0, CP_INDEX_IN_SURF_Q]
        # 可以为 elif nj > 1 and ni > 0: 添加类似逻辑，但通常翼型数据会是以上几种模式之一
        else:
            return {"status": "error", "message": f"块维度 ({ni},{nj},{nk}) 不适合提取标准的翼型Cp数据。期望ni=1或nj=1（或其中之一大于1代表点数）。"}

        if x_coords_np is None or y_coords_np is None or cp_values_np is None:
             return {"status": "error", "message": "未能从解析出的块数据中提取x, y坐标或Cp值。"}

        x_coords_list = x_coords_np.tolist()
        y_coords_list = y_coords_np.tolist()
        cp_values_list = cp_values_np.tolist()

        points = [{"x": x, "y": y, "cp": cp} for x, y, cp in zip(x_coords_list, y_coords_list, cp_values_list)]

        return {
            "status": "success",
            "method": "plot3d",
            #"points": points,
            "x_coords": x_coords_list,
            "cp_values": cp_values_list
        }

    except FileNotFoundError as fnf_error:
        return {"status": "error", "message": f"读取surf文件时发生文件未找到错误: {str(fnf_error)}"}
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"从surf.g/q提取Cp数据时发生未知错误: {str(e)}\n{traceback.format_exc()}"
        }

def process_foil_file(input_foil_file, output_foil_file, num_points=1001, visualize=False,
                      n_base=8, n_diff=20, n_enhanced=3):
    """
    处理翼型文件，使用增强CST方法重构曲线并保存为特定格式。

    Args:
        input_foil_file (str): 输入翼型文件的路径。
        output_foil_file (str): 输出翼型文件的路径。
        num_points (int): 输出翼型曲线的点数（每个表面）。
        visualize (bool): 是否可视化处理结果。
        n_base (int): 基础翼型CST拟合阶数。
        n_diff (int): 差异曲线CST拟合阶数。
        n_enhanced (int): 用于增强前缘的差异参数数量。

    Returns:
        dict: 处理结果状态和消息。
    """
    try:
        if os.path.exists(output_foil_file):
            os.remove(output_foil_file)
        print(f"[Info] 已删除旧的输出文件: {output_foil_file}")
        # 1. 读取原始坐标
        xu_orig, yu_orig, xl_orig, yl_orig = read_coordinates(input_foil_file)

        # 2. 使用增强CST方法拟合和生成新坐标
        # 注意： enhanced_cst_foil_with_fit 内部生成 nn = num_points 个点
        x_new, yu_new, yl_new, t0, R0, _, _, _, _ = enhanced_cst_foil_with_fit(
            xu=xu_orig, yu=yu_orig, xl=xl_orig, yl=yl_orig,
            nn=num_points, # 直接生成所需点数
            t=None, tail=0.0, xn1=0.5, xn2=1.0,
            n_base=n_base, n_diff=n_diff, n_enhanced=n_enhanced
        )

        # 3. 可选：可视化比较
        if visualize:
            plt.figure(figsize=(10, 5))
            # 绘制原始点
            plt.scatter(xu_orig, yu_orig, label='Original Upper', marker='.', color='blue', s=10)
            plt.scatter(xl_orig, yl_orig, label='Original Lower', marker='.', color='red', s=10)
            # 绘制拟合后的曲线
            plt.plot(x_new, yu_new, label=f'Enhanced CST Upper (n_base={n_base}, n_enh={n_enhanced})', color='cyan', linewidth=1)
            plt.plot(x_new, yl_new, label=f'Enhanced CST Lower (n_base={n_base}, n_enh={n_enhanced})', color='magenta', linewidth=1)
            plt.title('Original vs Enhanced CST Fit')
            plt.xlabel('x/c')
            plt.ylabel('y/c')
            plt.axis('equal')
            plt.legend()
            plt.grid(True)
            # 保存在输出文件同目录下
            vis_filename = os.path.join(os.path.dirname(output_foil_file), 'foil_fit_visualization.png')
            plt.savefig(vis_filename, dpi=300)
            plt.close()
            print(f"可视化结果已保存至: {vis_filename}")

        # 4. 保存重构曲线到输出文件 (Tecplot 格式，两区域)
        os.makedirs(os.path.dirname(output_foil_file), exist_ok=True)
        header = "Variables= X Y \n"
        zone_line = f"\nzone i= {num_points}\n" # 每个区域有 num_points 个点

        with open(output_foil_file, 'w') as f:
            f.write(header)
            # 第一个区域：上表面 (x_new, yu_new)
            f.write(zone_line)
            upper_surface_data = np.vstack((x_new, yu_new)).T
            np.savetxt(f, upper_surface_data, fmt='%.8f', comments='') # 提高精度

            # 第二个区域：下表面 (x_new, yl_new)
            f.write(zone_line)
            lower_surface_data = np.vstack((x_new, yl_new)).T
            np.savetxt(f, lower_surface_data, fmt='%.8f', comments='') # 提高精度

        return {"status": "success", "message": "翼型文件使用增强CST处理成功", "t0": t0, "R0": R0}

    except FileNotFoundError:
        return {"status": "error", "message": f"输入翼型文件未找到: {input_foil_file}"}
    except Exception as e:
        import traceback
        print(traceback.format_exc()) # 打印详细错误信息
        return {"status": "error", "message": f"翼型文件处理失败: {str(e)}"}

def modify_cgrid_parameters(cgrid_file_path, params):
    """
    修改Cgrid输入文件中的参数。

    Args:
        cgrid_file_path: Cgrid输入文件路径。
        params: 包含要修改参数的字典。

    Returns:
        dict: 操作结果状态和消息。
    """
    try:
        with open(cgrid_file_path, 'r') as f:
            lines = f.readlines()

        # 确保文件至少有4行（2个标题行 + 2个数据行）
        if len(lines) < 4:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 行数不足 (需要至少4行)"}


        # --- 修改开始: 调整参数映射 ---
        # line_idx 指向实际的数据行索引
        param_map = {
            # 网格尺寸参数 (数据在第 1 行，索引为 1)
            "foil_cells": {"data_line_idx": 1, "index": 0, "default": 151, "description": "翼型上下表面的网格单元数量"},
            "wake_cells": {"data_line_idx": 1, "index": 1, "default": 61, "description": "从翼型尾缘到远场的网格单元数量(流向方向)"},
            "grow_cells": {"data_line_idx": 1, "index": 2, "default": 101, "description": "从翼型表面到远场的网格单元数量(y+方向)"},

            # 物理参数 (数据在第 3 行，索引为 3)
            "y_plus":     {"data_line_idx": 3, "index": 0, "default": 0.9, "description": "指定的y+值"},
            "far_radius": {"data_line_idx": 3, "index": 1, "default": 15.0, "description": "远场半径"},
            "u_inf":      {"data_line_idx": 3, "index": 2, "default": 200.0, "description": "自由流来流速度U_inf(m/s)"},
            "density":    {"data_line_idx": 3, "index": 3, "default": 1.1, "description": "自由流密度rou(kg/m³)"},
            "reynolds":   {"data_line_idx": 3, "index": 4, "default": 3e6, "description": "自由流单位雷诺数Re(/m)"}
        }
        # --- 修改结束 ---

        # 先读取并分割好两个数据行
        try:
             data_values_line1 = lines[1].strip().split()
             data_values_line3 = lines[3].strip().split()
        except IndexError:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 格式错误，无法读取数据行"}


        # 检查数据行是否包含足够的值
        if len(data_values_line1) < 3:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 第一数据行格式错误 (需要至少3个值)"}
        if len(data_values_line3) < 5:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 第二数据行格式错误 (需要至少5个值)"}


        # 修改参数值
        modified = False
        for param_name, param_value in params.items():
            if param_name in param_map:
                config = param_map[param_name]
                data_line_idx = config["data_line_idx"]
                col_idx = config["index"]

                try:
                    if data_line_idx == 1:
                        # 修改第一行数据的值
                        data_values_line1[col_idx] = str(param_value)
                        modified = True
                    elif data_line_idx == 3:
                        # 修改第二行数据的值
                        data_values_line3[col_idx] = str(param_value)
                        modified = True
                except IndexError:
                     # 这个错误理论上不应该发生，因为前面检查了长度，但以防万一
                      print(f"[Warning] modify_cgrid_parameters: 尝试访问索引 {col_idx} 失败，参数 '{param_name}' 未修改。")
                      continue # 跳过这个参数

        # 如果有修改，则重建数据行并写回文件
        if modified:
            try:
                # 重建第一行数据 (保持缩进格式)
                lines[1] = f"                {data_values_line1[0]:<12} {data_values_line1[1]:<5} {data_values_line1[2]:<5}\n" # 调整间距以匹配原格式
                # 重建第二行数据 (保持缩进格式)
                lines[3] = f" {data_values_line3[0]:<19} {data_values_line3[1]:<14} {data_values_line3[2]:<19} {data_values_line3[3]:<17} {data_values_line3[4]:<10}\n" # 调整间距

                with open(cgrid_file_path, 'w') as f:
                    f.writelines(lines)
                return {"status": "success", "message": "Cgrid参数修改成功"}
            except IndexError:
                 # 捕获重建字符串时可能发生的索引错误
                 return {"status": "error", "message": f"修改Cgrid参数失败: 重建数据行时发生错误"}
        else:
             return {"status": "success", "message": "Cgrid参数无需修改 (未提供有效参数)"}


    except FileNotFoundError:
         return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 未找到"}
    except Exception as e:
        import traceback
        print(f"[DEBUG] Error in modify_cgrid_parameters: {e}")
        # traceback.print_exc() # 取消注释以获得更详细的堆栈跟踪
        return {"status": "error", "message": f"修改Cgrid参数失败: {str(e)}"}

def create_default_cgrid_config(cgrid_file_path):
    """
    创建默认的cgrid.txt文件
    """
    with open(cgrid_file_path, 'w') as f:
        f.write("Cell numbers: Foil surface, Wake, Grow\n                151           61    101\n")
        f.write(" y+ critical, Radius far field, Uinf (200 m/s), rou (1.1 kg/m3),    Re (/m)\n 0.9                15.0           200.0              1.1              3e6\n")

def run_cgrid_exe(working_dir, cgrid_exe_parent_dir):
    """
    运行网格生成程序cgrid.exe (或 cgrid)
    Args:
        working_dir: The directory where cgrid.txt and foil.dat are located,
                     and where cfl3d.xyz will be created.
        cgrid_exe_parent_dir: The directory containing the cgrid executable.
    """
    try:
        cgrid_file_path = os.path.join(working_dir, 'cgrid.txt')
        if not os.path.exists(cgrid_file_path):
            print(f"[Warning] cgrid.txt not found in {working_dir}, creating default.")
            create_default_cgrid_config(cgrid_file_path)

        output_grid_file = os.path.join(working_dir, 'cfl3d.xyz')
        if os.path.exists(output_grid_file):
            try:
                os.remove(output_grid_file)
                print(f"[清理] 已删除旧的网格文件: {output_grid_file}")
            except Exception as e:
                print(f"[警告] 删除旧的网格文件失败: {output_grid_file}, error: {e}")

        absolute_cgrid_exe_parent_dir = os.path.abspath(cgrid_exe_parent_dir)
        cgrid_exe_name = None
        if os.path.exists(os.path.join(absolute_cgrid_exe_parent_dir, 'cgrid')):
            cgrid_exe_name = 'cgrid'
        elif os.path.exists(os.path.join(absolute_cgrid_exe_parent_dir, 'cgrid.exe')):
            cgrid_exe_name = 'cgrid.exe'

        if not cgrid_exe_name:
             raise FileNotFoundError(f"Cgrid executable ('cgrid' or 'cgrid.exe') not found in {absolute_cgrid_exe_parent_dir}")

        cgrid_executable_absolute = os.path.join(absolute_cgrid_exe_parent_dir, cgrid_exe_name)
        print(f"[DEBUG] run_cgrid_exe: Trying to execute absolute path: {cgrid_executable_absolute}")

        absolute_working_dir = os.path.abspath(working_dir)
        result = subprocess.run(
            [cgrid_executable_absolute],
            cwd=absolute_working_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=120
        )
        stdout = result.stdout.decode('latin-1', errors='ignore')
        stderr = result.stderr.decode('latin-1', errors='ignore')

        if result.returncode == 0:
            print("[DEBUG] run_cgrid_exe: Process finished successfully.")
            print(f"  Stdout (decoded): {stdout}")
            if os.path.exists(os.path.join(working_dir, 'cfl3d.xyz')):
                print(f"[Info] cfl3d.xyz successfully created in {working_dir}")
                if os.path.exists(os.path.join(working_dir, 'cfl3d.inp')):
                    print(f"[Info] cfl3d.inp found in {working_dir} after cgrid run.")
                else:
                    print(f"[Info] cfl3d.inp NOT found in {working_dir} after cgrid run. (This may be normal)")
            else:
                print(f"[Error] cfl3d.xyz NOT created in {working_dir} despite cgrid success code.")
                return {"status": "error", "message": f"网格生成声称成功但未找到 cfl3d.xyz: {stdout}"}
            return {"status": "success", "message": "网格生成成功"}
        else:
            print(f"[ERROR] run_cgrid_exe: Process failed with code {result.returncode}.")
            print(f"  Stderr (decoded): {stderr}")
            return {"status": "error", "message": f"网格生成失败: {stderr}"}

    except FileNotFoundError as fnf_error:
        print(f"[ERROR] run_cgrid_exe: {fnf_error}")
        return {"status": "error", "message": f"运行网格生成程序时出错: {fnf_error}"}
    except subprocess.TimeoutExpired:
         return {"status": "error", "message": "运行网格生成程序超时"}
    except Exception as e:
        import traceback
        print(f"[ERROR] Exception during subprocess execution in run_cgrid_exe:")
        traceback.print_exc()
        return {"status": "error", "message": f"运行网格生成程序时出错: {str(e)}"}

def mesh_generation_execute(input_foil_file, num_points=1001, visualize=False, grid_params=None,
                            # 添加CST相关的可选参数
                            n_base=8, n_diff=20, n_enhanced=3, airfoil_name: str = None): # 添加 airfoil_name 参数
    """
    执行翼型处理和网格生成

    Args:
        input_foil_file: 输入翼型文件的路径
        num_points: 重构曲线的点数 (每个表面)
        visualize: 是否可视化处理结果
        grid_params: 网格生成参数字典
        n_base, n_diff, n_enhanced: 增强CST拟合参数
        airfoil_name: 翼型的名称 (可选, 用于返回结果)

    Returns:
        处理和网格生成结果信息, 包括 "unique_run_dir" 和 "airfoil_name"
    """
    base_cfl3d_dir = './cfl3d'

    run_id = f"run_{uuid.uuid4().hex[:10]}"
    unique_run_dir = os.path.join(base_cfl3d_dir, run_id)
    os.makedirs(unique_run_dir, exist_ok=True)
    print(f"[Info] Created unique run directory: {unique_run_dir}")

    output_foil_file = os.path.join(unique_run_dir, 'foil.dat')

    source_range_height_dat = os.path.join(base_cfl3d_dir, 'RangeAndHeight.dat')
    dest_range_height_dat = os.path.join(unique_run_dir, 'RangeAndHeight.dat')
    if os.path.exists(source_range_height_dat):
        try:
            shutil.copy(source_range_height_dat, dest_range_height_dat)
            print(f"[Info] Copied RangeAndHeight.dat to {unique_run_dir}")
        except Exception as copy_e:
            print(f"[Warning] Failed to copy RangeAndHeight.dat from {source_range_height_dat} to {dest_range_height_dat}: {copy_e}")
    else:
        print(f"[Warning] Source RangeAndHeight.dat not found at {source_range_height_dat}. This file is required for CFL3D MPI runs.")

    processing_result = process_foil_file(
        input_foil_file, output_foil_file, num_points, visualize,
        n_base=n_base, n_diff=n_diff, n_enhanced=n_enhanced
    )

    if processing_result["status"] != "success":
        return {
            "processing_result": processing_result,
            "grid_generation_result": {"status": "skipped", "message": "翼型处理失败，跳过网格生成"},
            "output_foil_file": output_foil_file,
            "unique_run_dir": unique_run_dir,
            "airfoil_name": airfoil_name # 添加 airfoil_name 到返回结果
        }

    param_result_msg = "未修改Cgrid参数（未提供grid_params）"
    cgrid_file_path = os.path.join(unique_run_dir, 'cgrid.txt')

    if grid_params:
        if not os.path.exists(cgrid_file_path):
            create_default_cgrid_config(cgrid_file_path)

        param_result = modify_cgrid_parameters(cgrid_file_path, grid_params)
        param_result_msg = param_result["message"]
        if param_result["status"] != "success":
             print(f"[警告] 修改Cgrid参数失败: {param_result_msg}，将尝试使用现有参数生成网格。")
    else:
        if not os.path.exists(cgrid_file_path):
            create_default_cgrid_config(cgrid_file_path)
            param_result_msg = "已创建默认Cgrid配置文件。"

    grid_result = run_cgrid_exe(working_dir=unique_run_dir, cgrid_exe_parent_dir=base_cfl3d_dir)

    return {
        "processing_result": processing_result,
        "cgrid_param_modification_result": param_result_msg,
        "grid_generation_result": grid_result,
        "output_foil_file": output_foil_file,
        "unique_run_dir": unique_run_dir,
        "airfoil_name": airfoil_name # 添加 airfoil_name 到返回结果
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--num-processes', type=int, default=2,
                       help='MPI处理器数量')
    args = parser.parse_args()
    num_processes = args.num_processes

    print("--- 开始执行完整流程测试 ---")

    # --- 准备测试参数 ---
    # 1. 网格生成参数
    test_airfoil_input_file = os.path.join(os.getcwd(), 'data', 'airfoil_dat_files', 'NACA 0012.dat')
    if not os.path.exists(test_airfoil_input_file):
        print(f"[错误] 测试翼型文件未找到: {test_airfoil_input_file}")
        # sys.exit(1)

    # 提取翼型名称用于测试
    test_airfoil_name_for_mesh_gen = os.path.splitext(os.path.basename(test_airfoil_input_file))[0]

    # 确保 RangeAndHeight.dat 在 ./cfl3d/ 目录下
    # cgrid 可执行文件也应该在 ./cfl3d/
    # cfl3d_mpi 可执行文件路径由 run_cfl3d 内部处理

    test_grid_params = {
        "foil_cells": 151,
        "wake_cells": 61,
        "grow_cells": 101,
        "y_plus": 0.9,
        "far_radius": 10.0
    }

    # 2. CFL3D 计算参数
    test_mach = 0.4
    test_alpha = 2.0
    test_re = 3.0e6
    test_IVISC = 5
    # 为了加速测试，可以考虑使用 advanced_config 减少迭代次数
    test_advanced_config_cfl3d = {
        "iflagts": 50,
        "ncyc_level1": 30,
        "ncyc_level2": 30,
        "ncyc_level3": 30,
        "nwrest": 100
    }

    print(f"\n--- [步骤 1: 网格生成] ---")
    print(f"输入翼型: {test_airfoil_input_file}")
    print(f"网格参数: {test_grid_params}")

    mesh_result = {}
    unique_run_directory_for_test = None

    if os.path.exists(test_airfoil_input_file):
        mesh_result = mesh_generation_execute(
            input_foil_file=test_airfoil_input_file,
            num_points=101,
            visualize=False,
            grid_params=test_grid_params,
            n_base=8,
            n_diff=20,
            n_enhanced=3,
            airfoil_name=test_airfoil_name_for_mesh_gen # 传递翼型名称
        )
        print("\n--- 网格生成结果 ---")
        print(json.dumps(mesh_result, indent=2))
        unique_run_directory_for_test = mesh_result.get("unique_run_dir")

        # 检查 cgrid 是否生成了 cfl3d.inp
        if unique_run_directory_for_test and mesh_result.get("grid_generation_result", {}).get("status") == "success":
            generated_inp_path = os.path.join(unique_run_directory_for_test, "cfl3d.inp")
            if os.path.exists(generated_inp_path):
                print(f"[验证成功] cgrid 已在 {generated_inp_path} 生成 cfl3d.inp")
            else:
                print(f"[验证失败] cgrid 未在 {unique_run_directory_for_test} 中生成 cfl3d.inp (重要)")
    else:
        print(f"[跳过] 网格生成，因为输入翼型文件 {test_airfoil_input_file} 未找到。")


    if unique_run_directory_for_test and \
       mesh_result.get("grid_generation_result", {}).get("status") == "success" and \
       os.path.exists(os.path.join(unique_run_directory_for_test, "cfl3d.inp")): # 确保 .inp 文件存在

        print(f"\n--- [步骤 2: CFL3D 计算] ---")
        print(f"唯一运行目录: {unique_run_directory_for_test}")
        print(f"马赫数: {test_mach}, 攻角: {test_alpha}, 雷诺数: {test_re}, IVISC: {test_IVISC}")
        # print(f"CFL3D 高级配置: {test_advanced_config_cfl3d}")

        simulation_result = cfl3d_simulation_func(
            mach=test_mach,
            alpha=test_alpha,
            re=test_re,
            unique_run_dir=unique_run_directory_for_test,
            output_folder_name="test_output_from_full_flow",
            IVISC=test_IVISC,
            num_processes=num_processes,  # 从命令行传入的值
            advanced_config=test_advanced_config_cfl3d,
            airfoil_name=test_airfoil_name_for_mesh_gen, # 传递翼型名称
            keep_intermediate_files=False, # Added keep_intermediate_files
            max_retries_on_cp_failure=1 # Added max_retries_on_cp_failure
        )
        print("\n--- CFL3D 计算结果 ---")
        if "run_stdout" in simulation_result:
            simulation_result_copy = simulation_result.copy()
            print(json.dumps(simulation_result_copy, indent=2))
        else:
            print(json.dumps(simulation_result, indent=2))

        results_dir = simulation_result.get("results_directory")
        if results_dir:
            print(f"\nCFL3D 结果文件位于: {results_dir}")
            cfl3d_out_path = os.path.join(results_dir, "cfl3d.out")
            if os.path.exists(cfl3d_out_path):
                print(f"cfl3d.out 存在于: {cfl3d_out_path}")
            else:
                print(f"cfl3d.out 未在 {results_dir} 中找到。")

            calc_output = simulation_result.get("calculation_output", {})
            force_coeffs = calc_output.get("force_coefficients", {})
            cl = force_coeffs.get("cl")
            cd = force_coeffs.get("cd")
            print(f"提取的力系数: CL = {cl}, CD = {cd}")

            cp_data_info = calc_output.get("cp_data", {})
            if cp_data_info:
                 print(f"提取的Cp点数: {len(cp_data_info.get('x_coords', []))}")

    elif not unique_run_directory_for_test:
        print("\n[跳过] CFL3D 计算，因为网格生成未提供有效的 unique_run_dir。")
    elif not mesh_result.get("grid_generation_result", {}).get("status") == "success":
        print(f"\n[跳过] CFL3D 计算，因为网格生成失败或跳过。状态: {mesh_result.get('grid_generation_result', {}).get('status')}")
    elif unique_run_directory_for_test and not os.path.exists(os.path.join(unique_run_directory_for_test, "cfl3d.inp")):
        print(f"\n[跳过] CFL3D 计算，因为 cgrid 未能在 {unique_run_directory_for_test} 中生成 cfl3d.inp 文件。")


    print("\n--- 完整流程测试结束 ---")