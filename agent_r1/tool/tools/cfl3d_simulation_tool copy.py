"""
CFL3D计算工具，用于执行CFD计算
#    python -m agent_r1.tool.tools.cfl3d_simulation_tool
"""

import os
import json
from agent_r1.tool.tool_base import Tool
from .Function import cfl3d_simulation_func

class CFL3DSimulationTool(Tool):
    """
    用于执行CFL3D计算流体力学分析的工具类，支持多种参数配置方式
    """
    
    def __init__(self):
        """
        初始化CFL3D计算工具
        """
        name = "cfl3d_simulation"
        description = "使用CFL3D执行CFD计算，分析翼型气动性能。支持基本参数直接设置和高级参数通过配置字典设置。"
        parameters = {
            "type": "object",
            "properties": {
                "mach": {
                    "type": "number",
                    "description": "马赫数"
                },
                "alpha": {
                    "type": "number",
                    "description": "攻角，单位为度"
                },
                "re": {
                    "type": "number",
                    "description": "雷诺数"
                },
                "IVISC": {
                    "type": "integer",
                    "description": "湍流模型选择，一般为0-13，默认为5(SA模型)。可通过查看手册IVISC获取更多信息",
                    "default": 5
                },
                "output_folder": {
                    "type": "string",
                    "description": "指定输出结果的文件夹名称，将创建在 cfl3d 目录下",
                    "default": "output"
                },
                "airfoil_name": {
                    "type": "string",
                    "description": "翼型的名称，用于记录和结果保存"
                },
                "unique_run_dir": {
                    "type": "string",
                    "description": "包含网格文件和CFL3D输入文件的唯一运行目录路径"
                },
                "advanced_config": {
                    "type": "object",
                    "description": "可选的高级配置字典，包含更多CFL3D输入参数设置，覆盖默认值或模板文件中的值",
                    "properties": {
                        "beta": {"type": "number", "description": "侧滑角，单位为度", "default": 0.0},
                        "tinf": {"type": "number", "description": "自由流温度，单位为兰肯温度(°R)", "default": 460.0},
                        "dt": {"type": "number", "description": "时间步长，负值表示局部时间步长", "default": -0.8},
                        "irest": {"type": "integer", "description": "重启标志，0表示新计算，1表示从之前结果重启", "default": 0},
                        "iflagts": {"type": "integer", "description": "最大时间步数", "default": 2000},
                        "fmax": {"type": "number", "description": "允许的最大CFL数", "default": 5.0},
                        "cfltau": {"type": "number", "description": "CFL数调整系数", "default": 7.5},
                        "sref": {"type": "number", "description": "参考面积，用于系数计算（二维翼型一般为1）", "default": 1.0},
                        "cref": {"type": "number", "description": "参考弦长，用于力矩计算（二维翼型一般为1）", "default": 1.0},
                        "bref": {"type": "number", "description": "参考展长，用于力矩计算（二维翼型一般为1）", "default": 1.0},
                        "xmc": {"type": "number", "description": "力矩参考点X坐标（二维翼型一般为0.25）", "default": 0.25},
                        "nplot3d": {"type": "integer", "description": "PLOT3D输出频率", "default": 1},
                        "nprint": {"type": "integer", "description": "收敛信息打印频率", "default": -1},
                        "nwrest": {"type": "integer", "description": "重启文件写入频率", "default": 5000},
                        "i2d": {"type": "integer", "description": "二维计算标志(1=2D, 0=3D)", "default": 1},
                        "iem": {"type": "integer", "description": "通量计算方法", "default": 0},
                        "iadvance": {"type": "integer", "description": "推进方法(0=显式, 1=隐式)", "default": 0},
                        "iforce": {"type": "integer", "description": "力和力矩计算频率", "default": 333},
                        "iflim_i": {"type": "integer", "description": "I方向通量限制器类型", "default": 4},
                        "iflim_j": {"type": "integer", "description": "J方向通量限制器类型", "default": 4},
                        "iflim_k": {"type": "integer", "description": "K方向通量限制器类型", "default": 4},
                        "rkap0_i": {"type": "number", "description": "I方向人工黏性系数", "default": 0.3333},
                        "rkap0_j": {"type": "number", "description": "J方向人工黏性系数", "default": 0.3333},
                        "rkap0_k": {"type": "number", "description": "K方向人工黏性系数", "default": 0.3333},
                        "mseq": {"type": "integer", "description": "多重网格层数", "default": 3},
                        "mgflag": {"type": "integer", "description": "多重网格标志(0=禁用, 1=启用)", "default": 1},
                        "ncyc_level1": {"type": "integer", "description": "第一层网格最大迭代次数", "default": 1000},
                        "ncyc_level2": {"type": "integer", "description": "第二层网格最大迭代次数", "default": 1000},
                        "ncyc_level3": {"type": "integer", "description": "第三层网格最大迭代次数", "default": 1000}
                    },
                    "default": None
                }
            },
            "required": ["mach", "alpha", "re", "unique_run_dir", "airfoil_name"]
        }
        
        super().__init__(name, description, parameters)
    
    def execute(self, args: dict) -> str:
        """
        执行CFL3D计算的主入口点。

        Args:
            args (dict): 包含工具参数的字典，符合 'parameters' 定义。

        Returns:
            str: JSON 格式的执行结果字符串。
        """
        # 获取必需参数
        mach = args.get("mach")
        alpha = args.get("alpha")
        re = args.get("re")
        unique_run_dir = args.get("unique_run_dir")
        airfoil_name = args.get("airfoil_name")

        if mach is None or alpha is None or re is None or unique_run_dir is None or airfoil_name is None:
            missing = []
            if mach is None: missing.append("mach")
            if alpha is None: missing.append("alpha")
            if re is None: missing.append("re")
            if unique_run_dir is None: missing.append("unique_run_dir")
            if airfoil_name is None: missing.append("airfoil_name")
            return json.dumps({"status": "error", "message": f"缺少必需的参数: {', '.join(missing)}"})

        # 获取可选参数
        IVISC = args.get("IVISC", 5)
        output_folder = args.get("output_folder", "output")
        advanced_config = args.get("advanced_config", None)

        print(f"[DEBUG] CFL3DSimulationTool.execute: mach={mach}, alpha={alpha}, re={re}, IVISC={IVISC}, output='{output_folder}', advanced_config provided: {advanced_config is not None}")

        try:
            # 调用Function.py中的主函数
            result = cfl3d_simulation_func(
                mach=mach,
                alpha=alpha,
                re=re,
                output_folder=output_folder,
                IVISC=IVISC,
                advanced_config=advanced_config,
                unique_run_dir=unique_run_dir,
                airfoil_name=airfoil_name
            )
            # 将结果字典转换为 JSON 字符串返回
            return json.dumps(result, indent=2)

        except Exception as e:
            import traceback
            error_msg = f"CFL3D计算工具执行失败: {str(e)}"
            print(f"[ERROR] {error_msg}")
            print(traceback.format_exc())
            return json.dumps({"status": "error", "message": error_msg})

    def calculate_reward(self, args: dict, result: str) -> float:
        """
        计算CFL3D模拟工具调用的奖励。
        成功给予小奖励，失败给予较大惩罚。
        """
        try:
            result_data = json.loads(result)
            calc_output = result_data.get("calculation_output", {})

            # 顶层错误优先
            if result_data.get("status") == "error":
                 return -0.7 # 工具执行层面就失败了，惩罚较大

            # 检查嵌套的计算状态
            calc_status = calc_output.get("status")
            if calc_status == "success":
                 # 计算成功，是核心步骤
                 force_status = calc_output.get("force_coefficients_status")
                 cp_status = calc_output.get("cp_data_status")
                 if force_status == "success" or cp_status == "success":
                     return 0.25 # 成功且提取到结果
                 else:
                     return 0.1 # 计算完成但结果提取失败，价值降低
            elif calc_status == "error":
                # 计算过程出错
                return -0.6 # 核心步骤失败，惩罚较大
            elif calc_status == "warning":
                 # 计算完成但有警告
                 return -0.0 # 中性或极小惩罚
            else:
                # 状态未知或缺失
                return -0.4
        except (json.JSONDecodeError, Exception):
            # 结果格式错误或意外异常
            return -0.8 # 惩罚最大

# --- 调试代码 ---
if __name__ == "__main__":
    import os
    import sys
    import shutil
    # 假设此脚本通过 python -m 从项目根目录运行
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
    # sys.path.insert(0, project_root)
    # try:
    #      from agent_r1.tool.tool_base import Tool # 重新导入以防直接运行
    #      from Function import cfl3d_simulation_func, modify_cfl3d_input, run_cfl3d, read_cfl3d_output, extract_force_coefficients, apply_advanced_config, extract_cp_from_cfl3d
    # except ImportError as e:
    #      print(f"导入错误: {e}. 请确保从项目根目录使用 'python -m agent_r1.tool.tools.cfl3d_simulation_tool' 运行，或者项目已正确安装。")
    #      sys.exit(1)

    print("--- 开始测试 CFL3DSimulationTool ---")

    # 预期 cfl3d 目录相对于项目根目录的位置
    cfl3d_base_dir = './cfl3d' # 相对于运行脚本的目录
    cfl3d_exe_path = os.path.join(cfl3d_base_dir, 'cfl3d_Seq_LHR.exe')
    cfl3d_inp_path = os.path.join(cfl3d_base_dir, 'cfl3d.inp')
    cfl3d_grid_path = os.path.join(cfl3d_base_dir, 'cfl3d.xyz')
    test_output_folder = 'test_cfl3d_output' # 测试用的输出目录名
    test_output_path = os.path.join(cfl3d_base_dir, test_output_folder)
    output_file_path = os.path.join(test_output_path, 'cfl3d.out')

    print(f"检查路径:")
    print(f"  预期 cfl3d 目录: {os.path.abspath(cfl3d_base_dir)}")
    print(f"  预期 cfl3d.exe: {os.path.abspath(cfl3d_exe_path)}")
    print(f"  预期 cfl3d.inp: {os.path.abspath(cfl3d_inp_path)}")
    print(f"  预期 cfl3d.xyz (网格): {os.path.abspath(cfl3d_grid_path)}")
    print(f"  测试输出目录: {os.path.abspath(test_output_path)}")

    # 1. 检查 cfl3d 目录是否存在
    if not os.path.isdir(cfl3d_base_dir):
        print(f"[错误] 必要的 '{cfl3d_base_dir}' 目录未在当前工作目录找到。")
        print("请确保从项目根目录运行，并且 'cfl3d' 目录存在。")
        sys.exit(1)
    else:
        print(f"[成功] 找到 '{cfl3d_base_dir}' 目录。")

    # 2. 检查 cfl3d 可执行文件是否存在
    if not os.path.exists(cfl3d_exe_path):
        print(f"[警告] 未找到 CFL3D 可执行文件 '{cfl3d_exe_path}'。计算会失败。")
        can_run_cfl3d = False
    else:
        print(f"[成功] 找到 CFL3D 可执行文件 '{cfl3d_exe_path}'。")
        can_run_cfl3d = True

    # 3. 检查 cfl3d 输入文件是否存在
    if not os.path.exists(cfl3d_inp_path):
        print(f"[错误] 未找到 CFL3D 输入模板文件 '{cfl3d_inp_path}'。")
        sys.exit(1)
    else:
        print(f"[成功] 找到 CFL3D 输入模板文件 '{cfl3d_inp_path}'。")

    # 4. 检查网格文件是否存在
    if not os.path.exists(cfl3d_grid_path):
        print(f"[警告] 未找到 CFL3D 网格文件 '{cfl3d_grid_path}'。计算可能会失败或使用默认网格。")
        grid_available = False
    else:
        print(f"[成功] 找到 CFL3D 网格文件 '{cfl3d_grid_path}'。")
        grid_available = True

    # 5. 尝试实例化工具
    try:
        cfl3d_tool = CFL3DSimulationTool()
        print("[成功] CFL3DSimulationTool 实例化成功。")
    except Exception as e:
        print(f"[错误] CFL3DSimulationTool 实例化失败: {e}")
        sys.exit(1)

    # 6. 准备测试参数
    test_args = {
        "mach": 0.5,
        "alpha": 2.0,
        "re": 3e6,
        "output_folder": test_output_folder, # 指定测试输出目录
        "IVISC": 5, # 使用默认的 SA 模型
        "unique_run_dir": "./cfl3d/dummy_run_dir", # 为测试提供一个虚拟的 unique_run_dir
        "airfoil_name": "NACA0012_test", # 为测试提供一个翼型名称
        "advanced_config": { # 减少迭代次数以加速测试
            "iflagts": 100, # 最大时间步数
            "ncyc_level1": 50,
            "ncyc_level2": 50,
            "ncyc_level3": 50,
            "nwrest": 100 # 减少重启文件写入频率
        }
    }

    print(f"\n--- 执行工具测试 (参数: {test_args}) ---")
    # 清理之前的测试输出目录（如果存在）
    if os.path.exists(test_output_path):
        print(f"清理旧的测试输出目录: {test_output_path}")
        try:
            shutil.rmtree(test_output_path)
        except Exception as e:
            print(f"[警告] 清理旧目录失败: {e}")

    if not can_run_cfl3d:
        print("[跳过] 执行测试，因为 CFL3D 可执行文件不可用。")
    elif not grid_available:
        print("[跳过] 执行测试，因为网格文件不可用。")
    else:
        # 为测试创建一个虚拟的 unique_run_dir 和其中的文件，如果它们不存在
        dummy_dir_path = test_args["unique_run_dir"]
        os.makedirs(dummy_dir_path, exist_ok=True)
        # 假设 cfl3d.inp 和 cfl3d.xyz 是必需的，并且应该存在于 dummy_dir_path 中
        # 在实际场景中，这些文件由网格生成步骤创建
        # 为简单起见，我们仅检查目录，实际测试可能需要复制/创建这些文件
        if not os.path.exists(os.path.join(dummy_dir_path, 'cfl3d.inp')):
            print(f"[警告] 测试用的 cfl3d.inp 不在 {dummy_dir_path}，将尝试从 {cfl3d_inp_path} 复制")
            if os.path.exists(cfl3d_inp_path):
                shutil.copy(cfl3d_inp_path, os.path.join(dummy_dir_path, 'cfl3d.inp'))
            else:
                print(f"[错误] 源 cfl3d.inp {cfl3d_inp_path} 也不存在，测试可能失败")
        
        if not os.path.exists(os.path.join(dummy_dir_path, 'cfl3d.xyz')):
            print(f"[警告] 测试用的 cfl3d.xyz 不在 {dummy_dir_path}，将尝试从 {cfl3d_grid_path} 复制")
            if os.path.exists(cfl3d_grid_path):
                shutil.copy(cfl3d_grid_path, os.path.join(dummy_dir_path, 'cfl3d.xyz'))
            else:
                print(f"[错误] 源 cfl3d.xyz {cfl3d_grid_path} 也不存在，测试可能失败")
        
        # 复制 RangeAndHeight.dat (如果存在源文件)
        source_range_height = os.path.join(cfl3d_base_dir, 'RangeAndHeight.dat')
        dest_range_height_in_dummy = os.path.join(dummy_dir_path, 'RangeAndHeight.dat')
        if os.path.exists(source_range_height):
            shutil.copy(source_range_height, dest_range_height_in_dummy)
        else:
            print(f"[警告] 源 RangeAndHeight.dat ({source_range_height}) 未找到，MPI运行可能受影响")

        try:
            # 调用标准的 execute 方法
            result_json = cfl3d_tool.execute(test_args)
            print("\n--- 工具执行结果 (JSON) ---")
            print(result_json)
            # 尝试解析 JSON
            result = json.loads(result_json)

            # 检查关键输出文件是否存在
            print("\n--- 检查输出文件 ---")
            # 稍等片刻让文件系统同步
            import time
            time.sleep(0.5)
            if os.path.exists(output_file_path):
                print(f"[成功] 找到输出文件: {output_file_path}")
                # 读取最后几行
                try:
                    with open(output_file_path, 'r') as f:
                        lines = f.readlines()
                    print("输出文件最后 10 行:")
                    for line in lines[-10:]:
                        print(line.strip())
                    # 检查是否有计算出的 CL/CD
                    calc_output = result.get("calculation_output", {})
                    coeffs = calc_output.get("force_coefficients", {})
                    print(f"提取的力系数: CL={coeffs.get('cl')}, CD={coeffs.get('cd')}")
                except Exception as read_e:
                    print(f"[警告] 读取输出文件时出错: {read_e}")
            else:
                print(f"[失败] 计算报告成功/警告，但未找到输出文件: {output_file_path}")

        except json.JSONDecodeError:
            print("[错误] 工具返回的不是有效的 JSON 字符串:")
            print(result_json)
        except Exception as e:
            print(f"\n[错误] 工具执行过程中出错: {e}")
            import traceback
            traceback.print_exc()

    # 测试完成后可以选择性清理测试输出目录
    if os.path.exists(test_args["unique_run_dir"]):
        shutil.rmtree(test_args["unique_run_dir"])
        print(f'\n[清理] 已删除虚拟unique_run_dir: {test_args["unique_run_dir"]}')

    print("\n--- CFL3DSimulationTool 测试结束 ---") 