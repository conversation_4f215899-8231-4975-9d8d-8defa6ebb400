"""
CFD网格生成工具，用于处理翼型文件并生成网格
#    python -m agent_r1.tool.tools.cgrid_tool

"""

from agent_r1.tool.tool_base import Tool
from .Function import (
    mesh_generation_execute,
    process_foil_file,
    modify_cgrid_parameters,
    create_default_cgrid_config,
    run_cgrid_exe
)
import json
import os
import traceback
from multiprocessing import Pool, TimeoutError
from typing import List, Dict, Any

class MeshGenerationTool(Tool):
    """
    用于处理翼型文件并生成CFD网格的工具类
    """
    
    def __init__(self):
        """
        初始化网格生成工具
        """
        name = "mesh_generation"
        description = "处理翼型文件（使用增强CST方法）并生成用于CFD分析的网格。输入翼型文件路径，可选指定重构点数和网格参数。"
        parameters = {
            "type": "object",
            "properties": {
                "airfoil_name": {
                    "type": "string",
                    "description": (
                        "输入翼型的名称,如果用户在请求中明确指定了翼型名称(例如 'NACA0012', 'RAE2822', '11 Percent Thickness Supercritical Airfoil')。"
                        "**必须严格使用用户提供的这一个确切的翼型名称字符串，**绝对不允许**修改或推断。** "
                        "工具将在预设目录中查找对应的 .dat 文件。"
                    )
                },
                "num_points": {
                    "type": "integer",
                    "description": "重构翼型曲线的点个数（每个表面）",
                    "default": 101
                },
                "n_base": {
                    "type": "integer",
                    "description": "基础翼型CST拟合阶数",
                    "default": 8
                },
                "n_diff": {
                    "type": "integer",
                    "description": "差异曲线CST拟合阶数",
                    "default": 20
                },
                "n_enhanced": {
                    "type": "integer",
                    "description": "用于增强前缘的差异参数数量",
                    "default": 3
                },
                "grid_params": {
                    "type": "object",
                    "description": "可选的网格生成参数字典，覆盖cgrid.txt中的默认值",
                    "properties": {
                        "foil_cells": {
                            "type": "integer",
                            "description": "翼型上下表面的网格单元数量",
                            "default": 151
                        },
                        "wake_cells": {
                            "type": "integer",
                            "description": "从翼型尾缘到远场的网格单元数量(流向方向)",
                            "default": 61
                        },
                        "grow_cells": {
                            "type": "integer",
                            "description": "从翼型表面到远场的网格单元数量(y+方向)",
                            "default": 101
                        },
                        "y_plus": {
                            "type": "number",
                            "description": "指定的y+值(决定壁面网格尺寸)",
                            "default": 0.9
                        },
                        "far_radius": {
                            "type": "number",
                            "description": "远场半径",
                            "default": 15.0
                        },
                        "u_inf": {
                            "type": "number",
                            "description": "自由流来流速度U_inf(m/s)",
                            "default": 200.0
                        },
                        "density": {
                            "type": "number",
                            "description": "自由流密度rou(kg/m³)",
                            "default": 1.1
                        },
                        "reynolds": {
                            "type": "number",
                            "description": "自由流单位雷诺数Re(/m)",
                            "default": 3e6
                        }
                    },
                    "default": None
                }
            },
            "required": ["airfoil_name"]
        }
        
        super().__init__(name, description, parameters)
        self.airfoil_dat_directory = './data/airfoil_dat_files'

    def execute(self, args: dict) -> str:
        """
        执行翼型处理和网格生成的主入口点。

        Args:
            args (dict): 包含工具参数的字典，符合 'parameters' 定义。

        Returns:
            str: JSON 格式的执行结果字符串。
        """
        airfoil_name = args.get("airfoil_name")
        if not airfoil_name:
             return json.dumps({"status": "error", "message": "缺少必需的参数 'airfoil_name'"})

        if not airfoil_name.lower().endswith('.dat'):
            dat_filename = f"{airfoil_name}.dat"
        else:
            dat_filename = airfoil_name

        input_foil_file = os.path.join(self.airfoil_dat_directory, dat_filename)

        if not os.path.exists(input_foil_file):
             return json.dumps({"status": "error", "message": f"找不到对应的翼型文件: '{input_foil_file}'"})

        num_points = args.get("num_points", 101)
        grid_params = args.get("grid_params", None)
        n_base = args.get("n_base", 8)
        n_diff = args.get("n_diff", 20)
        n_enhanced = args.get("n_enhanced", 3)
        visualize = args.get("visualize", False)

        print(f"[DEBUG] MeshGenerationTool.execute: airfoil_name='{airfoil_name}', constructed_path='{input_foil_file}', points={num_points}, grid_params provided: {grid_params is not None}")

        try:
            result = mesh_generation_execute(
                input_foil_file=input_foil_file,
                num_points=num_points,
                visualize=visualize,
                grid_params=grid_params,
                n_base=n_base,
                n_diff=n_diff,
                n_enhanced=n_enhanced,
                airfoil_name=airfoil_name
            )
            return json.dumps(result)

        except Exception as e:
            error_msg = f"网格生成工具执行失败 (execute): {str(e)}"
            print(f"[ERROR] {error_msg}")
            print(traceback.format_exc())
            return json.dumps({"status": "error", "message": error_msg, "unique_run_dir": None})

    def _execute_single_case_for_batch(self, args_with_index: tuple) -> tuple:
        """
        一个辅助方法，用于在 batch_execute 中并行执行单个网格生成案例。
        """
        original_index, case_args = args_with_index
        try:
            result_str = self.execute(case_args)
            return original_index, result_str
        except Exception as e:
            error_msg = f"CGRID并行计算案例执行失败 (args: {case_args}): {str(e)}"
            print(f"[BATCH_ERROR_CGRID] {error_msg}")
            print(traceback.format_exc())
            return original_index, json.dumps({"status": "error", "message": error_msg, "details": traceback.format_exc()})

    def batch_execute(self, args_list: List[Dict]) -> List[str]:
        """
        并行执行多个网格生成任务。
        """
        if not args_list:
            return []

        default_parallel_runs = min(os.cpu_count() or 1, 8) 
        num_parallel_processes = int(os.environ.get("CGRID_MAX_PARALLEL_RUNS", default_parallel_runs))
        num_parallel_processes = min(num_parallel_processes, len(args_list))

        print(f"[INFO] MeshGenerationTool.batch_execute: Starting batch of {len(args_list)} mesh generations with up to {num_parallel_processes} parallel processes.")

        indexed_args_list = list(enumerate(args_list))
        results_with_indices = [None] * len(args_list)
        
        default_job_timeout = 600 
        timeout_seconds_per_job = int(os.environ.get("CGRID_JOB_TIMEOUT", default_job_timeout)) 

        try:
            with Pool(processes=num_parallel_processes) as pool:
                async_job_results = [pool.apply_async(self._execute_single_case_for_batch, args=(task,)) for task in indexed_args_list]
                
                for i, async_res in enumerate(async_job_results):
                    original_idx = indexed_args_list[i][0]
                    try:
                        _, result_str = async_res.get(timeout=timeout_seconds_per_job)
                        results_with_indices[original_idx] = result_str
                    except TimeoutError:
                        error_msg = f"CGRID并行计算案例 (原始索引 {original_idx}, args: {args_list[original_idx]}) 超时 ({timeout_seconds_per_job}s)"
                        print(f"[BATCH_TIMEOUT_CGRID] {error_msg}")
                        results_with_indices[original_idx] = json.dumps({"status": "error", "message": error_msg})
                    except Exception as e:
                        error_msg = f"CGRID并行计算案例 (原始索引 {original_idx}, args: {args_list[original_idx]}) 获取结果时发生意外错误: {str(e)}"
                        print(f"[BATCH_ERROR_CGRID] {error_msg}")
                        print(traceback.format_exc())
                        results_with_indices[original_idx] = json.dumps({"status": "error", "message": error_msg})
                        
        except Exception as e:
            print(f"[FATAL_BATCH_ERROR_CGRID] MeshGenerationTool.batch_execute encountered an error with the multiprocessing pool: {e}")
            print(traceback.format_exc())
            for i in range(len(args_list)):
                if results_with_indices[i] is None:
                    results_with_indices[i] = json.dumps({"status": "error", "message": f"Batch processing pool error for CGRID: {str(e)}"})
        
        print(f"[INFO] MeshGenerationTool.batch_execute: Finished batch of {len(args_list)} mesh generations.")
        return results_with_indices
    
    def _process_foil_file(self, input_foil_file, output_foil_file, num_points=101, visualize=False, n_base=8, n_diff=20, n_enhanced=3):
        """
        处理翼型文件，重构曲线并保存为特定格式
        """
        return process_foil_file(input_foil_file, output_foil_file, num_points, visualize, n_base, n_diff, n_enhanced)
    
    def _modify_cgrid_parameters(self, cgrid_file_path, params):
        """
        修改Cgrid输入文件中的参数
        """
        return modify_cgrid_parameters(cgrid_file_path, params)
    
    def _create_default_cgrid_config(self, cgrid_file_path):
        """
        创建默认的cgrid.txt文件
        """
        return create_default_cgrid_config(cgrid_file_path)
    
    def _run_cgrid_exe(self, cfl3d_dir):
        """
        运行网格生成程序cgrid.exe
        """
        print(f"[Warning] _run_cgrid_exe called, but its mapping to the functional run_cgrid_exe is unclear from parameters. This method might be unused.")
        return {"status": "error", "message": "_run_cgrid_exe not fully implemented or mapped."}

    def calculate_reward(self, args: dict, result: str) -> float:
        """
        计算网格生成工具调用的奖励。
        成功且文件存在给予小奖励，失败给予中等惩罚。
        """
        try:
            result_data = json.loads(result)
            processing_status = result_data.get("processing_result", {}).get("status")
            grid_gen_status = result_data.get("grid_generation_result", {}).get("status")
            unique_run_dir = result_data.get("unique_run_dir")

            if processing_status == "success" and grid_gen_status == "success":
                if unique_run_dir and os.path.exists(os.path.join(unique_run_dir, 'cfl3d.xyz')):
                    if os.path.exists(os.path.join(unique_run_dir, 'cfl3d.inp')):
                        return 0.15 
                    else:
                        print(f"[Reward-Warning] Mesh gen (cfl3d.xyz) success, but cfl3d.inp missing in {unique_run_dir}")
                        return 0.05
                else:
                    print(f"[Reward-Warning] Mesh gen reported success, but cfl3d.xyz missing in {unique_run_dir}")
                    return -0.05 
            elif processing_status == "error" or grid_gen_status == "error":
                print(f"[Reward-Info] Mesh gen failed. Processing: {processing_status}, Grid Gen: {grid_gen_status}")
                return -0.4 
            elif grid_gen_status == "skipped": 
                print(f"[Reward-Info] Mesh gen skipped due to processing failure.")
                return -0.35 
            else:
                print(f"[Reward-Warning] Unknown state or missing unique_run_dir in mesh gen result.")
                return -0.3
        except (json.JSONDecodeError, KeyError, Exception) as e:
            print(f"[Reward-Error] Error parsing mesh gen result for reward: {e}")
            return -0.5

# --- 调试代码 ---
if __name__ == "__main__":
    import os
    import sys
    # 假设此脚本通过 python -m 从项目根目录运行
    # ... (保留可能需要的 sys.path 修改或导入检查) ...


    print("--- 开始测试 MeshGenerationTool ---")

    # 预期 cfl3d 目录相对于项目根目录的位置
    cfl3d_base_dir = './cfl3d' # 相对于运行脚本的目录（通常是项目根目录）
    cgrid_exe_path = os.path.join(cfl3d_base_dir, 'cgrid')
    cgrid_txt_path = os.path.join(cfl3d_base_dir, 'cgrid.txt')

    input_airfoil_name = "NACA 0012" # 测试用的翼型名称
    airfoil_dat_dir = './data/airfoil_dat_files' # 翼型 dat 文件目录
    expected_input_dat_path = os.path.join(airfoil_dat_dir, f"{input_airfoil_name}.dat")
    output_foil_file_processed = os.path.join(cfl3d_base_dir, 'foil.dat') # Function.py 写入的位置
    output_grid_file = os.path.join(cfl3d_base_dir, 'cfl3d.xyz') # cgrid.exe 生成的文件

    print(f"检查路径:")
    print(f"  预期 cfl3d 目录: {os.path.abspath(cfl3d_base_dir)}")
    print(f"  预期 cgrid.exe: {os.path.abspath(cgrid_exe_path)}")
    print(f"  预期 cgrid.txt: {os.path.abspath(cgrid_txt_path)}")
    # --- 修改开始 ---
    print(f"  预期输入翼型 .dat 文件: {os.path.abspath(expected_input_dat_path)}")
    # --- 修改结束 ---
    print(f"  预期输出网格文件: {os.path.abspath(output_grid_file)}")

    # 1. 检查 cfl3d 目录是否存在
    if not os.path.isdir(cfl3d_base_dir):
        print(f"[错误] 必要的 '{cfl3d_base_dir}' 目录未在当前工作目录找到。")
        print("请确保从项目根目录运行，并且 'cfl3d' 目录存在。")
        sys.exit(1)
    else:
        print(f"[成功] 找到 '{cfl3d_base_dir}' 目录。")

    # --- 修改开始 ---
    # 1.b 检查输入翼型文件是否存在
    if not os.path.exists(expected_input_dat_path):
        print(f"[错误] 输入翼型 .dat 文件 '{expected_input_dat_path}' 未找到。")
        print(f"请确保该文件存在于 '{airfoil_dat_dir}' 目录下。")
        sys.exit(1)
    else:
        print(f"[成功] 找到输入翼型 .dat 文件 '{expected_input_dat_path}'。")
    # --- 修改结束 ---

    # 2. 检查 cgrid.exe 是否存在
    if not os.path.exists(cgrid_exe_path):
        print(f"[警告] 未找到 Cgrid 可执行文件 '{cgrid_exe_path}'。网格生成会失败。")
        can_run_cgrid = False
    else:
        print(f"[成功] 找到 Cgrid 可执行文件 '{cgrid_exe_path}'。")
        can_run_cgrid = True

    # 3. 尝试实例化工具
    try:
        mesh_tool = MeshGenerationTool()
        print("[成功] MeshGenerationTool 实例化成功。")
    except Exception as e:
        print(f"[错误] MeshGenerationTool 实例化失败: {e}")
        sys.exit(1)

    # --- 修改开始 ---
    # 4. 准备测试参数 (移除创建虚拟文件)
    test_args = {
        "airfoil_name": input_airfoil_name, # 使用翼型名称
        "num_points": 1001, # 可以调整点数
        "grid_params": { # 使用默认或简化的网格参数
            "foil_cells": 151, # 可以使用默认值或测试值
            "wake_cells": 61,
            "grow_cells": 101,
            "y_plus": 0.9,
            "far_radius": 15.0,
            "u_inf": 200.0,
            "density": 1.1,
            "reynolds": 3e6
        }
    }
    # --- 修改结束 ---

    print(f"\n--- 执行工具测试 (参数: {test_args}) ---")
    try:
        # 调用内部逻辑所在的函数
        result_json = mesh_tool.execute(test_args)
        print("\n--- 工具执行结果 (JSON) ---")
        print(result_json)
        # 尝试解析 JSON
        result = json.loads(result_json)

        # 检查关键输出文件是否存在
        print("\n--- 检查输出文件 ---")
        if result and result.get("output_foil_file") and os.path.exists(result["output_foil_file"]):
             print(f"[成功] 找到处理后的翼型文件: {result['output_foil_file']}")
        elif os.path.exists(output_foil_file_processed): # 备用检查
             print(f"[成功] 找到处理后的翼型文件 (备用检查): {output_foil_file_processed}")
        else:
             print(f"[失败] 未找到处理后的翼型文件")

        grid_gen_result = result.get("grid_generation_result", {})
        if grid_gen_result.get("status") == "success":
             if os.path.exists(output_grid_file):
                 print(f"[成功] 找到生成的网格文件: {output_grid_file}")
             else:
                 # 即使 cgrid.exe 存在且报告成功，文件也可能因为权限等原因未生成
                 print(f"[警告] 网格生成报告成功，但未找到预期的网格文件: {output_grid_file}")
        elif can_run_cgrid:
             print(f"[失败] 网格生成失败或跳过: {grid_gen_result.get('message', '未知原因')}")
             if not os.path.exists(output_grid_file):
                 print(f"  未找到网格文件: {output_grid_file}")
        else:
            print("[跳过] 网格文件检查，因为 cgrid.exe 不可用。")

    except json.JSONDecodeError:
         print("[错误] 工具返回的不是有效的 JSON 字符串:")
         print(result_json)
    except Exception as e:
        print(f"\n[错误] 工具执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

    # --- 修改开始 ---
    # 6. 清理 (移除虚拟文件清理)
    finally:
        # 可以选择性地清理 cfl3d 目录下的输出文件
        # if os.path.exists(output_foil_file_processed): os.remove(output_foil_file_processed)
        # if os.path.exists(output_grid_file): os.remove(output_grid_file)
        # if os.path.exists(cgrid_txt_path): os.remove(cgrid_txt_path) # 如果测试修改了它
        print("\n[清理] 无需清理虚拟输入文件。")
    # --- 修改结束 ---

    print("\n--- MeshGenerationTool 测试结束 ---") 