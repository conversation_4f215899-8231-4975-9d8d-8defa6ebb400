"""
搜索工具实现，使用 CFL3D 手册作为知识源
#python -m agent_r1.tool.tools.cfl3d_search_tool
"""

import json
from typing import Dict, List
from agent_r1.tool.tool_base import Tool
from RAG.split import MilvusManager



class CFL3DSearchTool(Tool):
    """
    使用 CFL3D 手册作为知识源的搜索工具
    """

    def __init__(self, milvus_collection_name="cfl3d_manual"):
        """
        初始化搜索工具
        Args:
            milvus_collection_name (str): Milvus 集合的名称
        """
        name = "cfl3d_search"
        description = "搜索 CFL3D 手册中的信息，解答有关 CFD 和流体力学的问题。支持关键词过滤和不同的搜索模式。"
        parameters = {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索查询"
                },
                "keywords": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "用于过滤的关键词列表，例如['turbulence model', 'BCtype']"
                },
                "search_type": {
                    "type": "string",
                    "description": "搜索类型：'text'(文本搜索),'summary'(摘要搜索),'combined'(综合搜索)",
                    "enum": ["text", "summary", "combined"],
                    "default": "combined"
                },
                "limit": {
                    "type": "integer",
                    "description": "返回结果的最大数量",
                    "default": 5
                },
                "text_weight": {
                    "type": "number",
                    "description": "综合搜索中文本向量的权重比例(0-1)，摘要向量权重为(1-text_weight)",
                    "default": 0.6
                }
            },
            "required": ["query"]
        }

        super().__init__(name, description, parameters)

        # 初始化 MilvusManager (不创建新集合，使用现有集合)
        self.milvus_manager = MilvusManager(collection_name=milvus_collection_name, create_collection=False)
        print(f"[DEBUG] CFL3D 搜索工具 (集合: {milvus_collection_name}) 初始化完成")

    def execute(self, args: Dict) -> str:
        """
        执行搜索查询

        Args:
            args: 工具参数，包含:
                - "query": 搜索查询字符串
                - "keywords": (可选) 用于过滤的关键词列表
                - "search_type": (可选) 搜索类型，可选 "text", "summary", "combined"
                - "limit": (可选) 返回结果的最大数量
                - "text_weight": (可选) 综合搜索中文本向量的权重

        Returns:
            格式化的搜索结果 (JSON 字符串)
        """
        query = args.get("query", "").strip()
        keywords = args.get("keywords", None)
        search_type = args.get("search_type", "combined")
        limit = args.get("limit", 5)
        text_weight = args.get("text_weight", 0.6) # 使用参数中的权重，默认0.6

        if not query:
            return json.dumps({"error": "查询不能为空"})

        print(f"[DEBUG] 执行搜索: query='{query}', keywords={keywords}, type='{search_type}', limit={limit}, weight={text_weight}")

        try:
            # 根据搜索类型选择相应的搜索方法
            if search_type == "text":
                results = self.milvus_manager.vector_search(
                    query=query,
                    keywords=keywords,
                    field="vector",
                    limit=limit
                )
            elif search_type == "summary":
                results = self.milvus_manager.vector_search(
                    query=query,
                    keywords=keywords,
                    field="summary_vector",
                    limit=limit
                )
            else:  # combined or default
                results = self.milvus_manager.combined_search(
                    query=query,
                    keywords=keywords,
                    limit=limit,
                    text_weight=text_weight # 传递权重
                )

            print(f"[DEBUG] 原始搜索结果: {results}")
            return self._format_results(results, search_type, text_weight)
        except Exception as e:
            error_msg = f"执行搜索失败: {str(e)}"
            print(f"[ERROR] {error_msg}")
            import traceback
            traceback.print_exc() # 打印详细错误堆栈
            return json.dumps({"error": error_msg})

    def batch_execute(self, args_list: List[Dict]) -> List[str]:
        """
        批量执行多个搜索查询 (当前为顺序执行)

        Args:
            args_list: 工具参数列表

        Returns:
            格式化的搜索结果列表 (JSON 字符串列表)
        """
        results = []
        for args in args_list:
            results.append(self.execute(args))
        return results

    def _format_results(self, search_results, search_type: str, text_weight: float) -> str:
        """
        格式化搜索结果以提高可读性

        Args:
            search_results: 来自 MilvusManager 的搜索结果 (通常是 [[result1, result2,...]])
            search_type: 执行的搜索类型
            text_weight: 综合搜索时使用的文本权重

        Returns:
            格式化的结果字符串 (JSON 字符串)
        """
        if not search_results or not search_results[0]:
            print("[DEBUG] 搜索结果为空")
            return json.dumps({"results": []})

        formatted_results = []
        # MilvusManager 返回的是包含一个内层列表的结果 [[result1, result2, ...]]
        results_list = search_results[0]

        print(f"[DEBUG] 开始格式化 {len(results_list)} 条结果")

        for i, result in enumerate(results_list):
            try:
                if isinstance(result, dict) and "entity" in result:
                    entity = result["entity"]
                    score = result.get("distance", 0) # 距离或综合得分
                    source = result.get("source", search_type) # 'text', 'summary', 'both' 或默认类型

                    document = {
                        "rank": i + 1,
                        "title": entity.get("title", "N/A"),
                        "summary": entity.get("summary", "N/A"),
                        "document": entity.get("text", "N/A"),
                        "keywords": entity.get("keywords", []),
                        "score": float(f"{score:.4f}"), # 格式化分数
                        "source": source
                    }

                    # 如果是综合搜索，添加详细分数信息
                    if search_type == "combined" and source != 'text' and source != 'summary': # 'both' or combined result
                        text_score = result.get("text_score", 0)
                        summary_score = result.get("summary_score", 0)
                        document["details"] = {
                            "combined_score": float(f"{score:.4f}"),
                            "text_weighted_score": float(f"{text_score:.4f}"),
                            "summary_weighted_score": float(f"{summary_score:.4f}"),
                            "text_weight": text_weight,
                            "summary_weight": float(f"{1 - text_weight:.2f}")
                        }
                    elif search_type == "combined": # From text or summary in combined search
                         document["details"] = {
                             "original_score": float(f"{result.get('original_distance', 0):.4f}"),
                             "weighted_score": float(f"{score:.4f}"),
                             "weight": text_weight if source == 'text' else (1 - text_weight)
                         }

                    formatted_results.append(document)
                else:
                     print(f"[WARNING] 无法解析的结果格式: {result}")

            except Exception as e:
                print(f"[ERROR] 格式化结果时出错: {e}, 结果: {result}")
                continue # 跳过格式化失败的结果

        print(f"[DEBUG] 格式化完成，共 {len(formatted_results)} 条有效结果")
        return json.dumps({"results": formatted_results}, ensure_ascii=False, indent=2)

    def calculate_reward(self, args: Dict, result: str) -> float:
        """
        计算搜索操作的奖励值。
        成功找到结果给予小奖励，未找到结果给予微小奖励，错误给予小惩罚。
        """
        try:
            result_obj = json.loads(result)

            if "results" in result_obj:
                num_results = len(result_obj["results"])
                if num_results > 0:
                    # 成功找到结果，是理想情况
                    # 基础奖励 + 结果数量奖励，但上限降低
                    return 0.1 + min(num_results * 0.02, 0.1) # 总奖励上限 0.2
                else:
                    # 成功执行搜索，但未找到相关文档
                    return 0.0 # 中性奖励，表示工具可用但未提供信息
            elif "error" in result_obj:
                # 工具执行出错
                return -0.15 # 小惩罚
            else:
                # 无法识别的成功响应格式（不应发生）
                return -0.1
        except json.JSONDecodeError:
            # 无效的 JSON 结果，可能是 Agent 或工具生成错误
            return -0.25 # 稍大的惩罚
        except Exception as e:
            # 其他意外错误
            print(f"[ERROR] 计算搜索奖励时发生未知错误: {e}")
            return -0.1 # 小惩罚

# --- 测试代码 ---
if __name__ == "__main__":
    print("--- 开始测试 CFL3DSearchTool ---")

    # 确保 Milvus 数据库文件存在或路径正确
    # 如果 CFL3D.db 不在当前目录，需要调整 MilvusManager 的初始化
    # 或者确保 RAG/split.py 中的路径正确

    try:
        # 初始化工具 (假设 CFL3D.db 在 RAG 目录下或者 split.py 中指定了正确路径)
        # 如果需要，可以传递 Milvus 文件路径给 MilvusManager
        search_tool = CFL3DSearchTool(milvus_collection_name="cfl3d_manual") # 使用默认集合名

        # 测试用例 1: 基本文本搜索
        print("\n--- 测试 1: 文本搜索 ---")
        args1 = {"query": "boundary conditions", "search_type": "text", "limit": 3}
        result1_json = search_tool.execute(args1)
        print("参数:", args1)
        print("结果:", result1_json)
        print("奖励:", search_tool.calculate_reward(args1, result1_json))

        # 测试用例 2: 摘要搜索 + 关键词
        print("\n--- 测试 2: 摘要搜索 + 关键词 ---")
        args2 = {
            "query": "turbulence modeling options",
            "keywords": ["Spalart", "k-epsilon"],
            "search_type": "summary",
            "limit": 4
        }
        result2_json = search_tool.execute(args2)
        print("参数:", args2)
        print("结果:", result2_json)
        print("奖励:", search_tool.calculate_reward(args2, result2_json))

        # 测试用例 3: 综合搜索 (默认) + 不同权重
        print("\n--- 测试 3: 综合搜索 (默认权重) ---")
        args3 = {"query": "grid generation", "limit": 3}
        result3_json = search_tool.execute(args3)
        print("参数:", args3)
        print("结果:", result3_json)
        print("奖励:", search_tool.calculate_reward(args3, result3_json))

        print("\n--- 测试 4: 综合搜索 (自定义权重) ---")
        args4 = {"query": "implicit scheme", "limit": 3, "text_weight": 0.3}
        result4_json = search_tool.execute(args4)
        print("参数:", args4)
        print("结果:", result4_json)
        print("奖励:", search_tool.calculate_reward(args4, result4_json))

        # 测试用例 5: 查询无结果
        print("\n--- 测试 5: 查询无结果 ---")
        args5 = {"query": "nonexistent topic xyz123", "search_type": "text"}
        result5_json = search_tool.execute(args5)
        print("参数:", args5)
        print("结果:", result5_json)
        print("奖励:", search_tool.calculate_reward(args5, result5_json))

        # 测试用例 6: 错误处理 - 空查询
        print("\n--- 测试 6: 空查询 ---")
        args6 = {"query": "", "search_type": "text"}
        result6_json = search_tool.execute(args6)
        print("参数:", args6)
        print("结果:", result6_json)
        print("奖励:", search_tool.calculate_reward(args6, result6_json))

    except Exception as e:
        print(f"\n--- 测试过程中发生错误 ---")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {e}")
        print("请确保:")
        print("1. Milvus 数据库文件 'CFL3D.db' 存在且路径配置正确 (在 RAG/split.py 或 MilvusManager 初始化中)。")
        print("2. 必要的依赖已安装 (pymilvus, sentence-transformers, etc.)。")
        print("3. 嵌入模型文件已下载并位于 RAG/split.py 中指定的路径。")
        import traceback
        traceback.print_exc()

    print("\n--- 测试结束 ---")