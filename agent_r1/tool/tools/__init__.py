"""
Specific tool implementations
"""

from agent_r1.tool.tools.calculator_tool import CalculatorTool
from agent_r1.tool.tools.cfl3d_simulation_tool import CFL3DSimulationTool
#from agent_r1.tool.tools.cfl3d_search_tool import CFL3DSearchTool
from agent_r1.tool.tools.aerodynamic_parameters_tool import AerodynamicParametersTool
from agent_r1.tool.tools.cgrid_tool import MeshGenerationTool

__all__ = [
    'CalculatorTool',
    'CFL3DSimulationTool',
    'AerodynamicParametersTool',
    'MeshGenerationTool',
] 
#    'CFL3DSearchTool',

def _default_tools(env: str):
    if env == 'calculator':
        return [CalculatorTool()]
    elif env == 'cfd':
        return [
            MeshGenerationTool(),
            CFL3DSimulationTool(),
            #CFL3DSearchTool(),
            AerodynamicParametersTool(),
        ]
    else:
        # Potentially log a warning here if an unknown env is provided
        print(f"[WARNING] Unknown tool environment: {env}. Returning no tools.")
        return []