import glob
import json
import logging
import os
import re
import shutil
import subprocess
from io import StringIO
from subprocess import Popen, PIPE, STDOUT
import cst_modeling
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
import sys
from .enhanced_cst import enhanced_cst_foil_with_fit, read_coordinates
from cfdpost2.cfdresult import cfl3d2
from cfdpost2.section.physical import PhysicalSec
import time


def Aerodynamic_parameters(re=None, airspeed=None, mach=None, alpha=None, a=336.4, rho=1.17, chord_length=1.0,
                           mu=1.81e-5):
    """
    Function to convert between Reynolds number, airspeed, and Mach number.

    :param re: Optional, Reynolds number to convert to airspeed (if given).
    :param airspeed: Optional, airspeed (m/s) to convert to Reynolds number (if given).
    :param mach: Optional, Mach number to convert to airspeed or calculate from airspeed (if given).
    :param a: Speed of sound in m/s. Defaults to 336.4 m/s (standard condition).
    :param rho: Air density in kg/m³. If None, defaults to 1.225 kg/m³ (standard conditions).
    :param chord_length: Characteristic length, such as airfoil chord length in meters. If None, defaults to 1.0 m.
    :param mu: Dynamic viscosity of air in Pa·s. If None, defaults to 1.81e-5 Pa·s (standard for air at 15°C).

    :return: Returns a dictionary with calculated Reynolds number, airspeed, and Mach number.
    """
    # print(re, airspeed, mach)
    if airspeed is not None and airspeed == 0:
        airspeed = None
    if mach is not None and mach == 0:
        mach = None
    if re is not None and re == 0:
        re = None
    result = {}  # To store all calculated values
    print("inpt", re, airspeed, mach)
    if airspeed is None:
        if mach is not None:
            airspeed = mach * a  # 根据马赫数计算空速
        elif re is not None:
            airspeed = re * mu / (rho * chord_length)  # 根据雷诺数计算空速
        else:
            re = 0
            airspeed = 0
            # raise ValueError("At least one of Reynolds number, airspeed, or Mach number must be provided.")

    if mach is None:
        mach = airspeed / a  # 根据空速计算马赫数

    if re is None:
        re = (rho * airspeed * chord_length) / mu

    if alpha is None:
        alpha = 0
    # 如果提供了Reynolds数和空速

    '''if (re != None and 0) and (airspeed != None and 0) and (mach == None or 0):
        # 使用提供的空速计算Reynolds数
        calculated_re = (rho * airspeed * chord_length) / mu

        # 计算提供的Reynolds数和计算Reynolds数之间的相对误差
        relative_error = abs(calculated_re - re) / re
        print(re, airspeed)
        # 如果相对误差超过1%，则抛出错误
        if relative_error > 0.01:
            raise ValueError(f"The relative error between the provided Re and the calculated Re exceeds 1% "
                             f"(Relative Error: {relative_error * 100:.2f}%). "
                             f"Provided Re: {re}, Calculated Re: {calculated_re}.")

        # 计算并存储Reynolds数和空速
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了空速，则计算Reynolds数
    elif (re == None or 0) and (airspeed != None and 0):
        re = (rho * airspeed * chord_length) / mu
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了Reynolds数，则计算空速
    elif (airspeed == None or 0) and (re != None and 0):
        airspeed = (re * mu) / (rho * chord_length)
        result["Airspeed"] = airspeed
        result["Reynolds_number"] = re
        result["Mach"] = airspeed/a

    # 如果提供了马赫数，则根据声速计算空速
    if (mach != None and 0) and (airspeed == None or 0):
        airspeed = mach * a
        result["Airspeed"] = airspeed
        result["Mach"] = mach

    # 如果提供了空速，则计算马赫数
    elif (airspeed != None and 0) and mach == None:
        mach = airspeed / a
        result["Mach"] = mach
        result["Airspeed"] = airspeed'''

    # 如果没有提供足够的参数，则抛出错误
    # if airspeed == None or 0 and re ==None or 0 and mach == None or 0:

    # Include all parameters in the result
    result["Mach"] = mach
    result["Airspeed"] = airspeed
    result["Reynolds_number"] = re
    result["rho"] = rho
    result["chord_length"] = chord_length
    result["mu"] = mu
    result["speed_of_sound"] = a  # 声速
    result["alpha"] = alpha
    print("outpt:", re, airspeed, mach)
    return result


# cfl3d
def cfl3d_simulation_func(mach, alpha, re, output_folder="output", IVISC=5, advanced_config=None):
    """
    执行CFL3D计算的全流程函数
    
    Args:
        mach: 马赫数
        alpha: 攻角，单位为度
        re: 雷诺数
        output_folder: 输出文件夹名称，默认为"output"
        IVISC: 湍流模型选择(0-13)，默认为5(SA模型)
        advanced_config: 高级配置字典，包含更多参数设置
        
    Returns:
        计算结果
    """
    cfl3d_dir = './cfl3d'
    cfl3d_inp_path = os.path.join(cfl3d_dir, 'cfl3d.inp')
    
    # --- 修改开始: 在运行前删除旧的输出文件夹 ---
    output_folder_path = os.path.join(cfl3d_dir, output_folder)
    if os.path.exists(output_folder_path):
        try:
            shutil.rmtree(output_folder_path)
            print(f"[清理] 已删除旧的输出文件夹: {output_folder_path}")
        except Exception as e:
            print(f"[警告] 删除旧的输出文件夹失败: {output_folder_path}, error: {e}")
            # 即使删除失败，仍然尝试继续
    # --- 修改结束 ---

    try:
        # 修改CFL3D输入文件中的参数
        input_modification_result = modify_cfl3d_input(
            cfl3d_inp_path, 
            mach, 
            alpha, 
            re, 
            IVISC,
            advanced_config
        )
        
        # 运行CFL3D计算
        stdout, stderr = run_cfl3d(cfl3d_dir, output_folder)
        
        # 读取输出文件
        output_data = read_cfl3d_output(cfl3d_dir, output_folder)
        
        return {
            "input_modification": input_modification_result,
            "calculation_output": output_data
        }
    
    except Exception as e:
        return {"status": "error", "message": f"CFL3D计算过程中出错: {str(e)}"}

def modify_cfl3d_input(file_path, mach_value, alpha_value, re_value, IVISC=5, advanced_config=None):
    """
    修改CFL3D输入文件中的参数
    """
    try:
        with open(file_path, 'r') as file:
            lines = file.readlines()
        
        # 修改基本参数：Mach, Alpha和ReUe
        for i, line in enumerate(lines):
            if "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY" in line:
                values = lines[i + 1].split()
                reue = re_value / 1e6  # 将雷诺数转换为百万单位
                
                # 保留原始值的格式，只修改需要的值
                values[0] = f"{mach_value:.4f}"  # XMACH
                values[1] = f"{alpha_value:.4f}"  # ALPHA
                values[3] = f"{reue:.4f}"  # REUE (百万)
                
                # 重新组合这一行
                lines[i + 1] = "   ".join(values) + "\n"
                break
        
        # 修改湍流模型参数
        for i, line in enumerate(lines):
            if "IVISC(I)  IVISC(J)  IVISC(K)" in line:
                values = lines[i + 1].split()
                if len(values) >= 7:  # 确保有足够的元素
                    values[4] = f"{IVISC}"  # IVISC(I)
                    values[5] = f"{IVISC}"  # IVISC(J)
                    values[6] = f"{IVISC}"  # IVISC(K)
                    lines[i + 1] = "   ".join(values) + "\n"
                break
        
        # 处理高级配置参数
        if advanced_config:
            lines = apply_advanced_config(lines, advanced_config)
        
        # 保存修改后的文件
        with open(file_path, 'w') as file:
            file.writelines(lines)
        
        return {"status": "success", "message": "输入参数修改成功"}
    
    except Exception as e:
        return {"status": "error", "message": f"修改输入参数失败: {str(e)}"}

def run_cfl3d(cfl3d_dir, output_folder):
    """
    运行CFL3D计算

    参数:
        cfl3d_dir: CFL3D目录路径 (相对于执行脚本的目录)
        output_folder: 输出文件夹名称

    返回:
        计算程序的标准输出和标准错误
    """
    # 创建完整的输出文件夹路径
    output_folder_path = os.path.join(cfl3d_dir, output_folder)
    if not os.path.exists(output_folder_path):
        os.makedirs(output_folder_path)

    # --- 修改开始: 添加 RangeAndHeight.dat 到复制列表 ---
    files_to_copy = ['cfl3d.inp', 'cfl3d.xyz', 'RangeAndHeight.dat']
    # --- 修改结束 ---

    for file_name in files_to_copy:
        src_file = os.path.join(cfl3d_dir, file_name)
        dest_file = os.path.join(output_folder_path, file_name)
        if os.path.exists(src_file):
            try:
                 shutil.copy(src_file, dest_file)
                 print(f"[DEBUG] Copied {src_file} to {dest_file}") # 添加复制成功的调试信息
            except Exception as copy_e:
                 print(f"[Warning] Failed to copy {src_file} to {dest_file}: {copy_e}")
                 # 如果 RangeAndHeight.dat 复制失败，可能仍然导致 CFL3D 错误
                 if file_name == 'RangeAndHeight.dat':
                      # 可以考虑在这里抛出异常或返回错误，因为 CFL3D 可能依赖它
                      raise IOError(f"Required file '{file_name}' exists but could not be copied to output directory.") from copy_e
        else:
             # 对 RangeAndHeight.dat 做更严格的检查，如果它不存在，则直接报错退出
             if file_name == 'RangeAndHeight.dat':
                  raise FileNotFoundError(f"Required input file not found: {src_file}. CFL3D cannot run.")
             else:
                  print(f"[Warning] Optional file not found for copying: {src_file}")


    # --- 修改开始: 获取可执行文件的绝对路径 ---
    # 假设 cfl3d_dir 是相对于当前运行目录的路径
    base_run_dir = os.getcwd() # 获取运行 python 命令时的目录
    absolute_cfl3d_dir = os.path.abspath(os.path.join(base_run_dir, cfl3d_dir))
    cfl3d_executable_relative = 'cfl3d_Seq_LHR.exe' # 可执行文件名
    cfl3d_executable_absolute = os.path.join(absolute_cfl3d_dir, cfl3d_executable_relative)
    print(f"[DEBUG] run_cfl3d: Trying to execute absolute path: {cfl3d_executable_absolute}")
    # --- 修改结束 ---


    # 运行CFL3D计算程序
    cmd = 'y\n'  # 自动回答提示

    try:
        # 检查可执行文件是否存在（使用绝对路径）
        if not os.path.exists(cfl3d_executable_absolute):
             raise FileNotFoundError(f"CFL3D executable not found at absolute path: {cfl3d_executable_absolute}")

        p = subprocess.Popen(
            [cfl3d_executable_absolute],  # --- 修改: 使用绝对路径，并放入列表 ---
            cwd=output_folder_path,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            #text=True # 推荐使用 text=True 以便处理文本流
        )

        # 使用 communicate 获取输出，设置合理的 timeout
        try:
            stdout_bytes, stderr_bytes = p.communicate(input=cmd.encode('ascii'), timeout=600) # input 仍然需要编码

            # 尝试用指定编码解码，忽略错误
            stdout = stdout_bytes.decode('latin-1', errors='ignore')
            stderr = stderr_bytes.decode('latin-1', errors='ignore')            
            print(f"[DEBUG] run_cfl3d: Process finished with return code {p.returncode}")
            if p.returncode != 0:
                 print(f"[Warning] run_cfl3d: CFL3D process exited with non-zero code {p.returncode}.")
                 #print(f"  Stderr: {stderr}")
                 # 不再在这里抛出异常，让后续的 read_cfl3d_output 判断文件是否存在
                 # raise Exception(f"CFL3D process failed with code {p.returncode}. Stderr: {stderr}")
                 print(f"  Stderr (decoded with latin-1, errors ignored): {stderr}")

            return stdout, stderr

        except subprocess.TimeoutExpired:
             p.kill()
             stdout_bytes, stderr_bytes = p.communicate()
             stdout = stdout_bytes.decode('latin-1', errors='ignore')
             stderr = stderr_bytes.decode('latin-1', errors='ignore')
             raise TimeoutError(f"CFL3D process timed out after 600 seconds. Last stdout: {stdout}, Last stderr: {stderr}")


    except FileNotFoundError as fnf_error:
         # 特别处理文件未找到的错误
         print(f"[ERROR] run_cfl3d: {fnf_error}")
         raise # 重新抛出，让调用者知道
    except Exception as e:
        # 捕获其他可能的错误
        import traceback
        print(f"[ERROR] Exception during subprocess execution in run_cfl3d:")
        traceback.print_exc()
        raise Exception(f"运行CFL3D计算程序时出错: {str(e)}")

def read_cfl3d_output(cfl3d_dir, output_folder):
    """
    读取CFL3D输出文件
    """
    output_file = os.path.join(cfl3d_dir, output_folder, 'cfl3d.out')
    
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r') as f:
                content = f.readlines()
            
            # 查找结果标记行
            start_indices = [i + 1 for i, line in enumerate(content) 
                            if "***** FORCE AND MOMENT SUMMARIES - FINAL TIME STEP/MULTIGRID CYCLE *****" in line]
            
            if not start_indices:
                last_20_lines = ''.join(content[-20:]) if len(content) >= 20 else ''.join(content)
                return {"status": "warning", "message": "未找到结果标记行，可能计算未收敛", "data": last_20_lines}
            
            # 获取最后一次出现的标记索引
            start_index = start_indices[-1]
            
            # 提取关键结果数据
            extracted_lines = content[start_index:]
            extracted_text = ''.join(extracted_lines)
            
            # 尝试解析CL和CD值
            cl, cd = extract_force_coefficients(extracted_text)
            
            result = {
                "status": "success", 
                "data": extracted_text,
                "force_coefficients": {
                    "cl": cl,
                    "cd": cd
                }
            }
            
            # 获取Cp数据
            cp_data = extract_cp_from_cfl3d(cfl3d_dir, output_folder)
            
            if cp_data and cp_data.get("status") == "success":
                result["cp_data"] = {
                    "x_coords": cp_data["x_coords"],
                    "cp_values": cp_data["cp_values"]
                }
            
            return result
        
        except Exception as e:
            return {"status": "error", "message": f"读取输出文件失败: {str(e)}"}
    else:
        return {"status": "error", "message": "找不到输出文件，可能是CFL3D调用出现了问题"}

def extract_force_coefficients(output_text):
    """
    从CFL3D输出文本中提取最终的全局升力系数(CL)和阻力系数(CD)。
    查找 "SUMMARY OF FORCES AND MOMENTS - ALL GLOBAL BLOCKS" 部分。
    """
    cl = None
    cd = None
    in_global_summary = False
    lines = output_text.splitlines() # 按行分割

    for i, line in enumerate(lines):
        # 定位到全局力矩总结部分
        if "SUMMARY OF FORCES AND MOMENTS - ALL GLOBAL BLOCKS" in line:
            in_global_summary = True
            continue # 继续到下一行查找标题

        # 在全局总结部分查找包含 "CL" 和 "CD" 的标题行
        # （注意：这里假设标题行格式相对固定，包含这些大写字母）
        if in_global_summary and "CL" in line and "CD" in line and "CDp" in line:
            # 找到了标题行，下一行应该是数据
            if i + 1 < len(lines):
                data_line = lines[i + 1].strip()
                values = data_line.split()
                # 假设 CL 是第一个值，CD 是第二个值
                if len(values) >= 2:
                    try:
                        cl = float(values[0])
                        cd = float(values[1])
                        # 找到后即可退出循环，因为只需要最终结果
                        print(f"[DEBUG] extract_force_coefficients: Found CL={cl}, CD={cd}")
                        break
                    except ValueError:
                        print(f"[Warning] extract_force_coefficients: Failed to parse values in data line: {data_line}")
                        # 如果解析失败，可能格式有变，继续查找以防万一
                        pass
            else:
                # 找到了标题行但没有下一行数据
                 print("[Warning] extract_force_coefficients: Found header line but no data line followed.")
                 break # 停止查找


        # 如果已经离开了全局总结部分（例如遇到下一个 ***** 分隔符或其他标记），则停止查找
        # （这是一个可选的优化，防止在文件末尾误解析）
        # elif in_global_summary and "*****" in line:
        #    break

    if cl is None or cd is None:
         print("[Warning] extract_force_coefficients: Could not extract CL or CD from the global summary block.")

        
    return cl, cd

def apply_advanced_config(lines, advanced_config):
    """
    应用高级配置参数到输入文件
    
    Args:
        lines: 输入文件的行列表
        advanced_config: 高级配置字典
        
    Returns:
        修改后的行列表
    """
    # 详细的高级配置参数映射
    param_map = {
        # 流动参数
        "beta": {
            "marker": "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY",
            "index": 2,
            "default": 0.0,
            "description": "侧滑角，单位为度"
        },
        "tinf": {
            "marker": "XMACH     ALPHA      BETA  REUE,MIL   TINF,DR     IALPH    IHSTRY",
            "index": 4,
            "default": 460.0,
            "description": "自由流温度，单位为兰肯温度(°R)"
        },
        
        # 时间步长和求解控制
        "dt": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 0,
            "default": -0.8,
            "description": "时间步长，负值表示局部时间步长"
        },
        "irest": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 1,
            "default": 0,
            "description": "重启标志，0表示新计算，1表示从之前结果重启"
        },
        "iflagts": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 2,
            "default": 2000,
            "description": "最大时间步数"
        },
        "fmax": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 3,
            "default": 5.0,
            "description": "允许的最大CFL数"
        },
        "cfltau": {
            "marker": "DT     IREST   IFLAGTS      FMAX     IUNST    CFLTAU",
            "index": 5,
            "default": 7.5,
            "description": "CFL数调整系数"
        },
        
        # 参考值
        "sref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 0,
            "default": 1.0,
            "description": "参考面积，用于系数计算（一般二维翼型即为1）"
        },
        "cref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 1,
            "default": 1.0,
            "description": "参考弦长，用于力矩计算（一般二维翼型即为1）"
        },
        "bref": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 2,
            "default": 1.0,
            "description": "参考展长，用于力矩计算（一般二维翼型即为1）"
        },
        "xmc": {
            "marker": "SREF      CREF      BREF       XMC       YMC       ZMC",
            "index": 3,
            "default": 0.25,
            "description": "力矩参考点X坐标（一般二维翼型即为0.25）"
        },
        
        # 网格和计算控制
        "nplot3d": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 1,
            "default": 1,
            "description": "PLOT3D输出频率，正值表示每n步输出一次，0表示不输出"
        },
        "nprint": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 2,
            "default": -1,
            "description": "收敛信息打印频率，负值表示简化输出"
        },
        "nwrest": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 3,
            "default": 5000,
            "description": "重启文件写入频率"
        },
        "i2d": {
            "marker": "NGRID   NPLOT3D    NPRINT    NWREST      ICHK       I2D    NTSTEP       ITA",
            "index": 5,
            "default": 1,
            "description": "二维计算标志，1表示二维计算，0表示三维计算"
        },
        
        # 数值方法控制
        "iem": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 1,
            "default": 0,
            "description": "通量计算方法，0表示Roe方法，其他值对应不同的通量计算方案"
        },
        "iadvance": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 2,
            "default": 0,
            "description": "推进方法，0表示显式方法，1表示隐式方法"
        },
        "iforce": {
            "marker": "NCG       IEM  IADVANCE    IFORCE  IVISC(I)  IVISC(J)  IVISC(K)",
            "index": 3,
            "default": 333,
            "description": "力和力矩计算频率"
        },
        
        # 通量限制器
        "iflim_i": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 3,
            "default": 4,
            "description": "I方向通量限制器类型，4表示van Albada限制器"
        },
        "iflim_j": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 4,
            "default": 4,
            "description": "J方向通量限制器类型，4表示van Albada限制器"
        },
        "iflim_k": {
            "marker": "IDIAG(I)  IDIAG(J)  IDIAG(K)  IFLIM(I)  IFLIM(J)  IFLIM(K)",
            "index": 5,
            "default": 4,
            "description": "K方向通量限制器类型，4表示van Albada限制器"
        },
        
        # 人工黏性系数
        "rkap0_i": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 3,
            "default": 0.3333,
            "description": "I方向人工黏性系数"
        },
        "rkap0_j": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 4,
            "default": 0.3333,
            "description": "J方向人工黏性系数"
        },
        "rkap0_k": {
            "marker": "IFDS(I)   IFDS(J)   IFDS(K)  RKAP0(I)  RKAP0(J)  RKAP0(K)",
            "index": 5,
            "default": 0.3333,
            "description": "K方向人工黏性系数"
        },
        
        # 多重网格参数
        "mseq": {
            "marker": "MSEQ    MGFLAG    ICONSF       MTT      NGAM",
            "index": 0,
            "default": 3,
            "description": "多重网格层数，通常为2-3"
        },
        "mgflag": {
            "marker": "MSEQ    MGFLAG    ICONSF       MTT      NGAM",
            "index": 1,
            "default": 1,
            "description": "多重网格标志，1表示启用多重网格"
        },
        
        # 收敛控制
        "ncyc_level1": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 0,  # 第一个NCYC行
            "default": 1000,
            "description": "第一层网格最大迭代次数"
        },
        "ncyc_level2": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 1,  # 第二个NCYC行
            "default": 1000,
            "description": "第二层网格最大迭代次数"
        },
        "ncyc_level3": {
            "marker": "NCYC    MGLEVG     NEMGL     NITFO",
            "index": 0,
            "offset": 2,  # 第三个NCYC行
            "default": 1000,
            "description": "第三层网格最大迭代次数"
        }
    }
    
    # 处理每个高级参数
    for param_name, param_value in advanced_config.items():
        if param_name in param_map:
            config = param_map[param_name]
            marker = config["marker"]
            index = config["index"]
            offset = config.get("offset", 0)  # 处理多行同标记的情况
            
            # 查找参数所在行
            marker_lines = []
            for i, line in enumerate(lines):
                if marker in line:
                    marker_lines.append(i)
            
            if marker_lines and len(marker_lines) > offset:
                line_idx = marker_lines[offset]
                values = lines[line_idx + 1].split()
                if index < len(values):
                    # 设置参数值
                    values[index] = f"{param_value}"
                    lines[line_idx + 1] = "   ".join(values) + "\n"
    
    return lines

def extract_cp_from_cfl3d(cfl3d_dir, output_folder="output", j0=60, j1=361):
    """
    从CFL3D计算结果中提取压力系数(Cp)数据
    
    参数:
        cfl3d_dir: CFL3D目录路径
        output_folder: 输出文件夹名称
        j0: 下表面尾缘的j索引
        j1: 上表面尾缘的j索引
        
    返回:
        包含x坐标和cp值的字典
    """
    try:
        path = os.path.join(cfl3d_dir, output_folder)
        # 尝试使用cfl3d2模块读取plot3d数据
        succeed, field, foil, _ = cfl3d2.readprt_foil(path, j0=j0, j1=j1)
        
        if not succeed:
            return {"status": "error", "message": "读取plot3d文件失败"}
            
        # 提取翼型表面坐标和Cp值
        (x_coords, y_coords, cp_values, _, _) = foil
        
        # 将numpy数组转换为列表
        x_coords_list = x_coords.tolist() if hasattr(x_coords, 'tolist') else list(x_coords)
        y_coords_list = y_coords.tolist() if hasattr(y_coords, 'tolist') else list(y_coords)
        cp_values_list = cp_values.tolist() if hasattr(cp_values, 'tolist') else list(cp_values)
        
        # 构建点数据列表
        points = [{"x": x, "y": y, "cp": cp} for x, y, cp in zip(x_coords_list, y_coords_list, cp_values_list)]
        
        return {
            "status": "success",
            "method": "plot3d",
            "points": points,
            "x_coords": x_coords_list,
            "cp_values": cp_values_list
        }
    except Exception as plot3d_error:
        return {
            "status": "error", 
            "message": f"无法从plot3d提取Cp数据: {str(plot3d_error)}"
        }

def process_foil_file(input_foil_file, output_foil_file, num_points=1001, visualize=False,
                      n_base=8, n_diff=20, n_enhanced=3):
    """
    处理翼型文件，使用增强CST方法重构曲线并保存为特定格式。

    Args:
        input_foil_file (str): 输入翼型文件的路径。
        output_foil_file (str): 输出翼型文件的路径。
        num_points (int): 输出翼型曲线的点数（每个表面）。
        visualize (bool): 是否可视化处理结果。
        n_base (int): 基础翼型CST拟合阶数。
        n_diff (int): 差异曲线CST拟合阶数。
        n_enhanced (int): 用于增强前缘的差异参数数量。

    Returns:
        dict: 处理结果状态和消息。
    """
    try:
        if os.path.exists(output_foil_file):
            os.remove(output_foil_file)
        print(f"[Info] 已删除旧的输出文件: {output_foil_file}")
        # 1. 读取原始坐标
        xu_orig, yu_orig, xl_orig, yl_orig = read_coordinates(input_foil_file)

        # 2. 使用增强CST方法拟合和生成新坐标
        # 注意： enhanced_cst_foil_with_fit 内部生成 nn = num_points 个点
        x_new, yu_new, yl_new, t0, R0, _, _, _, _ = enhanced_cst_foil_with_fit(
            xu=xu_orig, yu=yu_orig, xl=xl_orig, yl=yl_orig,
            nn=num_points, # 直接生成所需点数
            t=None, tail=0.0, xn1=0.5, xn2=1.0,
            n_base=n_base, n_diff=n_diff, n_enhanced=n_enhanced
        )

        # 3. 可选：可视化比较
        if visualize:
            plt.figure(figsize=(10, 5))
            # 绘制原始点
            plt.scatter(xu_orig, yu_orig, label='Original Upper', marker='.', color='blue', s=10)
            plt.scatter(xl_orig, yl_orig, label='Original Lower', marker='.', color='red', s=10)
            # 绘制拟合后的曲线
            plt.plot(x_new, yu_new, label=f'Enhanced CST Upper (n_base={n_base}, n_enh={n_enhanced})', color='cyan', linewidth=1)
            plt.plot(x_new, yl_new, label=f'Enhanced CST Lower (n_base={n_base}, n_enh={n_enhanced})', color='magenta', linewidth=1)
            plt.title('Original vs Enhanced CST Fit')
            plt.xlabel('x/c')
            plt.ylabel('y/c')
            plt.axis('equal')
            plt.legend()
            plt.grid(True)
            # 保存在输出文件同目录下
            vis_filename = os.path.join(os.path.dirname(output_foil_file), 'foil_fit_visualization.png')
            plt.savefig(vis_filename, dpi=300)
            plt.close()
            print(f"可视化结果已保存至: {vis_filename}")

        # 4. 保存重构曲线到输出文件 (Tecplot 格式，两区域)
        os.makedirs(os.path.dirname(output_foil_file), exist_ok=True)
        header = "Variables= X Y \n"
        zone_line = f"\nzone i= {num_points}\n" # 每个区域有 num_points 个点

        with open(output_foil_file, 'w') as f:
            f.write(header)
            # 第一个区域：上表面 (x_new, yu_new)
            f.write(zone_line)
            upper_surface_data = np.vstack((x_new, yu_new)).T
            np.savetxt(f, upper_surface_data, fmt='%.8f', comments='') # 提高精度

            # 第二个区域：下表面 (x_new, yl_new)
            f.write(zone_line)
            lower_surface_data = np.vstack((x_new, yl_new)).T
            np.savetxt(f, lower_surface_data, fmt='%.8f', comments='') # 提高精度

        return {"status": "success", "message": "翼型文件使用增强CST处理成功", "t0": t0, "R0": R0}

    except FileNotFoundError:
        return {"status": "error", "message": f"输入翼型文件未找到: {input_foil_file}"}
    except Exception as e:
        import traceback
        print(traceback.format_exc()) # 打印详细错误信息
        return {"status": "error", "message": f"翼型文件处理失败: {str(e)}"}

def modify_cgrid_parameters(cgrid_file_path, params):
    """
    修改Cgrid输入文件中的参数。

    Args:
        cgrid_file_path: Cgrid输入文件路径。
        params: 包含要修改参数的字典。

    Returns:
        dict: 操作结果状态和消息。
    """
    try:
        with open(cgrid_file_path, 'r') as f:
            lines = f.readlines()

        # 确保文件至少有4行（2个标题行 + 2个数据行）
        if len(lines) < 4:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 行数不足 (需要至少4行)"}


        # --- 修改开始: 调整参数映射 ---
        # line_idx 指向实际的数据行索引
        param_map = {
            # 网格尺寸参数 (数据在第 1 行，索引为 1)
            "foil_cells": {"data_line_idx": 1, "index": 0, "default": 151, "description": "翼型上下表面的网格单元数量"},
            "wake_cells": {"data_line_idx": 1, "index": 1, "default": 61, "description": "从翼型尾缘到远场的网格单元数量(流向方向)"},
            "grow_cells": {"data_line_idx": 1, "index": 2, "default": 101, "description": "从翼型表面到远场的网格单元数量(y+方向)"},

            # 物理参数 (数据在第 3 行，索引为 3)
            "y_plus":     {"data_line_idx": 3, "index": 0, "default": 0.9, "description": "指定的y+值"},
            "far_radius": {"data_line_idx": 3, "index": 1, "default": 15.0, "description": "远场半径"},
            "u_inf":      {"data_line_idx": 3, "index": 2, "default": 200.0, "description": "自由流来流速度U_inf(m/s)"},
            "density":    {"data_line_idx": 3, "index": 3, "default": 1.1, "description": "自由流密度rou(kg/m³)"},
            "reynolds":   {"data_line_idx": 3, "index": 4, "default": 3e6, "description": "自由流单位雷诺数Re(/m)"}
        }
        # --- 修改结束 ---

        # 先读取并分割好两个数据行
        try:
             data_values_line1 = lines[1].strip().split()
             data_values_line3 = lines[3].strip().split()
        except IndexError:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 格式错误，无法读取数据行"}


        # 检查数据行是否包含足够的值
        if len(data_values_line1) < 3:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 第一数据行格式错误 (需要至少3个值)"}
        if len(data_values_line3) < 5:
             return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 第二数据行格式错误 (需要至少5个值)"}


        # 修改参数值
        modified = False
        for param_name, param_value in params.items():
            if param_name in param_map:
                config = param_map[param_name]
                data_line_idx = config["data_line_idx"]
                col_idx = config["index"]

                try:
                    if data_line_idx == 1:
                        # 修改第一行数据的值
                        data_values_line1[col_idx] = str(param_value)
                        modified = True
                    elif data_line_idx == 3:
                        # 修改第二行数据的值
                        data_values_line3[col_idx] = str(param_value)
                        modified = True
                except IndexError:
                     # 这个错误理论上不应该发生，因为前面检查了长度，但以防万一
                      print(f"[Warning] modify_cgrid_parameters: 尝试访问索引 {col_idx} 失败，参数 '{param_name}' 未修改。")
                      continue # 跳过这个参数

        # 如果有修改，则重建数据行并写回文件
        if modified:
            try:
                # 重建第一行数据 (保持缩进格式)
                lines[1] = f"                {data_values_line1[0]:<12} {data_values_line1[1]:<5} {data_values_line1[2]:<5}\n" # 调整间距以匹配原格式
                # 重建第二行数据 (保持缩进格式)
                lines[3] = f" {data_values_line3[0]:<19} {data_values_line3[1]:<14} {data_values_line3[2]:<19} {data_values_line3[3]:<17} {data_values_line3[4]:<10}\n" # 调整间距

                with open(cgrid_file_path, 'w') as f:
                    f.writelines(lines)
                return {"status": "success", "message": "Cgrid参数修改成功"}
            except IndexError:
                 # 捕获重建字符串时可能发生的索引错误
                 return {"status": "error", "message": f"修改Cgrid参数失败: 重建数据行时发生错误"}
        else:
             return {"status": "success", "message": "Cgrid参数无需修改 (未提供有效参数)"}


    except FileNotFoundError:
         return {"status": "error", "message": f"修改Cgrid参数失败: 文件 '{cgrid_file_path}' 未找到"}
    except Exception as e:
        import traceback
        print(f"[DEBUG] Error in modify_cgrid_parameters: {e}")
        # traceback.print_exc() # 取消注释以获得更详细的堆栈跟踪
        return {"status": "error", "message": f"修改Cgrid参数失败: {str(e)}"}

def create_default_cgrid_config(cgrid_file_path):
    """
    创建默认的cgrid.txt文件
    """
    with open(cgrid_file_path, 'w') as f:
        f.write("Cell numbers: Foil surface, Wake, Grow\n                151           61    101\n")    
        f.write(" y+ critical, Radius far field, Uinf (200 m/s), rou (1.1 kg/m3),    Re (/m)\n 0.9                15.0           200.0              1.1              3e6\n")

def run_cgrid_exe(cfl3d_dir):
    """
    运行网格生成程序cgrid.exe (或 cgrid)
    """
    try:
        # 检查是否存在cgrid.txt文件
        cgrid_file_path = os.path.join(cfl3d_dir, 'cgrid.txt')
        if not os.path.exists(cgrid_file_path):
            # 创建默认的cgrid.txt文件
            create_default_cgrid_config(cgrid_file_path)

        # --- 修改开始: 删除旧的网格文件 ---
        output_grid_file = os.path.join(cfl3d_dir, 'cfl3d.xyz')
        if os.path.exists(output_grid_file):
            try:
                os.remove(output_grid_file)
                print(f"[清理] 已删除旧的网格文件: {output_grid_file}")
            except Exception as e:
                print(f"[警告] 删除旧的网格文件失败: {output_grid_file}, error: {e}")
                # 即使删除失败，仍然尝试继续
        # --- 修改结束 ---


        # --- 获取 cgrid 的绝对路径 ---
        base_run_dir = os.getcwd()
        absolute_cfl3d_dir = os.path.abspath(os.path.join(base_run_dir, cfl3d_dir))
        cgrid_exe_name = None
        if os.path.exists(os.path.join(absolute_cfl3d_dir, 'cgrid')):
            cgrid_exe_name = 'cgrid'
        elif os.path.exists(os.path.join(absolute_cfl3d_dir, 'cgrid.exe')):
            cgrid_exe_name = 'cgrid.exe'

        if not cgrid_exe_name:
             raise FileNotFoundError(f"Cgrid executable ('cgrid' or 'cgrid.exe') not found in {absolute_cfl3d_dir}")

        cgrid_executable_absolute = os.path.join(absolute_cfl3d_dir, cgrid_exe_name)
        print(f"[DEBUG] run_cgrid_exe: Trying to execute absolute path: {cgrid_executable_absolute}")
        # --- 获取结束 ---

        # --- 修改: 使用绝对路径执行 ---
        result = subprocess.run(
            [cgrid_executable_absolute], # 使用绝对路径列表
            cwd=absolute_cfl3d_dir,      # cwd 设置为包含 exe 的目录
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            #text=True, # 使用 text=True
            timeout=120 # 添加超时
        )
        # --- 修改结束 ---
        stdout = result.stdout.decode('latin-1', errors='ignore')
        stderr = result.stderr.decode('latin-1', errors='ignore')
        
        if result.returncode == 0:
            print("[DEBUG] run_cgrid_exe: Process finished successfully.")
            print(f"  Stdout (decoded): {stdout}")
            return {"status": "success", "message": "网格生成成功"}
        else:
            print(f"[ERROR] run_cgrid_exe: Process failed with code {result.returncode}.")
            #stderr = result.stderr if result.stderr else "未知错误"
            #print(f"  Stderr: {stderr}")
            print(f"  Stderr (decoded): {stderr}")
            return {"status": "error", "message": f"网格生成失败: {stderr}"}

    except FileNotFoundError as fnf_error:
        print(f"[ERROR] run_cgrid_exe: {fnf_error}")
        return {"status": "error", "message": f"运行网格生成程序时出错: {fnf_error}"}
    except subprocess.TimeoutExpired:
         return {"status": "error", "message": "运行网格生成程序超时"}
    except Exception as e:
        import traceback
        print(f"[ERROR] Exception during subprocess execution in run_cgrid_exe:")
        traceback.print_exc()
        return {"status": "error", "message": f"运行网格生成程序时出错: {str(e)}"}

def mesh_generation_execute(input_foil_file, num_points=1001, visualize=False, grid_params=None,
                            # 添加CST相关的可选参数
                            n_base=8, n_diff=20, n_enhanced=3):
    """
    执行翼型处理和网格生成

    Args:
        input_foil_file: 输入翼型文件的路径
        num_points: 重构曲线的点数 (每个表面)
        visualize: 是否可视化处理结果
        grid_params: 网格生成参数字典
        n_base, n_diff, n_enhanced: 增强CST拟合参数

    Returns:
        处理和网格生成结果信息
    """
    cfl3d_dir = './cfl3d'
    output_foil_file = os.path.join(cfl3d_dir, 'foil.dat')

    # 处理翼型文件，传递CST参数
    processing_result = process_foil_file(
        input_foil_file, output_foil_file, num_points, visualize,
        n_base=n_base, n_diff=n_diff, n_enhanced=n_enhanced
    )

    # 如果翼型处理失败，则提前返回
    if processing_result["status"] != "success":
        return {
            "processing_result": processing_result,
            "grid_generation_result": {"status": "skipped", "message": "翼型处理失败，跳过网格生成"},
            "output_foil_file": None
        }


    # 如果提供了网格参数，修改cgrid.txt文件
    param_result_msg = "未修改Cgrid参数（未提供grid_params）"
    if grid_params:
        cgrid_file_path = os.path.join(cfl3d_dir, 'cgrid.txt')
        # 确保cgrid.txt文件存在
        if not os.path.exists(cgrid_file_path):
            create_default_cgrid_config(cgrid_file_path)

        # 修改网格参数
        param_result = modify_cgrid_parameters(cgrid_file_path, grid_params)
        param_result_msg = param_result["message"]
        if param_result["status"] != "success":
            # 参数修改失败，但仍然尝试生成网格（使用现有或默认参数）
             print(f"[警告] 修改Cgrid参数失败: {param_result_msg}，将尝试使用现有参数生成网格。")


    # 运行网格生成程序
    grid_result = run_cgrid_exe(cfl3d_dir)

    return {
        "processing_result": processing_result,
        "cgrid_param_modification_result": param_result_msg,
        "grid_generation_result": grid_result,
        "output_foil_file": output_foil_file
    }
        