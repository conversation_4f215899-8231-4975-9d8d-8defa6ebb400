"""
气动参数转换工具，用于转换雷诺数、马赫数和空速
"""

from agent_r1.tool.tool_base import Tool
import json

class AerodynamicParametersTool(Tool):
    """
    用于转换雷诺数、空速和马赫数的工具类
    """
    
    def __init__(self):
        """
        初始化气动参数转换工具
        """
        name = "aerodynamic_parameters"
        description = "转换雷诺数、空速和马赫数等气动参数。可以根据已知参数计算未知参数。"
        parameters = {
            "type": "object",
            "properties": {
                "re": {
                    "type": ["number", "null"],
                    "description": "雷诺数（可选）"
                },
                "airspeed": {
                    "type": ["number", "null"],
                    "description": "空速，单位为m/s（可选）"
                },
                "mach": {
                    "type": ["number", "null"],
                    "description": "马赫数（可选）"
                },
                "alpha": {
                    "type": ["number", "null"],
                    "description": "攻角，单位为度（可选）",
                    "default": 0
                },
                "a": {
                    "type": "number",
                    "description": "声速，单位为m/s",
                    "default": 336.4
                },
                "rho": {
                    "type": "number",
                    "description": "空气密度，单位为kg/m³",
                    "default": 1.17
                },
                "chord_length": {
                    "type": "number",
                    "description": "特征长度（如翼型弦长），单位为m",
                    "default": 1.0
                },
                "mu": {
                    "type": "number",
                    "description": "空气动力粘度，单位为Pa·s",
                    "default": 1.81e-5
                }
            }
        }
        
        super().__init__(name, description, parameters)
    
    def _execute(self, re=None, airspeed=None, mach=None, alpha=None, a=336.4, rho=1.17, chord_length=1.0, mu=1.81e-5):
        """
        执行参数转换
        
        Args:
            re: 雷诺数（可选）
            airspeed: 空速，单位为m/s（可选）
            mach: 马赫数（可选）
            alpha: 攻角，单位为度（可选）
            a: 声速，单位为m/s
            rho: 空气密度，单位为kg/m³
            chord_length: 特征长度（如翼型弦长），单位为m
            mu: 空气动力粘度，单位为Pa·s
            
        Returns:
            计算得到的各个参数值
        """
        # 处理零值或None
        if airspeed is not None and airspeed == 0:
            airspeed = None
        if mach is not None and mach == 0:
            mach = None
        if re is not None and re == 0:
            re = None
            
        result = {}  # 存储所有计算值
        
        # 计算空速
        if airspeed is None:
            if mach is not None:
                airspeed = mach * a  # 根据马赫数计算空速
            elif re is not None:
                airspeed = re * mu / (rho * chord_length)  # 根据雷诺数计算空速
            else:
                re = 0
                airspeed = 0
        
        # 计算马赫数
        if mach is None:
            mach = airspeed / a  # 根据空速计算马赫数
        
        # 计算雷诺数
        if re is None:
            re = (rho * airspeed * chord_length) / mu
        
        # 设置默认攻角
        if alpha is None:
            alpha = 0
            
        # 存储结果
        result["Mach"] = mach
        result["Airspeed"] = airspeed
        result["Reynolds_number"] = re
        result["rho"] = rho
        result["chord_length"] = chord_length
        result["mu"] = mu
        result["speed_of_sound"] = a
        result["alpha"] = alpha
        
        return result 

    def execute(self, args: dict) -> str:
        """
        执行参数转换并返回 JSON 字符串结果。
        """
        try:
            # 调用内部逻辑
            result_dict = self._execute(**args)
            # 总是视为成功，因为它是纯计算
            result_dict['status'] = 'success'
            return json.dumps(result_dict, indent=2)
        except TypeError as e:
            # 捕获可能的参数类型错误
            return json.dumps({'status': 'error', 'message': f"参数类型错误: {e}"})
        except Exception as e:
            # 捕获其他计算错误
            return json.dumps({'status': 'error', 'message': f"计算错误: {e}"})

    def calculate_reward(self, args: dict, result: str) -> float:
        """
        计算气动参数工具调用的奖励。
        成功调用给予微小正反馈，错误调用给予小惩罚。
        """
        try:
            result_data = json.loads(result)
            if result_data.get("status") == "success":
                 return 0.05 # 成功计算，微小奖励
            else:
                 # 内部错误（如类型错误）
                 return -0.1 # 小惩罚
        except (json.JSONDecodeError, Exception):
            # 结果格式错误或意外异常
            return -0.2 # 稍大的惩罚 