# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
Note that we don't combine the main with ray_trainer as ray_trainer is used by other main.
"""
from .agent_ray_trainer import RayAgent<PERSON>rainer

from agent_r1.tool import ToolEnv
from agent_r1.tool.tools import _default_tools

import ray
import hydra

from verl import DataProto
from .reward_score import _default_compute_score_format, _default_compute_score_answer, _default_compute_score_format_answer
import torch

class RewardManager():
    """The reward manager.
    """

    def __init__(self, tokenizer, num_examine) -> None:
        self.tokenizer = tokenizer
        self.num_examine = num_examine  # the number of batches of decoded responses to print to the console

    def __call__(self, data: DataProto):
        """We will expand this function gradually based on the available datasets"""

        # If there is rm score, we directly return rm score. Otherwise, we compute via rm_score_fn
        if 'rm_scores' in data.batch.keys():
            return data.batch['rm_scores']

        # Initialize reward tensor and lists
        # Use the first item's response shape to determine reward tensor shape if possible
        # Fallback to a potentially incorrect shape if batch is empty (should not happen in normal flow)
        if len(data) > 0:
             reward_tensor_shape = data.batch['responses'][0].shape
             reward_tensor = torch.zeros((len(data), reward_tensor_shape[0]), dtype=torch.float32) # Shape [batch_size, response_len]
        else:
             print("[RewardManager Warning] Input DataProto is empty. Returning empty tensors/lists.")
             return torch.empty(0), [], [] # Return empty results for empty input


        answer_lst = []
        format_lst = []
        already_print_data_sources = {}
        batch_has_error = False # Flag to track if any item caused an error

        for i in range(len(data)):
            score = -1.0  # Default score in case of error for this item
            answer_score = 0.0
            format_score = 0.0
            valid_response_length = 0 # Initialize here
            sequences_str = "[ERROR: Could not decode sequence]" # Default string

            try:
                data_item = data[i]  # DataProtoItem

                # --- Safely access non_tensor_batch ---
                non_tensor_data = getattr(data_item, 'non_tensor_batch', None)
                if non_tensor_data is None:
                    print(f"[RewardManager ERROR] Data item index {i} is missing 'non_tensor_batch'. Assigning score -1.0.")
                    batch_has_error = True
                    # Keep default scores, append them later outside try-except
                    reward_tensor[i, :] = -1.0 # Assign -1 to all steps for this item? Or just the last one? Let's choose last. Needs review.
                    # We need valid_response_length for the index, calculate it first if possible
                    prompt_ids = data_item.batch['prompts']
                    prompt_length = prompt_ids.shape[-1]
                    response_ids = data_item.batch['responses']
                    valid_response_length = data_item.batch['attention_mask'][prompt_length:].sum().item() # Use .item()
                    if valid_response_length > 0 :
                        reward_tensor[i, valid_response_length - 1] = -1.0
                    else:
                         # Handle case with zero length response if needed, maybe log warning
                         pass # Score is already -1 implicitly if length is 0

                    answer_lst.append(answer_score) # Append default 0.0
                    format_lst.append(format_score) # Append default 0.0
                    continue # Skip to next item

                reward_model_data = non_tensor_data.get('reward_model')
                if reward_model_data is None:
                    print(f"[RewardManager ERROR] Data item index {i} non_tensor_batch is missing 'reward_model' key. Assigning score -1.0.")
                    batch_has_error = True
                    # Assign score -1 (similar logic as above)
                    prompt_ids = data_item.batch['prompts']
                    prompt_length = prompt_ids.shape[-1]
                    response_ids = data_item.batch['responses']
                    valid_response_length = data_item.batch['attention_mask'][prompt_length:].sum().item()
                    if valid_response_length > 0 :
                         reward_tensor[i, valid_response_length - 1] = -1.0
                    answer_lst.append(answer_score)
                    format_lst.append(format_score)
                    continue

                ground_truth = reward_model_data.get('ground_truth')
                if ground_truth is None:
                    print(f"[RewardManager ERROR] Data item index {i} reward_model data is missing 'ground_truth' key. Assigning score -1.0.")
                    batch_has_error = True
                     # Assign score -1 (similar logic as above)
                    prompt_ids = data_item.batch['prompts']
                    prompt_length = prompt_ids.shape[-1]
                    response_ids = data_item.batch['responses']
                    valid_response_length = data_item.batch['attention_mask'][prompt_length:].sum().item()
                    if valid_response_length > 0 :
                         reward_tensor[i, valid_response_length - 1] = -1.0
                    answer_lst.append(answer_score)
                    format_lst.append(format_score)
                    continue

                data_source = non_tensor_data.get('data_source', 'unknown') # Use .get for safety

                # --- Decode sequence (keep this inside try-except too) ---
                prompt_ids = data_item.batch['prompts']
                prompt_length = prompt_ids.shape[-1]
                response_ids = data_item.batch['responses']

                # Calculate valid lengths (ensure they are scalar integers)
                valid_prompt_length = data_item.batch['attention_mask'][:prompt_length].sum().item()
                valid_response_length = data_item.batch['attention_mask'][prompt_length:].sum().item() # Use .item()

                valid_prompt_ids = prompt_ids[-valid_prompt_length:] if valid_prompt_length > 0 else torch.empty(0, dtype=torch.long)
                valid_response_ids = response_ids[:valid_response_length].long() if valid_response_length > 0 else torch.empty(0, dtype=torch.long)

                # decode
                sequences = torch.cat((valid_prompt_ids, valid_response_ids))
                # Handle empty sequence case
                if sequences.numel() > 0:
                     sequences_str = self.tokenizer.decode(sequences, skip_special_tokens=False)
                     # Assuming pad token handling is correct, keep as is
                     pad_token_id = self.tokenizer.pad_token_id
                     if pad_token_id is not None: # Check if pad_token_id exists
                          pad_token_str = self.tokenizer.decode([pad_token_id])
                          if pad_token_str: # Check if pad token decodes to non-empty string
                             sequences_str = sequences_str.split(pad_token_str)[0]
                else:
                     sequences_str = "" # Empty string for empty sequence


                # --- Call reward computation functions ---
                # TODO: Wrap these calls in try-except as well if they can fail
                # For now, assume they return valid floats or handle errors internally
                score = _default_compute_score_format_answer(data_source=data_source, solution_str=sequences_str, ground_truth=ground_truth)
                answer_score = _default_compute_score_answer(data_source=data_source, solution_str=sequences_str, ground_truth=ground_truth)
                format_score = _default_compute_score_format(data_source=data_source, solution_str=sequences_str)

                # Assign the calculated score to the last valid token position
                if valid_response_length > 0:
                    # Ensure score is a float before assigning
                    if not isinstance(score, (float, int)):
                         print(f"[RewardManager Warning] Score calculation for item {i} returned non-numeric value: {score}. Assigning -1.0.")
                         score = -1.0
                         batch_has_error = True
                    reward_tensor[i, valid_response_length - 1] = float(score)
                # Else: if valid_response_length is 0, score remains 0 at all positions for this item (default tensor init)

                # --- Logging (conditionally) ---
                if data_source not in already_print_data_sources:
                    already_print_data_sources[data_source] = 0
                if already_print_data_sources[data_source] < self.num_examine:
                    already_print_data_sources[data_source] += 1
                    print(f"--- RewardManager Debug (Item {i}, Source: {data_source}) ---")
                    print(f"[prompt+response ({len(sequences)} tokens)]", sequences_str)
                    print("[ground_truth]", ground_truth)
                    print(f"[score] Total: {score:.4f}, Answer: {answer_score:.4f}, Format: {format_score:.4f}")
                    print("-" * 20)


            except KeyError as e:
                print(f"[RewardManager FATAL ERROR] KeyError accessing data for item index {i}: {e}. Assigning score -1.0.")
                batch_has_error = True
                # Ensure reward_tensor is updated even on KeyError if possible
                if 'valid_response_length' in locals() and valid_response_length > 0:
                     reward_tensor[i, valid_response_length - 1] = -1.0
                # Use default scores 0.0 for lists
            except Exception as e:
                 print(f"[RewardManager FATAL ERROR] Unexpected error processing item index {i}: {e}")
                 import traceback
                 traceback.print_exc() # Print full traceback for unexpected errors
                 batch_has_error = True
                 # Ensure reward_tensor is updated even on error if possible
                 if 'valid_response_length' in locals() and valid_response_length > 0:
                     reward_tensor[i, valid_response_length - 1] = -1.0
                 # Use default scores 0.0 for lists

            # Append scores outside the try-except block to ensure lists have correct length
            answer_lst.append(answer_score)
            format_lst.append(format_score)


        # Maybe raise an error if the whole batch failed? Depends on desired behavior.
        # if batch_has_error:
        #     print("[RewardManager Warning] Errors occurred while processing some items in the batch.")


        # The original function signature implies returning reward_tensor, answer_lst, format_lst
        # Ensure reward_tensor has the correct shape [batch_size, response_len]
        return reward_tensor, answer_lst, format_lst

@hydra.main(config_path='config', config_name='agent_trainer', version_base=None)
def main(config):
    run_agent(config)


def run_agent(config) -> None:

    if not ray.is_initialized():
        import os
        # Get number of GPUs requested for the node from the config/SLURM
        # Assuming trainer.n_gpus_per_node reflects the allocation for this process's node
        num_gpus_to_init = config.trainer.n_gpus_per_node

        # Double-check with environment if possible (SLURM usually sets CUDA_VISIBLE_DEVICES)
        cuda_visible_devices = os.environ.get("CUDA_VISIBLE_DEVICES")
        if cuda_visible_devices:
            detected_gpus = len(cuda_visible_devices.split(','))
            if detected_gpus != num_gpus_to_init:
                print(f"Warning: Configured GPUs ({num_gpus_to_init}) differs from CUDA_VISIBLE_DEVICES count ({detected_gpus}). Using configured value.")
                # Or, choose to trust CUDA_VISIBLE_DEVICES:
                # num_gpus_to_init = detected_gpus

        print(f"Initializing Ray with num_gpus={num_gpus_to_init}")

        # Explicitly tell ray.init how many GPUs to expect on this node
        ray.init(
            num_cpus=8,
            num_gpus=num_gpus_to_init,            
            runtime_env={
                'env_vars': {
                    'TOKENIZERS_PARALLELISM': 'true',
                    'NCCL_DEBUG': 'WARN',
                    'VLLM_LOGGING_LEVEL': 'WARN'
                }
            }
        )

    ray.get(main_task.remote(config))


@ray.remote(num_cpus=1, max_retries=0) # <-- Set max_retries=0
def main_task(config):
    from verl.utils.fs import copy_to_local
    # print initial config
    from pprint import pprint
    from omegaconf import OmegaConf
    pprint(OmegaConf.to_container(config, resolve=True))  # resolve=True will eval symbol values
    OmegaConf.resolve(config)

    # download the checkpoint from hdfs
    local_path = copy_to_local(config.actor_rollout_ref.model.path)

    # instantiate tokenizer
    from verl.utils import hf_tokenizer, hf_processor
    tokenizer = hf_tokenizer(local_path)
    processor = hf_processor(local_path, use_fast=True)  # used for multimodal LLM, could be none

    # define worker classes
    if config.actor_rollout_ref.actor.strategy == 'fsdp':
        assert config.actor_rollout_ref.actor.strategy == config.critic.strategy
        from .fsdp_workers import ActorRolloutRefWorker, CriticWorker
        from verl.single_controller.ray import RayWorkerGroup
        ray_worker_group_cls = RayWorkerGroup

    elif config.actor_rollout_ref.actor.strategy == 'megatron':
        assert config.actor_rollout_ref.actor.strategy == config.critic.strategy
        from verl.workers.megatron_workers import ActorRolloutRefWorker, CriticWorker
        from verl.single_controller.ray.megatron import NVMegatronRayWorkerGroup
        ray_worker_group_cls = NVMegatronRayWorkerGroup

    else:
        raise NotImplementedError

    from .agent_ray_trainer import ResourcePoolManager, Role

    role_worker_mapping = {
        Role.ActorRollout: ray.remote(ActorRolloutRefWorker),
        Role.Critic: ray.remote(CriticWorker),
        Role.RefPolicy: ray.remote(ActorRolloutRefWorker)
    }

    global_pool_id = 'global_pool'
    resource_pool_spec = {
        global_pool_id: [config.trainer.n_gpus_per_node] * config.trainer.nnodes,
    }
    mapping = {
        Role.ActorRollout: global_pool_id,
        Role.Critic: global_pool_id,
        Role.RefPolicy: global_pool_id,
    }

    # we should adopt a multi-source reward function here
    # - for rule-based rm, we directly call a reward score
    # - for model-based rm, we call a model
    # - for code related prompt, we send to a sandbox if there are test cases
    # - finally, we combine all the rewards together
    # - The reward type depends on the tag of the data
    if config.reward_model.enable:
        if config.reward_model.strategy == 'fsdp':
            from verl.workers.fsdp_workers import RewardModelWorker
        elif config.reward_model.strategy == 'megatron':
            from verl.workers.megatron_workers import RewardModelWorker
        else:
            raise NotImplementedError
        role_worker_mapping[Role.RewardModel] = ray.remote(RewardModelWorker)
        mapping[Role.RewardModel] = global_pool_id

    resource_pool_manager = ResourcePoolManager(resource_pool_spec=resource_pool_spec, mapping=mapping)

    tools = _default_tools(config.tool.env)
    env = ToolEnv(tools=tools, max_turns=config.tool.max_turns)

    trainer = RayAgentTrainer(config=config,
                            tokenizer=tokenizer,
                            processor=processor,
                            role_worker_mapping=role_worker_mapping,
                            resource_pool_manager=resource_pool_manager,
                            ray_worker_group_cls=ray_worker_group_cls,
                            reward_fn=RewardManager(tokenizer=tokenizer, num_examine=0),
                            val_reward_fn=RewardManager(tokenizer=tokenizer, num_examine=1),
                            env=env)
    trainer.init_workers()
    trainer.fit()


if __name__ == '__main__':
    main()
