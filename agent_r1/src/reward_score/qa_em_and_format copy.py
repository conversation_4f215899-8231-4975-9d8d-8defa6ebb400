# agent_r1/src/reward_score/cfd_reward.py
# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import re
import json
import math
import numpy as np # 确保导入 numpy
from typing import Dict, Any, Optional, Tuple
import traceback # 引入 traceback

def parse_tool_call(assistant_block: str) -> Optional[Dict[str, Any]]:
    """从 assistant block 中解析 <tool_call> 内容"""
    tool_call_match = re.search(r'<tool_call>(.*?)</tool_call>', assistant_block, re.DOTALL)
    if tool_call_match:
        try:
            # 假设 tool_call 内容是 JSON 格式
            return json.loads(tool_call_match.group(1).strip())
        except json.JSONDecodeError:
            print(f"[DEBUG] Failed to parse tool_call JSON: {tool_call_match.group(1).strip()}")
            return None
    return None

def parse_tool_response(tool_block: str) -> Optional[Dict[str, Any]]:
    """解析 <tool_response> 内容"""
    tool_response_match = re.search(r'<tool_response>(.*?)</tool_response>', tool_block, re.DOTALL)
    if tool_response_match:
        try:
            # 假设 tool_response 内容是 JSON 格式
            return json.loads(tool_response_match.group(1).strip())
        except json.JSONDecodeError:
            print(f"[DEBUG] Failed to parse tool_response JSON: {tool_response_match.group(1).strip()}")
            return None
    return None

def parse_solution_str(solution_str: str) -> Tuple[list, list]:
    """解析完整的 solution_str，分离 assistant 和 tool blocks"""
    assistant_blocks = re.findall(r'<\|im_start\|>assistant\n(.*?)\n<\|im_end\|>', solution_str, re.DOTALL)
    tool_blocks = re.findall(r'<\|im_start\|>tool\n(.*?)\n<\|im_end\|>', solution_str, re.DOTALL)
    return assistant_blocks, tool_blocks

def find_leading_edge_index(x_coords):
    """找到前缘点（x 坐标最小值）的索引"""
    if not isinstance(x_coords, np.ndarray):
        x_coords = np.array(x_coords)
    return np.argmin(x_coords)

def split_surfaces(x_coords, y_coords_or_cp, le_index):
    """根据前缘索引将数据分为上下表面"""
    # 假设数据点顺序是：下表面尾缘 -> 下表面前缘 -> 上表面前缘 -> 上表面尾缘
    # 或者 上表面尾缘 -> 上表面前缘 -> 下表面前缘 -> 下表面尾缘
    # 关键是前缘点 (le_index) 是分割点

    # 尝试判断点序：如果 le_index 后面的 x 比前面的 x 大，认为是 "下->上" 顺序
    # （注意边界情况，le_index 可能为 0 或 len-1）
    is_lower_first = False
    if le_index > 0 and le_index < len(x_coords) - 1:
         if x_coords[le_index+1] > x_coords[le_index-1]: # 简单判断趋势
             is_lower_first = True
    elif le_index == 0 and len(x_coords) > 1: # 前缘在第一个点
         is_lower_first = True # 假设是下->上
    # else: le_index 在最后或只有一个点，难以判断，默认上->下

    if is_lower_first:
        # 下表面: 从索引 0 到 le_index (包含)
        x_lower = x_coords[:le_index+1]
        ycp_lower = y_coords_or_cp[:le_index+1]
        # 上表面: 从索引 le_index 到末尾
        x_upper = x_coords[le_index:]
        ycp_upper = y_coords_or_cp[le_index:]
    else: # 假设是 上->下 顺序
        # 上表面: 从索引 0 到 le_index (包含)
        x_upper = x_coords[:le_index+1]
        ycp_upper = y_coords_or_cp[:le_index+1]
        # 下表面: 从索引 le_index 到末尾
        x_lower = x_coords[le_index:]
        ycp_lower = y_coords_or_cp[le_index:]

    # --- 确保 x 坐标单调递增 ---
    # 上表面通常是从 x=0 到 x=1 (或 x=1 到 x=0)，需要统一
    if x_upper[0] > x_upper[-1]: # 如果是 1 -> 0 顺序，则反转
        x_upper = x_upper[::-1]
        ycp_upper = ycp_upper[::-1]

    # 下表面通常是从 x=1 到 x=0 (或 x=0 到 x=1)，需要统一
    if x_lower[-1] < x_lower[0]: # 如果是 1 -> 0 顺序，则反转
         x_lower = x_lower[::-1]
         ycp_lower = ycp_lower[::-1]

    return x_upper, ycp_upper, x_lower, ycp_lower

def compute_rmse_for_surface(exp_x, exp_cp, calc_x, calc_cp):
    """为单个表面计算 RMSE（包含插值）"""
    if len(exp_x) == 0 or len(calc_x) == 0:
         print("[DEBUG] Surface RMSE: Empty data for surface.")
         return np.inf # 返回无穷大表示无法计算

    try:
        # 确保计算数据的 x 是排序好的且唯一的
        unique_calc_x, unique_indices = np.unique(calc_x, return_index=True)
        unique_calc_cp = calc_cp[unique_indices]

        if not np.all(np.diff(unique_calc_x) > 0):
             print("[ERROR] Surface RMSE: Calculated X for surface not strictly monotonic after unique.")
             return np.inf

        # 插值
        interpolated_calc_cp = np.interp(exp_x, unique_calc_x, unique_calc_cp)

        # 计算 RMSE
        rmse = np.sqrt(np.mean((exp_cp - interpolated_calc_cp)**2))
        return rmse

    except Exception as e:
        print(f"[ERROR] Surface RMSE: Error during interpolation/RMSE: {e}")
        return np.inf

def compute_score_result_accuracy(tool_responses: list, experimental_data: Dict[str, Any]) -> float:
    """
    评估计算结果 Cp 分布与实验数据的准确性。
    使用表面分离和插值处理坐标点不匹配的情况。
    从工具返回的嵌套结构中提取 Cp 和 X 坐标 (使用正确的键名)。
    """
    if not tool_responses or not experimental_data:
        print("[DEBUG] Accuracy: No tool responses or experimental data.")
        return 0.0

    # 查找最后一个成功的 CFL3D 模拟结果 (同前)
    last_successful_simulation_output = None
    for response_str in reversed(tool_responses):
        response_data = parse_tool_response(response_str)
        calc_output = response_data.get("calculation_output") if isinstance(response_data, dict) else None
        if calc_output and isinstance(calc_output, dict) and calc_output.get("status") == "success":
            last_successful_simulation_output = calc_output
            break

    if not last_successful_simulation_output:
        print("[DEBUG] Accuracy: No successful CFL3D simulation result found.")
        return 0.0

    # --- 获取 Cp 数据和对应的 X 坐标 (使用正确的键名) ---
    experimental_cp_list = experimental_data.get('target_cp_distribution')
    experimental_x_list = experimental_data.get('target_x_coordinates')

    cp_data = last_successful_simulation_output.get('cp_data')
    calculated_cp_list = None
    calculated_x_list = None
    if isinstance(cp_data, dict) and cp_data.get('status') == 'success':
        # 使用正确的键名 'x_coords' 和 'cp_values'
        calculated_x_list = cp_data.get('x_coords')
        calculated_cp_list = cp_data.get('cp_values')
    else:
        cp_status = cp_data.get('status') if isinstance(cp_data, dict) else "Not a dict or missing"
        cp_msg = cp_data.get('message', 'N/A') if isinstance(cp_data, dict) else 'N/A'
        print(f"[DEBUG] Accuracy: 'cp_data' extraction failed or status not success. Status: {cp_status}, Msg: {cp_msg}")


    # --- 数据有效性检查 ---
    if not isinstance(experimental_cp_list, list) or not experimental_cp_list:
        print("[DEBUG] Accuracy: Experimental Cp data ('target_cp_distribution') missing or not a list.")
        return 0.0
    # 实验 X 坐标可选，如果缺失则无法进行基于表面的精确比较
    if not isinstance(experimental_x_list, list) or not experimental_x_list or len(experimental_x_list) != len(experimental_cp_list):
        print("[ERROR] Accuracy: Experimental X coordinates ('target_x_coordinates') missing or length mismatch. Cannot perform surface-based comparison reliably.")
        # 可以回退到旧的、不太准确的整体比较方法，或者直接返回 0 分
        return 0.0 # 决定返回 0 分，因为表面分离需要X坐标
    if not isinstance(calculated_cp_list, list) or not calculated_cp_list:
        print("[DEBUG] Accuracy: Calculated Cp data ('cp_values') missing or not extracted.")
        return 0.0
    if not isinstance(calculated_x_list, list) or not calculated_x_list or len(calculated_x_list) != len(calculated_cp_list):
         print("[DEBUG] Accuracy: Calculated X coordinates ('x_coords') missing, invalid, or length mismatch.")
         return 0.0

    # 转换为 NumPy 数组
    experimental_cp = np.array(experimental_cp_list)
    experimental_x = np.array(experimental_x_list)
    calculated_cp = np.array(calculated_cp_list)
    calculated_x = np.array(calculated_x_list)

    # --- 表面分离 ---
    try:
        # 找到前缘点索引
        exp_le_index = find_leading_edge_index(experimental_x)
        calc_le_index = find_leading_edge_index(calculated_x)

        # 分离实验数据
        exp_x_upper, exp_cp_upper, exp_x_lower, exp_cp_lower = split_surfaces(
            experimental_x, experimental_cp, exp_le_index
        )
        # 分离计算数据
        calc_x_upper, calc_cp_upper, calc_x_lower, calc_cp_lower = split_surfaces(
            calculated_x, calculated_cp, calc_le_index
        )
    except Exception as e:
        print(f"[ERROR] Accuracy: Failed to split surfaces: {e}")
        return 0.0 # 分离失败则无法比较

    # --- 分别计算上下表面的 RMSE ---
    rmse_upper = compute_rmse_for_surface(exp_x_upper, exp_cp_upper, calc_x_upper, calc_cp_upper)
    rmse_lower = compute_rmse_for_surface(exp_x_lower, exp_cp_lower, calc_x_lower, calc_cp_lower)

    if rmse_upper == np.inf or rmse_lower == np.inf:
        print("[DEBUG] Accuracy: Could not compute RMSE for one or both surfaces.")
        return 0.0 # 如果任一表面计算失败，则总分给 0

    # --- 组合 RMSE (例如，简单平均) ---
    # 可以根据上下表面的点数加权，但简单平均通常足够
    combined_rmse = (rmse_upper + rmse_lower) / 2.0

    # --- 将 RMSE 转换为分数 ---
    accuracy_score = 1.0 / (1.0 + combined_rmse) # RMSE 越小，分数越高
    # print(f"[DEBUG] Accuracy: RMSE Upper={rmse_upper:.4f}, Lower={rmse_lower:.4f}, Combined={combined_rmse:.4f}, Score={accuracy_score:.4f}")

    return max(0.0, min(accuracy_score, 1.0))

# --- 主奖励函数 compute_score_cfd ---
def compute_score_cfd(solution_str: str, experimental_data: Dict[str, Any]) -> float:
    """
    计算 CFD 任务的总奖励分数。
    主要基于最终 CFL3D 计算结果与实验数据的 Cp 匹配度。
    同时考虑计算是否成功完成。

    Args:
        solution_str: Agent 生成的完整思考、调用和响应序列。
        experimental_data: 当前任务对应的实验数据 (需要包含 'target_cp_distribution' 和 'target_x_coordinates')。

    Returns:
        总奖励分数，目标范围大致在 [-1.0, 1.0]。
        -1.0: 完全失败 (如未运行模拟, 模拟崩溃)。
        ~ -0.7: 模拟成功但 Cp 提取或比较失败。
        ~ -0.5 到 +1.0: 模拟成功且 Cp 比较成功，分数取决于匹配精度 (越接近实验数据越高)。
    """
    if solution_str is None:
        return -1.0 # 无解视为最差

    assistant_blocks, tool_blocks = parse_solution_str(solution_str)
    tool_responses = [block for block in tool_blocks if '<tool_response>' in block]

    # --- 查找最后一次 CFL3D 模拟的有效输出 ---
    last_cfl3d_calc_output = None
    simulation_failed_explicitly = False
    simulation_run_successfully = False

    for response_str in reversed(tool_responses):
        response_data = parse_tool_response(response_str)
        # 假设可以通过 response_data 的结构或特定键判断是否为 CFL3D 响应
        # 这里简化：检查是否存在 'calculation_output' 键
        if isinstance(response_data, dict) and "calculation_output" in response_data:
            calc_output = response_data["calculation_output"]
            if isinstance(calc_output, dict):
                 calc_status = calc_output.get("status")
                 if calc_status == "success":
                     last_cfl3d_calc_output = calc_output # 找到最新的成功计算
                     simulation_run_successfully = True
                     break # 找到即可退出循环
                 elif calc_status == "error":
                      simulation_failed_explicitly = True
                      # 即使失败也要记录，防止后面错误地认为没运行
                      break # 找到明确的失败信号
            # 如果 calculation_output 不是字典或没有 status，则忽略这个响应

    # --- 根据查找结果计算奖励 ---
    try:
        if simulation_run_successfully and last_cfl3d_calc_output:
            # 模拟成功，尝试计算精度分数
            # 注意: compute_score_result_accuracy 现在内部处理 Cp 提取和比较
            # 它需要 tool_responses 列表来查找最后成功的模拟输出
            accuracy_score = compute_score_result_accuracy(tool_responses, experimental_data)

            if accuracy_score > 0:
                # Cp 比较成功，奖励基于精度
                # 将 accuracy_score (0到1) 映射到 [-0.5, 1.0] 范围
                total_reward = -0.5 + 1.5 * accuracy_score
                # print(f"[DEBUG] Reward: Simulation OK, Cp Compare OK. Accuracy={accuracy_score:.4f}, Reward={total_reward:.4f}")
            else:
                # 模拟成功，但 Cp 提取或比较失败 (accuracy_score <= 0)
                total_reward = -0.7
                # print(f"[DEBUG] Reward: Simulation OK, Cp Compare FAILED. Reward={total_reward:.4f}")

        elif simulation_failed_explicitly:
            # 模拟明确运行失败
            total_reward = -1.0
            # print(f"[DEBUG] Reward: Simulation FAILED explicitly. Reward={total_reward:.4f}")
        else:
            # 没有找到成功的模拟，也没有找到明确失败的模拟（可能根本没运行）
            total_reward = -1.0
            # print(f"[DEBUG] Reward: Simulation NOT run or no success/failure status found. Reward={total_reward:.4f}")

        # 确保奖励在预期范围内
        return max(-1.0, min(total_reward, 1.0))

    except Exception as e:
        print(f"[ERROR] Error in compute_score_cfd main logic: {e}")
        print(traceback.format_exc())
        print(f"[DEBUG] Solution string causing error: {solution_str[:500]}...")
        return -1.0 # 出错时返回最低分
