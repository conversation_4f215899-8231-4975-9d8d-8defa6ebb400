# Copyright 2024 Bytedance Ltd. and/or its affiliates
# ... (license header) ...

from typing import Any, Dict, Optional

# --- 导入你的具体评分函数 ---
# !! 确保路径正确 !!
try:
    # 假设 qa_em_and_format.py 与 __init__.py 在同一目录下
    from .qa_em_and_format import (
        compute_score_cfd,
        # 导入新的独立评分函数
        compute_cfd_answer_score_only,
        compute_cfd_format_score_only,
    )
    # 你可能还需要导入用于其他数据源的评分函数
    # from .other_reward_module import compute_score_other_task
    CFD_SCORE_FN_LOADED = True
except ImportError as e:
    print(f"[Reward Score Init WARNING] Could not import CFD scoring functions from qa_em_and_format.py: {e}")
    CFD_SCORE_FN_LOADED = False
    # 定义占位符
    def compute_score_cfd(*args, **kwargs): print("[ERROR] compute_score_cfd called but failed to load!"); return -1.0
    def compute_cfd_answer_score_only(*args, **kwargs): print("[ERROR] compute_cfd_answer_score_only called but failed to load!"); return 0.0
    def compute_cfd_format_score_only(*args, **kwargs): print("[ERROR] compute_cfd_format_score_only called but failed to load!"); return 0.0


# --- 修改默认调度函数 ---

def _default_compute_score_format_answer(data_source: str, solution_str: str, ground_truth: Optional[Dict[str, Any]]) -> float:
    """
    默认的奖励分数计算函数 (格式 + 答案) - 根据数据源进行调度。
    """
    if data_source == 'cfd': # 确认这里是 'cfd'
        if CFD_SCORE_FN_LOADED:
             # 确保 ground_truth 是字典 (或者根据你的实现调整)
             if not isinstance(ground_truth, dict) and ground_truth is not None:
                  print(f"[ERROR] CFD Score: ground_truth is not a dict for data_source '{data_source}'.")
                  return -1.0 # 返回错误分数
             # 调用修改后的 cfd 综合评分函数
             return compute_score_cfd(solution_str=solution_str, experimental_data=ground_truth)
        else:
             print(f"[ERROR] CFD Score function was requested for source '{data_source}' but failed to load.")
             return -1.0
    else:
        print(f"[ERROR] Reward Score: No specific reward function implemented or registered for data_source: '{data_source}' in _default_compute_score_format_answer.")
        return -1.0


def _default_compute_score_answer(data_source: str, solution_str: str, ground_truth: Optional[Dict[str, Any]]) -> float:
    """
    默认的答案准确性分数计算 - 根据数据源进行调度。
    """
    if data_source == 'cfd': # 确认这里是 'cfd'
        if CFD_SCORE_FN_LOADED:
             if not isinstance(ground_truth, dict) and ground_truth is not None:
                  print(f"[ERROR] CFD Answer Score: ground_truth is not a dict for data_source '{data_source}'.")
                  return 0.0
             # 调用 cfd 仅答案评分函数
             return compute_cfd_answer_score_only(solution_str=solution_str, experimental_data=ground_truth)
        else:
             # print(f"[WARN] CFD Answer score function failed to load for source '{data_source}'. Returning 0.0") # Log different message?
             return 0.0
    else:
        print(f"[ERROR] Reward Score: No specific answer reward function implemented for data_source: '{data_source}' in _default_compute_score_answer.")
        return 0.0


def _default_compute_score_format(data_source: str, solution_str: str) -> float:
    """
    默认的格式正确性分数计算 - 根据数据源进行调度。
    """
    if data_source == 'cfd': # 确认这里是 'cfd'
        if CFD_SCORE_FN_LOADED:
            # 调用 cfd 仅格式评分函数
            return compute_cfd_format_score_only(solution_str=solution_str)
        else:
             # print(f"[WARN] CFD Format score function failed to load for source '{data_source}'. Returning 0.0")
             return 0.0
    else:
        print(f"[ERROR] Reward Score: No specific format reward function implemented for data_source: '{data_source}' in _default_compute_score_format.")
        return 0.0

# 可能还有其他导出或变量...