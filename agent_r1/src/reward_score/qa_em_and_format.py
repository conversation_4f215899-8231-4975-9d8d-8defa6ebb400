# agent_r1/src/reward_score/cfd_reward.py
# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import re
import json
import math
import numpy as np # 确保导入 numpy
from typing import Dict, Any, Optional, Tuple, List # 确保导入 List
import traceback # 引入 traceback
import os # 引入 os 模块
import matplotlib
matplotlib.use('Agg') # Use a non-interactive backend
import matplotlib.pyplot as plt

def parse_tool_call(assistant_block: str) -> Optional[Dict[str, Any]]:
    """从 assistant block 中解析 <tool_call> 内容 (更健壮)"""
    if not isinstance(assistant_block, str): return None # 检查输入类型
    tool_call_match = re.search(r'<tool_call>(.*?)</tool_call>', assistant_block, re.DOTALL)
    if tool_call_match:
        content = tool_call_match.group(1).strip()
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            print(f"[DEBUG] Failed to parse tool_call JSON: {content}")
            return None # 返回 None 而不是崩溃
    return None

def parse_tool_response(tool_block: str) -> Optional[Dict[str, Any]]:
    """解析 <tool_response> 内容 (更健壮)"""
    if not isinstance(tool_block, str): return None # 检查输入类型
    tool_response_match = re.search(r'<tool_response>(.*?)</tool_response>', tool_block, re.DOTALL)
    if tool_response_match:
        content = tool_response_match.group(1).strip()
        try:
            # --- 特殊处理：如果内容看起来像纯文本错误消息，而不是 JSON ---
            # 这是一个启发式规则，可能需要根据实际错误格式调整
            if not content.startswith('{') and not content.startswith('['):
                 print(f"[DEBUG] Tool response content appears non-JSON: {content}")
                 # 可以选择返回一个包含错误信息的字典，或直接返回 None
                 return {"error": "Non-JSON tool response content", "content": content}
                 # 或者 return None

            return json.loads(content)
        except json.JSONDecodeError:
            print(f"[DEBUG] Failed to parse tool_response JSON: {content}")
            # 如果解析失败，也可能包含有用的错误信息
            return {"error": "JSONDecodeError in tool response", "content": content}
            # 或者 return None
    return None

def parse_solution_str(solution_str: Optional[str]) -> Tuple[List[str], List[str]]:
    """
    解析完整的 solution_str，分离 assistant 块和 tool_response 块。
    优先从 <tool> 块中提取 tool_response，如果未找到，则尝试从 <user> 块中提取。
    现在处理 <|im_end|> 前的空白字符更灵活。
    """
    assistant_blocks = []
    tool_response_blocks = []  # 初始化为空列表
    if not isinstance(solution_str, str):
        return assistant_blocks, tool_response_blocks
    try:
        # 正则表达式，允许 <|im_end|> 前有可选的空白（包括换行符）
        # 使用 re.DOTALL 使 . 匹配换行符
        assistant_pattern = r'<\|im_start\|>assistant\n(.*?)\s*<\|im_end\|>'
        tool_pattern = r'<\|im_start\|>tool\n(.*?)\s*<\|im_end\|>'
        user_pattern = r'<\|im_start\|>user\n(.*?)\s*<\|im_end\|>'

        assistant_blocks = re.findall(assistant_pattern, solution_str, re.DOTALL)
        
        # 1. 尝试从 <tool> 块中提取 tool_response
        tool_blocks_content = re.findall(tool_pattern, solution_str, re.DOTALL)
        
        if tool_blocks_content:
            # 只提取那些实际包含 <tool_response> 标签的块
            tool_response_blocks = [block for block in tool_blocks_content if '<tool_response>' in block]

        if tool_response_blocks:
            print(f"[DEBUG] parse_solution_str: Found {len(tool_response_blocks)} tool_response(s) in <tool> blocks.")
        else:
            # 如果在 <tool> 块中没有找到有效的响应（或者根本没有 <tool> 块）
            # 则尝试从 <user> 块中提取
            print("[DEBUG] parse_solution_str: No valid tool_responses found in <tool> blocks. Attempting to extract from <user> blocks.")
            
            all_user_blocks_content = re.findall(user_pattern, solution_str, re.DOTALL)
            if all_user_blocks_content:
                # 同样，只提取那些实际包含 <tool_response> 标签的块
                tool_response_blocks = [block for block in all_user_blocks_content if '<tool_response>' in block]
            
            if tool_response_blocks:
                 print(f"[DEBUG] parse_solution_str: Found {len(tool_response_blocks)} tool_response(s) in <user> blocks.")
            # else: 如果在 user 块中仍然没有找到, tool_response_blocks 保持为空列表，这是正确的
                 
    except Exception as e:
         print(f"[ERROR] Failed to parse solution_str with regex: {e}")
         # 出错时确保返回空列表
         assistant_blocks = []
         tool_response_blocks = []
         
    return assistant_blocks, tool_response_blocks

def find_leading_edge_index(x_coords):
    """找到前缘点（x 坐标最小值）的索引 (更健壮)"""
    try:
        if x_coords is None or len(x_coords) == 0:
            print("[DEBUG] find_leading_edge_index: Input x_coords is empty or None.")
            return None # 返回 None 表示无法找到
        if not isinstance(x_coords, np.ndarray):
            x_coords = np.array(x_coords)
         # 检查是否包含 NaN 或 Inf
        if np.isnan(x_coords).any() or np.isinf(x_coords).any():
             print("[WARNING] find_leading_edge_index: x_coords contains NaN or Inf.")
             # 可以选择移除 NaN/Inf 后再计算，或直接返回 None
             x_coords = x_coords[~np.isnan(x_coords) & ~np.isinf(x_coords)]
             if len(x_coords) == 0: return None

        if len(x_coords) == 0: # 再次检查，以防所有点都是 NaN/Inf
             return None

        return np.argmin(x_coords)
    except Exception as e:
         print(f"[ERROR] find_leading_edge_index failed: {e}")
         return None # 返回 None 表示失败

def split_surfaces(x_coords, y_coords_or_cp, le_index):
    """根据前缘索引将数据分为上下表面，根据Cp平均值识别上下表面"""
    try:
        if x_coords is None or y_coords_or_cp is None or le_index is None or \
           not isinstance(le_index, (int, np.integer)) or le_index < 0 or le_index >= len(x_coords):
            print("[DEBUG] split_surfaces: Invalid input (None, invalid index, or length mismatch).")
            return None, None, None, None

        n_points = len(x_coords)
        if n_points < 2:
            print("[DEBUG] split_surfaces: Need at least 2 points to split.")
            return None, None, None, None
        if len(y_coords_or_cp) != n_points:
            print("[ERROR] split_surfaces: x_coords and y_coords_or_cp length mismatch.")
            return None, None, None, None

        if not isinstance(x_coords, np.ndarray): x_coords = np.array(x_coords)
        if not isinstance(y_coords_or_cp, np.ndarray): y_coords_or_cp = np.array(y_coords_or_cp)

        valid_mask = ~np.isnan(x_coords) & ~np.isinf(x_coords) & ~np.isnan(y_coords_or_cp) & ~np.isinf(y_coords_or_cp)
        if not valid_mask.all():
            original_count = len(x_coords)
            x_coords = x_coords[valid_mask]
            y_coords_or_cp = y_coords_or_cp[valid_mask]
            print(f"[WARNING] split_surfaces: Data contains NaN/Inf. Removed {original_count - len(x_coords)} invalid points.")
            le_index = find_leading_edge_index(x_coords)
            if le_index is None:
                print("[ERROR] split_surfaces: Could not find leading edge after removing NaN/Inf.")
                return None, None, None, None
            n_points = len(x_coords)
            if n_points < 2:
                print("[DEBUG] split_surfaces: Less than 2 valid points remaining after removing NaN/Inf.")
                return None, None, None, None
        
        # 分割成两段（包含LE点）
        segment1_x = x_coords[:le_index+1]
        segment1_cp = y_coords_or_cp[:le_index+1]
        
        segment2_x = x_coords[le_index:]
        segment2_cp = y_coords_or_cp[le_index:]
        
        # 计算两段的Cp平均值
        mean_cp1 = np.mean(segment1_cp)
        mean_cp2 = np.mean(segment2_cp)
        
        # 通过Cp平均值判断上下表面 - Cp更小（更负）为上表面
        if mean_cp1 < mean_cp2:
            x_upper = segment1_x
            cp_upper = segment1_cp
            x_lower = segment2_x
            cp_lower = segment2_cp
            print(f"[INFO] Segment 1 (mean Cp={mean_cp1:.4f}) identified as upper surface")
            print(f"[INFO] Segment 2 (mean Cp={mean_cp2:.4f}) identified as lower surface")
        else:
            x_upper = segment2_x
            cp_upper = segment2_cp
            x_lower = segment1_x
            cp_lower = segment1_cp
            print(f"[INFO] Segment 2 (mean Cp={mean_cp2:.4f}) identified as upper surface")
            print(f"[INFO] Segment 1 (mean Cp={mean_cp1:.4f}) identified as lower surface")
        
        # 确保x坐标均从LE到TE方向排列（x值递增）
        if len(x_upper) > 1 and x_upper[0] > x_upper[-1]:
            x_upper = x_upper[::-1]
            cp_upper = cp_upper[::-1]
            print("[INFO] Reversed upper surface data for consistent LE->TE direction")
            
        if len(x_lower) > 1 and x_lower[0] > x_lower[-1]:
            x_lower = x_lower[::-1]
            cp_lower = cp_lower[::-1]
            print("[INFO] Reversed lower surface data for consistent LE->TE direction")

        return x_upper, cp_upper, x_lower, cp_lower

    except Exception as e:
        print(f"[ERROR] split_surfaces failed: {e}")
        traceback.print_exc()
        return None, None, None, None

def compute_rmse_for_surface(exp_x, exp_cp, calc_x, calc_cp):
    """为单个表面计算 RMSE（包含插值）(更健壮)"""
    try:
        # 检查输入是否有效
        if exp_x is None or exp_cp is None or calc_x is None or calc_cp is None or \
           len(exp_x) == 0 or len(exp_cp) == 0 or len(calc_x) == 0 or len(calc_cp) == 0 or \
           len(exp_x) != len(exp_cp) or len(calc_x) != len(calc_cp):
            print("[DEBUG] Surface RMSE: Invalid or empty input data for surface.")
            return np.inf # 返回无穷大表示无法计算

        # 转换为 NumPy 数组
        exp_x, exp_cp = np.array(exp_x), np.array(exp_cp)
        calc_x, calc_cp = np.array(calc_x), np.array(calc_cp)

        # 检查 NaN/Inf
        if np.isnan(exp_x).any() or np.isinf(exp_x).any() or np.isnan(exp_cp).any() or np.isinf(exp_cp).any() or \
           np.isnan(calc_x).any() or np.isinf(calc_x).any() or np.isnan(calc_cp).any() or np.isinf(calc_cp).any():
             print("[WARNING] Surface RMSE: Input data contains NaN or Inf. Attempting removal.")
             # 清理实验数据
             valid_exp = ~np.isnan(exp_x) & ~np.isinf(exp_x) & ~np.isnan(exp_cp) & ~np.isinf(exp_cp)
             exp_x, exp_cp = exp_x[valid_exp], exp_cp[valid_exp]
             # 清理计算数据
             valid_calc = ~np.isnan(calc_x) & ~np.isinf(calc_x) & ~np.isnan(calc_cp) & ~np.isinf(calc_cp)
             calc_x, calc_cp = calc_x[valid_calc], calc_cp[valid_calc]
             if len(exp_x) == 0 or len(calc_x) == 0:
                  print("[ERROR] Surface RMSE: No valid data remaining after removing NaN/Inf.")
                  return np.inf

        # 确保计算数据的 x 是排序好的且唯一的 (插值函数要求)
        unique_indices = np.argsort(calc_x)
        unique_calc_x = calc_x[unique_indices]
        unique_calc_cp = calc_cp[unique_indices]
        # 移除重复的 x 值（保留第一个出现的）
        unique_mask, first_indices = np.unique(unique_calc_x, return_index=True)
        if len(unique_mask) != len(unique_calc_x):
             print(f"[DEBUG] Surface RMSE: Removed {len(unique_calc_x) - len(unique_mask)} duplicate X values in calculated data.")
             unique_calc_x = unique_calc_x[first_indices]
             unique_calc_cp = unique_calc_cp[first_indices]


        # 检查单调性（np.interp 需要 x 递增）
        if not np.all(np.diff(unique_calc_x) > 0):
             # 如果只有一个点，diff 为空，也视为非单调递增（无法插值）
             if len(unique_calc_x) <= 1:
                  print("[ERROR] Surface RMSE: Calculated X for surface has <= 1 unique valid point. Cannot interpolate.")
             else:
                  print("[ERROR] Surface RMSE: Calculated X for surface not strictly monotonic after unique/sort.")
             return np.inf # 无法插值

        # 执行插值
        interpolated_calc_cp = np.interp(exp_x, unique_calc_x, unique_calc_cp)

        # 检查插值结果
        if np.isnan(interpolated_calc_cp).any() or np.isinf(interpolated_calc_cp).any():
             print("[ERROR] Surface RMSE: Interpolation resulted in NaN or Inf.")
             return np.inf

        # 计算 RMSE
        squared_errors = (exp_cp - interpolated_calc_cp)**2
        mean_squared_error = np.mean(squared_errors)
        rmse = np.sqrt(mean_squared_error)

        if np.isnan(rmse) or np.isinf(rmse):
            print("[ERROR] Surface RMSE: Final RMSE value is NaN or Inf.")
            return np.inf

        return rmse

    except Exception as e:
        print(f"[ERROR] Surface RMSE: Unexpected error during interpolation/RMSE: {e}")
        traceback.print_exc() # 打印详细错误
        return np.inf # 返回无穷大

def compute_accuracy_score_from_rmse(tool_responses: list, experimental_data: Optional[Dict[str, Any]]) -> float:
    """
    计算基于 RMSE 的准确度分数 [0, 1]。
    返回 np.nan 如果无法计算。
    tool_responses 是包含 <tool_response> 标签的块的原始内容列表。
    """
    accuracy_score = np.nan # 使用 NaN 表示无法计算或出错
    plot_info = {"run_id": None, "permanent_results_dir": None, "source_type": None} # 初始化绘图信息

    try:
        if not isinstance(tool_responses, list) or not tool_responses:
            print("[DEBUG] compute_accuracy_score_from_rmse: No tool_responses provided.")
            return accuracy_score
        if not isinstance(experimental_data, dict) or not experimental_data:
            print("[DEBUG] compute_accuracy_score_from_rmse: No experimental_data provided.")
            return accuracy_score

        exp_x = experimental_data.get('target_x_coordinates')
        exp_cp = experimental_data.get('target_cp_distribution')

        # Check if data is present and is either a list or numpy array
        is_exp_x_valid_type = isinstance(exp_x, (list, np.ndarray))
        is_exp_cp_valid_type = isinstance(exp_cp, (list, np.ndarray))

        # Check for general validity: type, length match, and not empty
        # Note: len() works for both Python lists and 1D NumPy arrays.
        # For NumPy arrays, .size could also be used for emptiness check.
        if not is_exp_x_valid_type or \
           not is_exp_cp_valid_type or \
           (is_exp_x_valid_type and hasattr(exp_x, '__len__') and len(exp_x) == 0) or \
           (is_exp_x_valid_type and is_exp_cp_valid_type and hasattr(exp_x, '__len__') and hasattr(exp_cp, '__len__') and len(exp_x) != len(exp_cp)):
            
            error_detail = []
            if not is_exp_x_valid_type:
                error_detail.append(f"target_x_coordinates is type {type(exp_x)}, expected list or np.ndarray.")
            if not is_exp_cp_valid_type:
                error_detail.append(f"target_cp_distribution is type {type(exp_cp)}, expected list or np.ndarray.")
            
            if is_exp_x_valid_type and hasattr(exp_x, '__len__'): # Only proceed if exp_x is somewhat valid for length checks
                if len(exp_x) == 0:
                    error_detail.append("Experimental data (target_x_coordinates) is empty.")
                elif is_exp_cp_valid_type and hasattr(exp_cp, '__len__') and len(exp_x) != len(exp_cp):
                    error_detail.append(f"Length mismatch: target_x_coordinates ({len(exp_x)}) vs target_cp_distribution ({len(exp_cp)}).")
            elif exp_x is None: # If exp_x itself is None
                 error_detail.append("target_x_coordinates is None.")

            print(f"[ERROR] Accuracy: Invalid experimental data format. Details: {'; '.join(error_detail)}")
            return accuracy_score

        final_calculated_data = None
        # tool_responses 列表现在包含的是 tool block 或 user block 的内容字符串,
        # 每个字符串都应该包含一个 <tool_response> 标签
        for response_block_content in reversed(tool_responses): 
            response_data = parse_tool_response(response_block_content)
            if isinstance(response_data, dict) and "error" not in response_data:
                calc_output = response_data.get("calculation_output")
                # 从 response_data 获取 permanent_results_directory 用于保存绘图
                current_permanent_results_dir_from_response = response_data.get("permanent_results_directory")

                if isinstance(calc_output, dict) and calc_output.get("status") == "success":
                    # 检查是否有CP数据文件路径
                    cp_data_file_key = 'results_file_path' # 使用正确的键名
                    cp_data_file = calc_output.get(cp_data_file_key)
                    
                    temp_run_id = None
                    temp_perm_dir = None

                    if cp_data_file and os.path.exists(cp_data_file) and cp_data_file.endswith('.json'): # 确保是json文件
                        try:
                            # 从 JSON 文件读取CP数据
                            with open(cp_data_file, 'r') as f:
                                results_json_data = json.load(f)
                            
                            # 从JSON结构中提取CP数据
                            calc_x_list = results_json_data.get('cp_data', {}).get('x_coords')
                            calc_cp_list = results_json_data.get('cp_data', {}).get('cp_values')
                            
                            if isinstance(calc_x_list, list) and isinstance(calc_cp_list, list) and \
                               len(calc_x_list) == len(calc_cp_list) and len(calc_x_list) > 0:
                                final_calculated_data = {'x': np.array(calc_x_list), 'cp': np.array(calc_cp_list)}
                                
                                temp_run_id = results_json_data.get('run_id')
                                temp_perm_dir = os.path.dirname(cp_data_file)
                                if not temp_run_id and temp_perm_dir: # Fallback for run_id
                                    temp_run_id = os.path.basename(temp_perm_dir)
                                if not temp_perm_dir and current_permanent_results_dir_from_response:
                                    temp_perm_dir = current_permanent_results_dir_from_response
                                
                                plot_info = {
                                    "run_id": temp_run_id,
                                    "permanent_results_dir": temp_perm_dir,
                                    "source_type": "json"
                                }
                                print(f"[DEBUG] compute_accuracy_score_from_rmse: Successfully loaded CP data from JSON file {cp_data_file}. Plot info: {plot_info}")
                                break # 假设我们只需要最新的成功计算结果
                            else:
                                print(f"[ERROR] compute_accuracy_score_from_rmse: Invalid or missing 'cp_data' (x_coords/cp_values) in JSON file {cp_data_file}.")
                        
                        except json.JSONDecodeError as json_err:
                            print(f"[ERROR] Failed to decode JSON from file {cp_data_file}: {json_err}")
                        except Exception as e:
                            print(f"[ERROR] Failed to read or process CP data from JSON file {cp_data_file}: {e}")
                    elif cp_data_file and not cp_data_file.endswith('.json'):
                        print(f"[WARN] compute_accuracy_score_from_rmse: Expected a .json file for CP data, but got {cp_data_file}. Trying to parse from dict...")
                    
                    # 如果文件读取失败或文件不是JSON，尝试从响应中直接获取 (保持原有逻辑作为后备)
                    if not final_calculated_data: # 只有在从文件加载失败时才尝试这个
                        cp_data_dict = calc_output.get('cp_data')
                        if isinstance(cp_data_dict, dict):
                            # 之前的 cp_data 结构可能是 {"points_count": ...}
                            # 或者更早的可能是 {"x_coords": [...], "cp_values": [...]}
                            # 我们需要的是后者
                            calc_x = cp_data_dict.get('x_coords') 
                            calc_cp = cp_data_dict.get('cp_values') 
                            
                            if isinstance(calc_x, list) and isinstance(calc_cp, list) and \
                               len(calc_x) == len(calc_cp) and len(calc_x) > 0:
                                final_calculated_data = {'x': np.array(calc_x), 'cp': np.array(calc_cp)}
                                # For data from dict, run_id and perm_dir must come from the broader response_data
                                temp_perm_dir = current_permanent_results_dir_from_response
                                if temp_perm_dir: # Deriving run_id from perm_dir is the most reliable here
                                     temp_run_id = os.path.basename(temp_perm_dir)
                                
                                plot_info = {
                                    "run_id": temp_run_id,
                                    "permanent_results_dir": temp_perm_dir,
                                    "source_type": "dict"
                                }
                                print(f"[DEBUG] compute_accuracy_score_from_rmse: Successfully extracted final_calculated_data directly from tool response dictionary. Plot info: {plot_info}")
                                break
                            else:
                                print(f"[DEBUG] compute_accuracy_score_from_rmse: cp_data dict present in tool response, but x_coords/cp_values are invalid, mismatched, or missing.")
                        else:
                            print(f"[DEBUG] compute_accuracy_score_from_rmse: calc_output success, but cp_data is not a dict or is missing from tool response dict.")
        
        if final_calculated_data:
            exp_x = np.array(exp_x)
            exp_cp = np.array(exp_cp)
            exp_le_index = find_leading_edge_index(exp_x)
            calc_le_index = find_leading_edge_index(final_calculated_data['x'])

            if exp_le_index is not None and calc_le_index is not None:
                exp_x_up, exp_cp_up, exp_x_low, exp_cp_low = split_surfaces(exp_x, exp_cp, exp_le_index)
                calc_x_up, calc_cp_up, calc_x_low, calc_cp_low = split_surfaces(final_calculated_data['x'], final_calculated_data['cp'], calc_le_index)

                if exp_x_up is not None and calc_x_up is not None and \
                   exp_x_low is not None and calc_x_low is not None: # Ensure all parts for RMSE are present
                    rmse_upper = compute_rmse_for_surface(exp_x_up, exp_cp_up, calc_x_up, calc_cp_up)
                    rmse_lower = compute_rmse_for_surface(exp_x_low, exp_cp_low, calc_x_low, calc_cp_low)

                    if not np.isinf(rmse_upper) and not np.isinf(rmse_lower):
                        combined_rmse = (rmse_upper + rmse_lower) / 2.0
                        
                        # 修改 accuracy_score 计算机制
                        # K_FACTOR_RMSE 用于调整RMSE对分数的影响程度
                        # 较大的 K_FACTOR_RMSE 会使得分数对 RMSE 的变化更敏感
                        K_FACTOR_RMSE = 3.0 
                        accuracy_score = max(0.1, 1.0 - K_FACTOR_RMSE * combined_rmse)
                        
                            
                        print(f"[DEBUG] compute_accuracy_score_from_rmse: RMSE_upper={rmse_upper:.4f}, RMSE_lower={rmse_lower:.4f}, Combined_RMSE={combined_rmse:.4f}, Accuracy_Score={accuracy_score:.4f}")

                        # <<< PLOTTING LOGIC START >>>
                        if plot_info.get("run_id") and plot_info.get("permanent_results_dir") and os.path.isdir(plot_info["permanent_results_dir"]):
                            try:
                                plt.figure(figsize=(12, 8))
                                # Plot experimental data
                                if exp_x_up is not None and exp_cp_up is not None:
                                    plt.plot(exp_x_up, exp_cp_up, 'bo-', label='Exp. Upper', markersize=3, linewidth=1)
                                if exp_x_low is not None and exp_cp_low is not None:
                                    plt.plot(exp_x_low, exp_cp_low, 'ro-', label='Exp. Lower', markersize=3, linewidth=1)
                                # Plot calculated data
                                if calc_x_up is not None and calc_cp_up is not None:
                                    plt.plot(calc_x_up, calc_cp_up, 'b*--', label='Calc. Upper', markersize=4, linewidth=1)
                                if calc_x_low is not None and calc_cp_low is not None:
                                    plt.plot(calc_x_low, calc_cp_low, 'r*--', label='Calc. Lower', markersize=4, linewidth=1)

                                plt.xlabel('x/c')
                                plt.ylabel('Cp')
                                plt.title(f'Cp Distribution Comparison - Run ID: {plot_info["run_id"]}\\nCombined RMSE: {combined_rmse:.4f}')
                                plt.gca().invert_yaxis() # Invert Cp axis
                                plt.legend()
                                plt.grid(True)
                                
                                plot_filename = f'cp_comparison_{plot_info["run_id"]}.png'
                                plot_save_path = os.path.join(plot_info["permanent_results_dir"], plot_filename)
                                plt.savefig(plot_save_path)
                                plt.close() # Close the figure to free memory
                                print(f"[INFO] Cp comparison plot saved to: {plot_save_path}")
                            except Exception as plot_e:
                                print(f"[ERROR] Failed to generate or save Cp comparison plot: {plot_e}")
                                traceback.print_exc()
                        else:
                            missing_plot_info = []
                            if not plot_info.get("run_id"): missing_plot_info.append("run_id")
                            if not plot_info.get("permanent_results_dir"): missing_plot_info.append("permanent_results_dir")
                            elif not os.path.isdir(plot_info.get("permanent_results_dir", "")): missing_plot_info.append(f"permanent_results_dir_not_a_directory ({plot_info.get('permanent_results_dir')})")
                            print(f"[WARN] Skipping Cp plot generation: Missing or invalid plot_info ({', '.join(missing_plot_info)}). Plot info was: {plot_info}")
                        # <<< PLOTTING LOGIC END >>>
                    else:
                        print(f"[DEBUG] compute_accuracy_score_from_rmse: RMSE calculation resulted in Inf. RMSE_upper={rmse_upper}, RMSE_lower={rmse_lower}")
                else:
                    print(f"[DEBUG] compute_accuracy_score_from_rmse: Failed to split surfaces for exp or calc data after finding LE.")
            else:
                print(f"[DEBUG] compute_accuracy_score_from_rmse: Failed to find leading edge index for exp_data (idx={exp_le_index}) or calc_data (idx={calc_le_index}).")
        else:
            print(f"[DEBUG] compute_accuracy_score_from_rmse: No valid final_calculated_data found after processing tool responses.")


    except Exception as e:
        print(f"[FATAL ERROR] Unexpected error in compute_accuracy_score_from_rmse: {e}")
        traceback.print_exc()
        accuracy_score = np.nan # 出错时返回 NaN

    return accuracy_score

def compute_score_cfd(solution_str: str, experimental_data: Optional[Dict[str, Any]]) -> float:
    """
    计算 CFD 任务的综合奖励分数（格式 + 答案）。
    接收 solution_str 和 experimental_data (ground_truth)，返回单个 float 分数。
    """
    final_score = -1.0 # PPO 奖励，默认最差
    answer_score_val = 0.0 # 用于计算
    format_score_val = 0.0 # 用于计算
    cl_cd_available = False # 新增标志，表示是否有CL/CD数据

    try:
        if not isinstance(experimental_data, dict):
             print("[WARN] compute_score_cfd: Invalid or missing experimental_data dict.")

        assistant_blocks, tool_response_blocks = parse_solution_str(solution_str)

        # --- 3. 格式评分 ---
        # tool_response_blocks 已经是由 parse_solution_str 处理过的，包含了响应内容的列表
        if assistant_blocks and tool_response_blocks: 
            has_successful_response = False
            for resp_block_content in tool_response_blocks: 
                 resp_data = parse_tool_response(resp_block_content) 
                 if isinstance(resp_data, dict) and "error" not in resp_data:
                      calc_output = resp_data.get("calculation_output")
                      if isinstance(calc_output, dict) and calc_output.get("status") == "success":
                           has_successful_response = True
                           break
            if has_successful_response:
                 format_score_val = 1.0
            else:
                 format_score_val = 0.7 
        elif assistant_blocks: 
            format_score_val = 0.3
        
        # --- 4. 检查是否有有效的CL/CD数据 ---
        for resp_block_content in tool_response_blocks:
            resp_data = parse_tool_response(resp_block_content)
            if isinstance(resp_data, dict) and "error" not in resp_data:
                calc_output = resp_data.get("calculation_output")
                if isinstance(calc_output, dict) and calc_output.get("status") == "success":
                    force_coeffs = calc_output.get("force_coefficients", {})
                    if isinstance(force_coeffs, dict) and force_coeffs.get("cl") is not None and force_coeffs.get("cd") is not None:
                        cl_cd_available = True
                        break
        
        # --- 5. 计算准确性分数 (Answer Score) ---
        accuracy_score_result = compute_accuracy_score_from_rmse(tool_response_blocks, experimental_data)

        if not np.isnan(accuracy_score_result):
            answer_score_val = accuracy_score_result
        elif cl_cd_available:
            # 如果没有有效的Cp比较分数，但有CL/CD数据，给予基础分数
            answer_score_val = 0.1  # 基础分数
        
        # --- 6. 组合成分数 ---
        if format_score_val == 0.0:
            final_score = -1.0 
        elif answer_score_val == 0.0 and format_score_val < 1.0 and not cl_cd_available: 
            final_score = -0.8
        else:
            final_score = answer_score_val * 0.8 + format_score_val * 0.2
        
        # 修改 AccuracyResult 的打印方式，使其在为 NaN 时不使用浮点格式化
        accuracy_result_str = f"{accuracy_score_result:.4f}" if not np.isnan(accuracy_score_result) else "NaN"
        print(f"[DEBUG] compute_score_cfd: AccuracyResult={accuracy_result_str}, AnswerScoreVal={answer_score_val:.4f}, Format={format_score_val:.4f}, CL/CD_Available={cl_cd_available} -> Final Score={final_score:.4f}")

    except Exception as e:
        print(f"[FATAL ERROR] compute_score_cfd failed: {e}")
        traceback.print_exc()
        final_score = -1.0 

    # 确保在返回前打印最终计算的奖励
    print(f"[INFO] compute_score_cfd: Final calculated reward for current evaluation: {final_score:.4f}")
    return max(-1.0, min(final_score, 1.0))

# 示例：为 _default_compute_score_answer 提供单独的分数
def compute_cfd_answer_score_only(solution_str: str, experimental_data: Optional[Dict[str, Any]]) -> float:
     _, tool_response_blocks = parse_solution_str(solution_str) 
     score = compute_accuracy_score_from_rmse(tool_response_blocks, experimental_data)
     return 0.0 if np.isnan(score) else max(0.0, min(score, 1.0))

# 示例：为 _default_compute_score_format 提供单独的分数
def compute_cfd_format_score_only(solution_str: str) -> float:
     assistant_blocks, tool_response_blocks = parse_solution_str(solution_str) 
     format_score_val = 0.0
     if assistant_blocks and tool_response_blocks:
          has_successful_response = False
          for resp_block_content in tool_response_blocks: 
               resp_data = parse_tool_response(resp_block_content) 
               if isinstance(resp_data, dict) and "error" not in resp_data:
                    calc_output = resp_data.get("calculation_output")
                    if isinstance(calc_output, dict) and calc_output.get("status") == "success":
                         has_successful_response = True
                         break
          if has_successful_response: format_score_val = 1.0
          else: format_score_val = 0.7
     elif assistant_blocks: format_score_val = 0.3
     return max(0.0, min(format_score_val, 1.0))
