#!/usr/bin/env python3
"""
GPU监控数据分析工具
用于分析训练过程中的GPU使用情况，并提供参数优化建议
"""

import json
import os
import sys
import argparse
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime
import numpy as np

def parse_gpu_log(log_file):
    """解析GPU内存日志文件"""
    data = []
    current_timestamp = None
    
    with open(log_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('[') and '] GPU状态:' in line:
                # 提取时间戳
                timestamp_str = line.split('] GPU状态:')[0][1:]
                current_timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            elif line and current_timestamp and ',' in line:
                # 解析GPU数据行
                parts = line.split(', ')
                if len(parts) >= 8:
                    try:
                        gpu_data = {
                            'timestamp': current_timestamp,
                            'gpu_id': int(parts[0]),
                            'name': parts[1],
                            'memory_used': int(parts[2]),
                            'memory_total': int(parts[3]),
                            'memory_free': int(parts[4]),
                            'gpu_util': int(parts[5]),
                            'memory_util': int(parts[6]),
                            'temperature': int(parts[7])
                        }
                        gpu_data['memory_percent'] = (gpu_data['memory_used'] / gpu_data['memory_total']) * 100
                        data.append(gpu_data)
                    except ValueError:
                        continue
    
    return pd.DataFrame(data)

def parse_training_phases(log_file):
    """解析训练阶段日志"""
    phases = []
    
    if not os.path.exists(log_file):
        return pd.DataFrame()
    
    with open(log_file, 'r') as f:
        for line in f:
            try:
                phase_data = json.loads(line.strip())
                phases.append(phase_data)
            except json.JSONDecodeError:
                continue
    
    return phases

def analyze_memory_usage(df):
    """分析内存使用情况"""
    print("=== GPU 内存使用分析 ===")
    
    if df.empty:
        print("没有找到GPU数据")
        return
    
    # 按GPU分组分析
    for gpu_id in sorted(df['gpu_id'].unique()):
        gpu_data = df[df['gpu_id'] == gpu_id]
        
        print(f"\nGPU {gpu_id} 分析:")
        print(f"  内存总量: {gpu_data['memory_total'].iloc[0]} MB")
        print(f"  平均内存使用: {gpu_data['memory_used'].mean():.1f} MB ({gpu_data['memory_percent'].mean():.1f}%)")
        print(f"  最大内存使用: {gpu_data['memory_used'].max()} MB ({gpu_data['memory_percent'].max():.1f}%)")
        print(f"  最小内存使用: {gpu_data['memory_used'].min()} MB ({gpu_data['memory_percent'].min():.1f}%)")
        print(f"  平均GPU利用率: {gpu_data['gpu_util'].mean():.1f}%")
        print(f"  平均温度: {gpu_data['temperature'].mean():.1f}°C")
        
        # 内存使用建议
        max_usage_percent = gpu_data['memory_percent'].max()
        if max_usage_percent > 95:
            print(f"  ⚠️  警告: 内存使用率过高 ({max_usage_percent:.1f}%)，建议减少批次大小")
        elif max_usage_percent > 85:
            print(f"  ⚠️  注意: 内存使用率较高 ({max_usage_percent:.1f}%)，接近上限")
        elif max_usage_percent < 60:
            print(f"  ✅ 建议: 内存使用率较低 ({max_usage_percent:.1f}%)，可以增加批次大小")
        else:
            print(f"  ✅ 内存使用率合理 ({max_usage_percent:.1f}%)")

def generate_optimization_suggestions(df):
    """生成参数优化建议"""
    print("\n=== 参数优化建议 ===")
    
    if df.empty:
        print("没有足够的数据生成建议")
        return
    
    # 计算平均内存使用率
    avg_memory_percent = df['memory_percent'].mean()
    max_memory_percent = df['memory_percent'].max()
    avg_gpu_util = df['gpu_util'].mean()
    
    print(f"当前配置分析:")
    print(f"  平均内存使用率: {avg_memory_percent:.1f}%")
    print(f"  最大内存使用率: {max_memory_percent:.1f}%")
    print(f"  平均GPU利用率: {avg_gpu_util:.1f}%")
    
    suggestions = []
    
    # 内存使用建议
    if max_memory_percent > 95:
        suggestions.append("🔴 紧急: 减少批次大小 (train_batch_size, ppo_mini_batch_size)")
        suggestions.append("🔴 紧急: 减少序列长度 (max_prompt_length, max_response_length)")
        suggestions.append("🔴 紧急: 降低GPU内存利用率 (gpu_memory_utilization)")
    elif max_memory_percent > 85:
        suggestions.append("🟡 建议: 适度减少批次大小")
        suggestions.append("🟡 建议: 考虑启用更多内存优化 (param_offload, optimizer_offload)")
    elif max_memory_percent < 60:
        suggestions.append("🟢 优化: 可以增加批次大小以提高训练效率")
        suggestions.append("🟢 优化: 可以增加序列长度以处理更复杂任务")
        suggestions.append("🟢 优化: 可以提高GPU内存利用率")
    
    # GPU利用率建议
    if avg_gpu_util < 50:
        suggestions.append("🟡 性能: GPU利用率较低，考虑增加批次大小或减少TP_SIZE")
    elif avg_gpu_util > 95:
        suggestions.append("🟢 性能: GPU利用率很高，配置良好")
    
    # 具体参数建议
    print(f"\n具体参数调整建议:")
    
    if max_memory_percent > 90:
        print("  data.train_batch_size: 建议减少到当前值的70-80%")
        print("  actor_rollout_ref.actor.ppo_mini_batch_size: 建议减少到当前值的70-80%")
        print("  actor_rollout_ref.rollout.gpu_memory_utilization: 建议设置为0.4-0.5")
    elif max_memory_percent < 65:
        print("  data.train_batch_size: 可以增加到当前值的120-150%")
        print("  actor_rollout_ref.actor.ppo_mini_batch_size: 可以增加到当前值的120-150%")
        print("  actor_rollout_ref.rollout.gpu_memory_utilization: 可以设置为0.7-0.8")
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def plot_memory_usage(df, output_dir):
    """绘制内存使用图表"""
    if df.empty:
        return
    
    plt.figure(figsize=(15, 10))
    
    # 子图1: 内存使用率时间序列
    plt.subplot(2, 2, 1)
    for gpu_id in sorted(df['gpu_id'].unique()):
        gpu_data = df[df['gpu_id'] == gpu_id]
        plt.plot(gpu_data['timestamp'], gpu_data['memory_percent'], 
                label=f'GPU {gpu_id}', marker='o', markersize=2)
    plt.title('GPU 内存使用率随时间变化')
    plt.xlabel('时间')
    plt.ylabel('内存使用率 (%)')
    plt.legend()
    plt.grid(True)
    plt.xticks(rotation=45)
    
    # 子图2: GPU利用率时间序列
    plt.subplot(2, 2, 2)
    for gpu_id in sorted(df['gpu_id'].unique()):
        gpu_data = df[df['gpu_id'] == gpu_id]
        plt.plot(gpu_data['timestamp'], gpu_data['gpu_util'], 
                label=f'GPU {gpu_id}', marker='o', markersize=2)
    plt.title('GPU 利用率随时间变化')
    plt.xlabel('时间')
    plt.ylabel('GPU 利用率 (%)')
    plt.legend()
    plt.grid(True)
    plt.xticks(rotation=45)
    
    # 子图3: 内存使用分布
    plt.subplot(2, 2, 3)
    memory_percentages = df['memory_percent'].values
    plt.hist(memory_percentages, bins=20, alpha=0.7, edgecolor='black')
    plt.axvline(memory_percentages.mean(), color='red', linestyle='--', 
                label=f'平均值: {memory_percentages.mean():.1f}%')
    plt.title('内存使用率分布')
    plt.xlabel('内存使用率 (%)')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(True)
    
    # 子图4: 温度监控
    plt.subplot(2, 2, 4)
    for gpu_id in sorted(df['gpu_id'].unique()):
        gpu_data = df[df['gpu_id'] == gpu_id]
        plt.plot(gpu_data['timestamp'], gpu_data['temperature'], 
                label=f'GPU {gpu_id}', marker='o', markersize=2)
    plt.title('GPU 温度随时间变化')
    plt.xlabel('时间')
    plt.ylabel('温度 (°C)')
    plt.legend()
    plt.grid(True)
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = os.path.join(output_dir, 'gpu_usage_analysis.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"图表已保存到: {plot_file}")
    
    # 显示图表（如果在交互环境中）
    try:
        plt.show()
    except:
        pass

def main():
    parser = argparse.ArgumentParser(description='分析GPU监控日志')
    parser.add_argument('log_dir', help='GPU监控日志目录')
    parser.add_argument('--plot', action='store_true', help='生成图表')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.log_dir):
        print(f"错误: 日志目录不存在: {args.log_dir}")
        sys.exit(1)
    
    # 解析GPU内存日志
    gpu_log_file = os.path.join(args.log_dir, 'gpu_memory.log')
    if os.path.exists(gpu_log_file):
        print(f"正在分析GPU日志: {gpu_log_file}")
        df = parse_gpu_log(gpu_log_file)
        
        if not df.empty:
            analyze_memory_usage(df)
            generate_optimization_suggestions(df)
            
            if args.plot:
                plot_memory_usage(df, args.log_dir)
        else:
            print("没有找到有效的GPU数据")
    else:
        print(f"GPU日志文件不存在: {gpu_log_file}")
    
    # 解析训练阶段日志
    phase_log_file = os.path.join(args.log_dir, 'training_phases.log')
    if os.path.exists(phase_log_file):
        phases = parse_training_phases(phase_log_file)
        if phases:
            print(f"\n=== 训练阶段分析 ===")
            print(f"记录的训练阶段数: {len(phases)}")
            for phase in phases[-5:]:  # 显示最近5个阶段
                print(f"  {phase['timestamp']}: {phase['phase']}")

if __name__ == '__main__':
    main()
