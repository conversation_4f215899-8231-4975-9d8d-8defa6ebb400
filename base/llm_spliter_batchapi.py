import json
import os
from zhipuai import ZhipuAI
from typing import List, Dict
from langchain_text_splitters import MarkdownHeaderTextSplitter


api_key = '63dadaa668cbe3eabbca4842bf801297.Lhqjs2iZk0sghldk'
client = ZhipuAI(api_key=api_key)
import re
from typing import List
from pymilvus import MilvusClient
import numpy as np
from pymilvus import Collection, DataType, FieldSchema, CollectionSchema, connections
import time
import os
from tqdm import tqdm
import json
import openai
from os import path
from model import EmbeddingManager
from pymilvus.model.hybrid import BGEM3EmbeddingFunction

def write_chunks_to_jsonl(chunks: List[Dict[str, str]], filename='chunks_batch.jsonl'):
    """
    Writes chunks to a JSONL file in a format compatible with the batch API.
    
    Args:
        chunks (List[Dict[str, str]]): A list of chunks with 'title' and 'content'.
        filename (str): The filename to write the JSONL data to.
    """
    with open(filename, 'w', encoding='utf-8') as f:
        for i, chunk in enumerate(chunks):
            json_data = {
                "custom_id": f"request-{i+1}",  # Unique ID for tracking each request
                "method": "POST",
                "url": "/v4/chat/completions",
                "body": {
                    "model": "glm-4-plus",
                    "messages": [
                        {"role": "system", "content": "你是一个文本语义切分助手，请根据语义将markdown文本进一步分割为文本块。请遵循以下要求："
                        "1. 划分时请保持原文，不要加入额外提示词；2. 划分时请尽量保证语义的完整性，如例子、习题、表格等请完整保留，同时注意将数学表达式与相关文本合并,不要单独划分。"},
                        {"role": "user", "content": f"请按照语义划分以下文本，并以\n\n作为划分后文本块之间的分割符:\n{chunk.page_content}"}
                    ],
                    "max_tokens": 4000,
                    "temperature": 0.9
                }
            }
            f.write(json.dumps(json_data, ensure_ascii=False) + "\n")  # Write each request as a new line in the JSONL file

def upload_jsonl_file(filename: str):
    """
    Uploads the JSONL file to the Batch API and returns the file ID.
    
    Args:
        filename (str): The filename to upload.
        
    Returns:
        str: The file ID returned by the server.
    """
    result = client.files.create(file=open(filename, "rb"), purpose="batch")
    return result.id

def create_batch_task(file_id: str):
    """
    Creates a batch task to process the uploaded file.
    
    Args:
        file_id (str): The file ID of the uploaded JSONL file.
        
    Returns:
        dict: The response from the Batch API with task details.
    """
    create = client.batches.create(
        input_file_id=file_id,
        endpoint="/v4/chat/completions",  # Endpoint for GLM-4 completions
        auto_delete_input_file=True,  # Automatically delete the file after processing
        metadata={
            "description": "Text segmentation and refinement"
        }
    )
    return create

def get_batch_results(batch_id: str):
    """
    Retrieves the results of the batch task.
    
    Args:
        batch_id (str): The ID of the batch task.
        
    Returns:
        dict: The results of the batch task.
    """
    batch_job = client.batches.retrieve(batch_id)
    
    if batch_job.status == "completed":
        # Get the output file ID from the completed job
        output_file_id = batch_job.output_file_id
        
        # Download the results
        content = client.files.content(output_file_id)
        content.write_to_file("batch_output.jsonl")
        print("Batch results downloaded successfully.")
    else:
        print(f"Batch status: {batch_job.status}")

def process_batch_output(filename='batch_output.jsonl'):
    refined_chunks = []
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            result = json.loads(line)
            custom_id = result['custom_id']
            refined_content = result['response']['body']['choices'][0]['message']['content']
            refined_chunks.append({"title": custom_id, "content": refined_content})
    return refined_chunks

def split_text_into_segments(text: str) -> List[str]:
    """
    Splits the given text into segments based on '\n\n' as the paragraph delimiter.
    
    Args:
        text (str): The full text to be split.
        
    Returns:
        List[str]: A list of paragraphs.
    """
    # Split the text by double newline '\n\n' which indicates a paragraph break
    segments = text.split('\n\n')
    return segments

clientZ = MilvusClient("Air.db")

bge_m3_ef = BGEM3EmbeddingFunction(
    #model_name='BAAI/bge-large-zh-v1.5', # Specify the model name
    model_name='/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181',
    #model_name='BAAI/bge-m3',
    device='cuda', # Specify the device to use, e.g., 'cpu' or 'cuda:0'
    use_fp16=False # Specify whether to use fp16. Set to `False` if `device` is `cpu`.
)

'''schema = MilvusClient.create_schema(
    auto_id=True,
    enable_dynamic_field=True,
)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=1024)

index_params = client.prepare_index_params()

# 4.2. Add an index on the vector field.
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="HNSW",
    params={ "nlist": 128 }
)

# 3. Create collection
client.create_collection(
    collection_name="book", 
    schema=schema, 
    index_params=index_params
)

time.sleep(5)
print("collection created")'''

'''res = client.get_load_state(
collection_name="book"
)'''

'''headers_to_split_on = [
    ("#", "Header 1"),
    ("##", "Header 2"),
    ("###", "Header 3"),
    ("####", "Header 4"),
]
path = "/mnt/e/Python/dataset/outputmd/"
file = "Fluid Mechanics/"
markdown_file = path + file + "output.md"
#markdown_file = "testpdf/test_md2/output.md"
with open(markdown_file, 'r', encoding='utf-8') as f:
    markdown_content = f.read()
print("Data read.")

markdown_splitter = MarkdownHeaderTextSplitter(
    headers_to_split_on=headers_to_split_on, strip_headers=False
)
md_header_splits = markdown_splitter.split_text(markdown_content)

# Char-level splits
from langchain_text_splitters import RecursiveCharacterTextSplitter

chunk_size = 2500
chunk_overlap = 100
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=chunk_size, chunk_overlap=chunk_overlap
)

# Split
chunks = text_splitter.split_documents(md_header_splits)#print(f"Chunks split.total:{len(chunks)}")
for chunk in chunks:
    #print(chunk.page_content, "\n\n")
    print(len(chunk.page_content))
print(f"Splitted,total:{len(chunks)}")

write_chunks_to_jsonl(chunks)

file_id = upload_jsonl_file('chunks_batch.jsonl')
print(f"Uploaded file ID: {file_id}")

time.sleep(10)

batch_task = create_batch_task(file_id)
print(f"Batch task created: {batch_task}")'''

'''batch_id = batch_task.id
batch_job = client.batches.retrieve(batch_id)
print(batch_job)'''
batch_id = 'batch_1850519151543595008'
get_batch_results(batch_id)


refined_chunks = process_batch_output()
print("Refined chunks processed.")
for refined_chunk in refined_chunks:
    print(refined_chunk["content"], len(refined_chunk["content"]))

for chunk in tqdm(refined_chunks, desc="Processing Chunks"):
    # Split the content of the chunk by \n\n to get segments
    segments = chunk['content'].split('\n\n')
    
    # Generate embeddings for the segments
    doc_embeddings = bge_m3_ef.encode_documents(segments)
    
    # Prepare the data for insertion into the knowledge base
    data = [
        {
            "vector": doc_embeddings["dense"][i],  # Embedding vector for each segment
            "text": segments[i],  # Original segment text
            "subject": "aerodynamics"  # Example subject (you can modify as needed)
        }
        for i in range(len(doc_embeddings["dense"]))
    ]
    
    # Insert the batch into the knowledge base
    res = clientZ.insert(collection_name="book", data=data)
    
    # Free up memory after each batch (optional)
    del doc_embeddings
    print(f"Processed and inserted chunk: {chunk['title']}, len:{len(segments)}")

print("All files processed and inserted into the knowledge base.")

'''
result = clientZ.files.delete(
    file_id=batch_id       
)
'''

'''
queries = ["什么是哥西问题？"]
query_vectors = bge_m3_ef.encode_queries(queries)

res = clientZ.search(
    collection_name="book", # target collection
    data=query_vectors["dense"], # query vectors
    search_param={"metric_type": "L2", "params": {}},
    limit=5, 
)

print(res)

for query_index, query_result in enumerate(res):
    # Extract the IDs from the search result
    ids_to_retrieve = [hit['id'] for hit in query_result]

    # Fetch the documents using the extracted IDs
    final_results = clientZ.get(
        collection_name="book",  # Use your collection name
        ids=ids_to_retrieve
    )

    # Create a mapping from ID to the corresponding document fetched from clientZ.get()
    id_to_document = {record["id"]: record for record in final_results}

    # Now loop through the original search results and print the corresponding text in the original order
    print(f"\nResults for Query {query_index + 1}:")
    for hit in query_result:
        document = id_to_document.get(hit['id'])
        if document:
          print(f"ID: {hit['id']}, Match Text: {document.get('text')}, Distance: {hit['distance']}")'''