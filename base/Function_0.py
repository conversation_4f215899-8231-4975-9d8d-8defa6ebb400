import glob
import json
import logging
import os
import re
import shutil
import subprocess
from io import StringIO
from subprocess import Popen, PIPE, STDOUT
import cst_modeling
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
from tecplot.constant import PlotType
import tecplot
import sys
from cfdpost2.cfdresult import cfl3d2
from cfdpost2.section.physical import PhysicalSec
import time
import openai

from zhipuai import ZhipuAI
api_key = '63dadaa668cbe3eabbca4842bf801297.Lhqjs2iZk0sghldk'
client = ZhipuAI(api_key=api_key)
openai.api_key = "********************************************************************************************************************************************************************"

# Xfoil
def xfoil_calculation(foil, re, alfa):
    def xfoil_interact(foil, re, alfa):
        def get_cmd(foil, re, alfa):
            if foil is not None:
                load_cmd = 'LOAD ' + foil
                # print(f'{foil}')
            else:
                load_cmd = 'NACA 0012'
            # print(f'{load_cmd}')
            cmd = '''    
            {}

            OPER
            VISC {} 
            ITER
            1000
            PACC
            OUTPUT/re={}_aoa={}_SAVE

            a{}

            '''.format(load_cmd, re, re, alfa, alfa, )
            return cmd

        p = Popen(['./XFOIL/xfoil.exe'], stdout=PIPE, stdin=PIPE, stderr=STDOUT)
        stdout = p.communicate(
            input=get_cmd(foil=foil, re=re, alfa=alfa).encode())[0]
        # print(stdout)
        # print('=====================================================')
        return stdout.decode('utf-8')

    if re == 0 or None:
        re = 200000

    def clear_output_folder(output_folder='output'):
        # Check if the output folder exists
        if os.path.exists(output_folder):
            # Remove all files in the output folder
            shutil.rmtree(output_folder)
        # Recreate the empty folder
        os.makedirs(output_folder)

    # Clear the output folder before running a new calculation
    clear_output_folder()
    # Run XFoil for the given airfoil, Reynolds number, and angle of attack
    raw_output = xfoil_interact(foil, re, alfa)
    # print('xfoil_cal')

    # Read results from the output file
    file_content = None

    # Read the entire output file as text
    output_file = './XFOIL/save'
    if os.path.exists(output_file):
        with open(output_file, 'r') as infile:
            file_content = infile.read()  # Read the entire file as a single string
    else:
        print(f"Warning: Output file '{output_file}' not found.")

    # Return the full file content and the raw XFoil output
    return {
        "file_content": file_content,
    }

# VLM3d
from pytornado.stdfun.run import StdRunArgs, standard_run
def VLM3D_calculation(airspeed=100, alpha=2.5, foil=None, ):
    """
    API to run VLM program with custom airspeed, alpha, and airfoil and return log information.

    ### params:
    - `setting_file`: file path for settings
    - `airspeed`: optional, custom airspeed value
    - `alpha`: optional, custom angle of attack value (alpha)
    - `foil`: optional, path to the airfoil data file
    - `verbose`: optional, enables verbose output
    - `debug`: optional, enables debug mode
    - `quiet`: optional, suppresses output
    """
    if airspeed == 0 or None:
        airspeed = 100
    setting_file = 'pytornado/settings/template.json'
    if foil is None:
        foil = 'pytornado/airfoils/foil.dat'
    # Load the current state from the template file
    state_file = 'pytornado/state/template.json'
    with open(state_file, 'r') as f:
        state_data = json.load(f)

    # Load the aircraft configuration file
    aircraft_file = 'pytornado/aircraft/template_aircraft.json'
    with open(aircraft_file, 'r') as f:
        aircraft_data = json.load(f)

    # If airspeed or alpha are provided, update them in the state data
    if airspeed is not None:
        state_data['aero']['airspeed'] = airspeed

    if alpha is not None:
        state_data['aero']['alpha'] = alpha

    # If a custom airfoil file is provided, update it in the aircraft file
    if foil is not None:
        aircraft_data['wings'][0]['segments'][0]['airfoils']['inner'] = f"{foil}"
        aircraft_data['wings'][0]['segments'][0]['airfoils']['outer'] = f"{foil}"

    # Save the updated state back to the template file
    with open(state_file, 'w') as f:
        json.dump(state_data, f, indent=4)

    # Save the updated aircraft configuration back to the file
    with open(aircraft_file, 'w') as f:
        json.dump(aircraft_data, f, indent=4)

    # Set up logging to capture logs to a string (StringIO)
    log_stream = StringIO()
    stream_handler = logging.StreamHandler(log_stream)
    stream_handler.setLevel(logging.INFO)

    # Create a specific logger for this function
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.addHandler(stream_handler)

    # Log the start of the process
    logger.info("Running VLM simulation with the following settings:")
    logger.info(f"Setting file: {setting_file}")
    if airspeed is not None:
        logger.info(f"Custom airspeed: {airspeed}")
    if alpha is not None:
        logger.info(f"Custom alpha: {alpha}")
    if foil is not None:
        logger.info(f"Custom airfoil: {foil}")

    # Proceed with running the VLM simulation
    args = StdRunArgs(run=setting_file, verbose=False, debug=False, quiet=False)
    results = standard_run(args)

    # Log the completion of the simulation
    logger.info("Simulation complete.")

    # Capture the logs
    stream_handler.flush()  # Ensure all logs are written to the stream
    log_output = log_stream.getvalue()  # Get the string from the StringIO buffer

    # Clean up the handler from the logger
    logger.removeHandler(stream_handler)
    log_stream.close()
    # print('VLM_cal')

    # Return the useful log information instead of raw result objects
    return log_output


# parameters processing
def Aerodynamic_parameters(re=None, airspeed=None, mach=None, alpha=None, a=336.4, rho=1.17, chord_length=1.0,
                           mu=1.81e-5):
    """
    Function to convert between Reynolds number, airspeed, and Mach number.

    :param re: Optional, Reynolds number to convert to airspeed (if given).
    :param airspeed: Optional, airspeed (m/s) to convert to Reynolds number (if given).
    :param mach: Optional, Mach number to convert to airspeed or calculate from airspeed (if given).
    :param a: Speed of sound in m/s. Defaults to 336.4 m/s (standard condition).
    :param rho: Air density in kg/m³. If None, defaults to 1.225 kg/m³ (standard conditions).
    :param chord_length: Characteristic length, such as airfoil chord length in meters. If None, defaults to 1.0 m.
    :param mu: Dynamic viscosity of air in Pa·s. If None, defaults to 1.81e-5 Pa·s (standard for air at 15°C).

    :return: Returns a dictionary with calculated Reynolds number, airspeed, and Mach number.
    """
    # print(re, airspeed, mach)
    if airspeed is not None and airspeed == 0:
        airspeed = None
    if mach is not None and mach == 0:
        mach = None
    if re is not None and re == 0:
        re = None
    result = {}  # To store all calculated values
    print("inpt", re, airspeed, mach)
    if airspeed is None:
        if mach is not None:
            airspeed = mach * a  # 根据马赫数计算空速
        elif re is not None:
            airspeed = re * mu / (rho * chord_length)  # 根据雷诺数计算空速
        else:
            re = 0
            airspeed = 0
            # raise ValueError("At least one of Reynolds number, airspeed, or Mach number must be provided.")

    if mach is None:
        mach = airspeed / a  # 根据空速计算马赫数

    if re is None:
        re = (rho * airspeed * chord_length) / mu

    if alpha is None:
        alpha = 0
    # 如果提供了Reynolds数和空速

    '''if (re != None and 0) and (airspeed != None and 0) and (mach == None or 0):
        # 使用提供的空速计算Reynolds数
        calculated_re = (rho * airspeed * chord_length) / mu

        # 计算提供的Reynolds数和计算Reynolds数之间的相对误差
        relative_error = abs(calculated_re - re) / re
        print(re, airspeed)
        # 如果相对误差超过1%，则抛出错误
        if relative_error > 0.01:
            raise ValueError(f"The relative error between the provided Re and the calculated Re exceeds 1% "
                             f"(Relative Error: {relative_error * 100:.2f}%). "
                             f"Provided Re: {re}, Calculated Re: {calculated_re}.")

        # 计算并存储Reynolds数和空速
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了空速，则计算Reynolds数
    elif (re == None or 0) and (airspeed != None and 0):
        re = (rho * airspeed * chord_length) / mu
        result["Reynolds_number"] = re
        result["Airspeed"] = airspeed
        result["Mach"] = airspeed/a

    # 如果只提供了Reynolds数，则计算空速
    elif (airspeed == None or 0) and (re != None and 0):
        airspeed = (re * mu) / (rho * chord_length)
        result["Airspeed"] = airspeed
        result["Reynolds_number"] = re
        result["Mach"] = airspeed/a

    # 如果提供了马赫数，则根据声速计算空速
    if (mach != None and 0) and (airspeed == None or 0):
        airspeed = mach * a
        result["Airspeed"] = airspeed
        result["Mach"] = mach

    # 如果提供了空速，则计算马赫数
    elif (airspeed != None and 0) and mach == None:
        mach = airspeed / a
        result["Mach"] = mach'''

    # 如果没有提供足够的参数，则抛出错误
    # if airspeed == None or 0 and re ==None or 0 and mach == None or 0:

    # Include all parameters in the result
    result["Mach"] = mach
    result["Airspeed"] = airspeed
    result["Reynolds_number"] = re
    result["rho"] = rho
    result["chord_length"] = chord_length
    result["mu"] = mu
    result["speed_of_sound"] = a  # 声速
    result["alpha"] = alpha
    print("outpt:", re, airspeed, mach)
    return result


# cfl3d
def cfl3d_simulation(input_foil_file, mach, alpha, re, visualize=True):
    # 确保所有操作都在 ./cfl3d 目录中进行
    cfl3d_dir = './cfl3d'
    output_folder = 'output'

    # 1. 读取翼型文件并重命名为 foil.dat，并添加特定的头信息
    def process_foil_file(input_foil_file, output_foil_file, num_points=1000):
        from cst_modeling.basic import reconstruct_curve_by_length
        #print("处理翼型文件...", input_foil_file)
        with open(input_foil_file, 'r') as f:
            data = f.readlines()
        header = "Variables= X Y \n"

        # Find the boundary between upper and lower surfaces
        half_point = len(data) // 2
        zone_line = f"\n zone i= {num_points}\n"

        x_upper, y_upper = [], []
        x_lower, y_lower = [], []

        for i, line in enumerate(data):
            values = line.split()
            if len(values) == 2:
                if i < half_point:
                    x_lower.append(float(values[0]))
                    y_lower.append(float(values[1]))
                else:
                    x_upper.append(float(values[0]))
                    y_upper.append(float(values[1]))
        sorted_lower = sorted(zip(x_lower, y_lower), key=lambda pair: pair[0])
        x_lower, y_lower = zip(*sorted_lower)
        sorted_upper = sorted(zip(x_upper, y_upper), key=lambda pair: pair[0])
        x_upper, y_upper = zip(*sorted_upper)
        x_lower = np.array(x_lower)
        y_lower = np.array(y_lower)
        x_upper = np.array(x_upper)
        y_upper = np.array(y_upper)
        # Reconstruct curves for upper and lower surfaces
        lower_curve = np.concatenate((x_lower[:, None], y_lower[:, None], np.zeros([x_lower.shape[0], 1])), axis=1)
        upper_curve = np.concatenate((x_upper[:, None], y_upper[:, None], np.zeros([x_upper.shape[0], 1])), axis=1)

        new_lower_curve = reconstruct_curve_by_length(lower_curve, n=num_points)
        new_upper_curve = reconstruct_curve_by_length(upper_curve, n=num_points)

        plt.figure()
        plt.plot(x_lower, y_lower, 'k.-', label='Lower Surface')
        plt.plot(new_lower_curve[:, 0], new_lower_curve[:, 1], 'r.-', label='Reconstructed Lower Surface')
        plt.plot(x_upper, y_upper, 'b.-', label='Upper Surface')
        plt.plot(new_upper_curve[:, 0], new_upper_curve[:, 1] + 0.5, 'g.-', label='Reconstructed Upper Surface')

        plt.legend()
        plt.savefig('reconstruct_curve.jpg', dpi=300)
        plt.close()

        # Save new curves to output DAT file
        with open(output_foil_file, 'w') as f:
            f.write(header)
            f.write(zone_line)
            np.savetxt(f, new_lower_curve[:, :2], fmt='%.6f', comments='')
            f.write(zone_line)
            np.savetxt(f, new_upper_curve[:, :2], fmt='%.6f', comments='')

    # 2. 运行 cgrid.exe
    def run_cgrid_exe():
        # print("运行 cgrid.exe...")
        #result = subprocess.run(['./cfl3d/cgrid.exe'], cwd=cfl3d_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        result = subprocess.run(
        ['./cgrid.exe'],
        cwd=cfl3d_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
        )
        '''if result.returncode == 0:
            print("cgrid.exe 执行成功")
        else:
            print(f"cgrid.exe 执行失败: {result.stderr.decode()}")'''

    # 3. 修改cfl3d.inp中的mach，alpha，re参数
    def modify_cfl3d_input(file_path, mach_value, alpha_value, re_value):
        with open(file_path, 'r') as file:
            lines = file.readlines()

        # 查找并修改 Mach, Alpha 和 ReUe 的值
        for i, line in enumerate(lines):
            if "XMACH     ALPHA      BETA  REUE,MIL" in line:
                values = lines[i + 1].split()
                reue = re_value / 1e6
                values[0] = f"{mach_value:.2f}"  # XMACH
                values[1] = f"{alpha_value:.2f}"  # ALPHA
                values[3] = f"{reue:.2f}"  # REUE
                lines[i + 1] = "   ".join(values) + "\n"
                break

        # 更新 IVISC(I), IVISC(J), IVISC(K) 的值
        for i, line in enumerate(lines):
            if "IVISC(I)  IVISC(J)  IVISC(K)" in line:
                values = lines[i + 1].split()
                if len(values) >= 7:  # 确保有足够的元素进行索引
                    values[4] = "5"  # IVISC(I)
                    values[5] = "5"  # IVISC(J)
                    values[6] = "5"  # IVISC(K)
                    lines[i + 1] = "   ".join(values) + "\n"
                break

        # 保存修改后的内容
        with open(file_path, 'w') as file:
            file.writelines(lines)
        # print("已修改 cfl3d.inp 文件中的 Mach, Alpha 和 ReUe 参数")

    # 4. 将生成的文件复制到output目录中，并运行cfl3d_Seq_LHR.exe
    def run_cfl3d_in_output_folder(output_folder):
        output_folder_path = os.path.join(cfl3d_dir, output_folder)
        if not os.path.exists(output_folder_path):
            os.makedirs(output_folder_path)

        # 复制必要文件到 output 文件夹
        files_to_copy = ['cfl3d.inp', 'cfl3d.xyz']
        for file in files_to_copy:
            src_file = os.path.join(cfl3d_dir, file)
            if os.path.exists(src_file):
                shutil.copy(src_file, os.path.join(output_folder_path, file))
            else:
                print(f"文件 {file} 未找到，无法复制到 {output_folder_path}")

        # 切换到 output 文件夹并运行 cfl3d_Seq_LHR.exe
        # print("运行 cfl3d_Seq_LHR.exe...")
        cmd = 'y\n'

        try:
            # 使用 Popen 来运行 cfl3d_Seq_LHR.exe，并通过 stdin 传递 y 以自动确认提示
            p = subprocess.Popen(
                ['./cfl3d_Seq_LHR.exe'],
                cwd=output_folder_path,
                stdin=subprocess.PIPE,  # 允许传递输入
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            # 通过 stdin 传递 'y' 并继续运行
            #stdout = p.communicate(input=cmd.encode())[0]
            stdout, stderr = p.communicate(input=cmd.encode(), timeout=150)
            return stdout, stderr
            # 打印输出和错误信息
            '''if stdout:
                try:
                    print("Standard Output (binary):")
                    print(stdout)
                except UnicodeDecodeError:
                    print("Standard Output: Binary data received, unable to decode.")
            else :
                print("no stdout")
            # Print standard error if available
            if stderr:
                try:
                    print("Standard Error: ", stderr.decode('utf-8'))
                except UnicodeDecodeError:
                    print("Standard Error: Binary error message received, unable to decode.")
            else :
                print("no stderr")'''
        except Exception as e:
            print(f"运行过程中发生错误: {str(e)}")

    # 5. 读取 cfl3d.out 文件并返回其文本数据
    def read_cfl3d_output(output_folder):

        output_file = os.path.join(cfl3d_dir, output_folder, 'cfl3d.out')
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                content = f.readlines()

            # Find the line containing the specific marker
            start_indices = [i + 1 for i, line in enumerate(content) 
                            if "***** FORCE AND MOMENT SUMMARIES - FINAL TIME STEP/MULTIGRID CYCLE *****" in line]
            
            if not start_indices:
                print("未找到指定的标记行")
                last_20_lines = ''.join(content[-20:]) if len(content) >= 20 else ''.join(content)
                return f"未找到指定的标记行，可能计算未收敛，以下是输出文件最后二十行：\n{last_20_lines}"

            # Get the last occurrence index
            start_index = start_indices[-1]

            # Extract lines after the marker
            extracted_lines = content[start_index:]

            # Join the extracted lines into a single string
            extracted_text = ''.join(extracted_lines)
            return extracted_text
        else:
            print(f"找不到输出文件 {output_file}")
            return "找不到输出文件，可能是cfl3d调用出现了问题"
        '''if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                content = f.read()
            extracted_lines = []

            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "lift" in line.lower() and "drag" in line.lower():
                    # 提取接下来几行的数据
                    extracted_lines.extend(lines[i:i + 6])

            # 提取每次迭代的升力和阻力数据
            lift_drag_data = []
            for line in extracted_lines:
                # 匹配数据格式的行，包含迭代次数、残差、升力和阻力等
                if isinstance(line, str) and rre.match(
                        r"\s*\d+\s+\d+\s+\d+\s+[\d.E+-]+\s+[\d.E+-]+\s+[\d.E+-]+\s+[\d.E+-]+\s+[\d.E+-]+", line):
                    values = line.split()
                    iteration = int(values[2])  # 迭代次数
                    lift = float(values[5])  # 升力系数
                    drag = float(values[6])  # 阻力系数
                    lift_drag_data.append({'iteration': iteration, 'lift': lift, 'drag': drag})

            # 获取最后一个迭代的升力和阻力数据
            if lift_drag_data:
                final_data = lift_drag_data[-1]
                return {
                    'final_iteration': final_data['iteration'],
                    'final_lift': final_data['lift'],
                    'final_drag': final_data['drag']
                }
            else:
                return None
        else:
            print(f"找不到输出文件 {output_file}")
            return None'''

    # 主流程开始
    
    try:
        # 确保所有路径使用 ./cfl3d 目录
        input_foil_file_path = os.path.join(input_foil_file)
        output_foil_file_path = os.path.join(cfl3d_dir, 'foil.dat')
        cfl3d_inp_path = os.path.join(cfl3d_dir, 'cfl3d.inp')

        # 1. 处理翼型文件
        process_foil_file(input_foil_file_path, output_foil_file_path)

        # 2. 运行 cgrid.exe
        run_cgrid_exe()

        # 3. 修改 cfl3d.inp 文件中的 Mach, Alpha 和 Re 参数
        modify_cfl3d_input(cfl3d_inp_path, mach, alpha, re)

        # 4. 复制文件并在 output 文件夹中运行 cfl3d_Seq_LHR.exe
        stdout, stderr = run_cfl3d_in_output_folder(output_folder)
        if stderr:
            print(f"Standard Error: {stderr}")
            return stderr
        # 5. 读取 cfl3d.out 文件内容
        output_data = read_cfl3d_output(output_folder)

    except Exception as e:
        print(f"An error occurred during the CFD process: {e}")
        return f"Error exporting cloud maps: {e}"

    # Separate try block for outputting cloud maps
    if visualize:
        try:
            # 6. 输出云图
            cfl3d2tec(foil_p=input_foil_file,mach_p=mach, alpha=alpha)
            export_cloud_maps(foil=input_foil_file, mach=mach, alpha=alpha)
            cfl3d_dir = './cfl3d'
            
            log_output_data(alpha, mach, input_foil_file, output_data)
            # List of files to copy with versioning
            '''
            img_dir = os.path.join(cfl3d_dir, 'img')
            cache_dir = os.path.join(cfl3d_dir, 'cache')
            files_to_copy = [
                os.path.join(img_dir, 'combined_cloud_maps.png'),
                os.path.join(img_dir, 'foil_Cp.png'),
                os.path.join(cfl3d_dir, 'flow-field.dat')
            ]

            # Copy each file with versioning
            for file_path in files_to_copy:
                copy_file_with_versioning(file_path, cache_dir, output_data_log=output_data)'''
        except Exception as e:
            print(f"An error occurred while exporting cloud maps: {e}")

    return output_data  # Return output data regardless of cloud map success

def log_output_data(alpha, mach, input_foil_file, output_data):
    # 定义日志文件路径
    log_file_path = './cfl3d/img/logfile.txt'
    
    # 准备要写入日志文件的内容
    log_content = f"Parameters: alpha={alpha}, mach={mach}, foil={input_foil_file}\n"
    log_content += f"Output Data: {output_data}\n"
    log_content += "-"*50 + "\n"  # 分隔线，方便阅读
    
    # 打开日志文件，使用 'a' 模式进行追加写入
    with open(log_file_path, 'a') as logfile:
        logfile.write(log_content)

'''def copy_file_with_versioning(src_file, dst_dir, output_data_log=None):
    """
    Copies a file to the destination directory with versioning and logs output_data if provided.
    
    Args:
        src_file (str): The path to the source file.
        dst_dir (str): The path to the destination directory.
        output_data_log (dict): A dictionary containing output data to log. Keys are filenames, values are data.
    """
    if not os.path.exists(src_file):
        print(f"Source file {src_file} does not exist.")
        return
    
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)
    
    # Extract the base name and extension of the source file
    base_name, ext = os.path.splitext(os.path.basename(src_file))
    
    # Get all files in the destination directory that match the base name pattern
    cache_files = [f for f in os.listdir(dst_dir) if f.startswith(base_name) and f.endswith(ext)]
    
    # If no files found, start numbering from 1; otherwise, find the next index
    if not cache_files:
        next_index = 1
    else:
        indices = [int(f.split('.')[0].split('-')[-1]) for f in cache_files if '-' in f.split('.')[0]]
        next_index = max(indices) + 1 if indices else 1
    
    # Construct the new filename with the next index
    new_filename = f'{base_name}-{next_index}{ext}'
    dst_file = os.path.join(dst_dir, new_filename)
    
    try:
        shutil.copy2(src_file, dst_file)  # Use copy2 to preserve metadata
        print(f"Successfully copied {src_file} to {dst_file}")
        
        # Log output_data if provided
        if output_data_log is not None and base_name in output_data_log:
            log_output_data(base_name, next_index, output_data_log[base_name], dst_dir)
    except Exception as e:
        print(f"Failed to copy file: {e}")

def log_output_data(base_name, index, output_data, dst_dir):
    """
    Logs the output_data into a text file with an associated index.
    
    Args:
        base_name (str): Base name of the file being logged.
        index (int): Index number corresponding to the file version.
        output_data (str or dict): Data to be logged.
        dst_dir (str): Destination directory for the log file.
    """
    log_filename = os.path.join(dst_dir, f'{base_name}-output-{index}.txt')
    with open(log_filename, 'w') as log_file:
        if isinstance(output_data, dict):
            for key, value in output_data.items():
                log_file.write(f"{key}: {value}\n")
        else:
            log_file.write(str(output_data))
        print(f"Logged output data to {log_filename}")
'''
## result processing
def cfl3d2tec(foil_p,mach_p, alpha, path='./cfl3d/output', iVar=None, var_name=None, j0=60, j1=361):
    def post_foil_cfl3d(path, j0, j1, nHi=40000, fname='feature2d.txt', tecplot=True):
        '''
        Read in CFL3D foil result and extract flow features.

        Inputs:
        ---
        path:   folder that contains the output files \n
        j0:     j index of the lower surface TE \n
        j1:     j index of the upper surface TE \n
        nHi:    maximum number of mesh points in k direction for boundary layer \n
        fname:  output file name. If None, then no output \n
        tecplot:    True, then convert cfl3d.prt to surface.dat \n

        Single C-block cfl3d.prt index
        ---
        ```text
        i : 1 - 1   symmetry plane
        j : 1 - nj  far field of lower surface TE to far field of upper surface TE
        k : 1 - nk  surface to far field
        ```
        '''

        converge, CL, CD, Cm, CDp, CDf = cfl3d2.readCoef(path)

        if not converge:
            print('  CFD not converged. ' + path)
            return

        succeed1, AoA = cfl3d2.readAoA(path)
        succeed2, Minf, AoA0, Re, l2D = cfl3d2.readinput(path)
        succeed3, field, foil, _ = cfl3d2.readprt_foil(path, j0=j0, j1=j1)

        if not succeed1:
            AoA = AoA0

        # print(Minf, AoA, Re * 1e6, CL)

        if not succeed2 or not succeed3:
            print('  CFD output file corrupted. ' + path)
            return

        (x, y, Cp1, _, _) = foil
        (X, Y, U, V, P, T, Ma, Cp, vi) = field

        '''Hi, Hc, info = PhysicalSec.getHi(X, Y, U, V, T, j0=j0, j1=j1, nHi=nHi)
        (Tw, dudy) = info
        # print(Cp1)
        fF = PhysicalSec(Minf, AoA, Re * 1e6)
        fF.setdata(x, y, Cp1, Tw, Hi, Hc, dudy)
        fF.extract_features()'''
        plt.figure(figsize=(8, 6))
        plt.plot(x, -Cp1, label='Cp', color='red', linewidth=1.5)
        plt.xlabel('X/C (Chord Length)')
        plt.ylabel('Cp (Pressure Coefficient)')
        plt.title('Pressure Coefficient Distribution over the Foil')
        plt.axhline(0, color='gray', linestyle='--', linewidth=0.8)  # Line at Cp = 0
        plt.legend()
        plt.grid()
        plt.tight_layout()
        #plt.show()
        plt.savefig(f'./cfl3d/img/foil_Cp_{foil_p}_mach={mach_p}_alpha={alpha}.png', dpi=500)
        plt.close()
        '''if fname is not None:
            fF.output_features(fname=fname, append=True, keys_=None)

        if tecplot:
            succeed = cfl3d2.readprt(path)

        return fF'''

    # Set default values if not provided
    if iVar is None:
        iVar = [1, 2, 3, 5, 6, 7, 9, 10, 11]  # Default iVar values
    if var_name is None:
        var_name = ['density', 'u', 'v', 'e', 'velocity', 'T', 'M', 'p', 'cp']  # Default variable names

    t0 = time.perf_counter()

    # Read the data from plot2d
    xy, qq, mach, alfa, reyn = cfl3d2.readPlot2d(path)
    # print(f"Coordinates: {xy}")

    # Analyze the data from plot3d
    variables = cfl3d2.analysePlot3d(mach, qq, iVar, gamma_r=1.4)

    # Output the analyzed data to Tecplot format
    cfl3d2.outputTecplot(xy, variables, var_name, fname='./cfl3d/flow-field.dat', append=False)

    # Read additional data (if needed)
    cfl3d2.readprt(path=path)

    # Post-process the foil data
    post_foil_cfl3d(path, j0=j0, j1=j1, fname="feature2d.txt")

    t1 = time.perf_counter()
    # print('Processing Time = %.3f s' % (t1 - t0))
    img_dir = 'img'
    tecplot_file = './cfl3d/flow-field.dat'
    fname = "feature2d.txt"
    if not os.path.exists(img_dir):
        os.makedirs(img_dir)

    # Copy the generated files to the img folder
    try:
        shutil.copy(tecplot_file, os.path.join(tecplot_file))  # Copy flow-field.dat
        shutil.copy(fname, os.path.join(fname))  # Copy feature2d.txt
        print(f'Files copied to {img_dir}')
    except Exception as e:
        print(f'Error copying files: {e}')

def export_cloud_maps(mach, alpha, foil):
    logging.basicConfig(stream=sys.stdout, level=logging.INFO)

    tecplot.session.connect(port=7600)
    '''if '-c' in sys.argv:
        tecplot.session.connect()'''

    data_file = './cfl3d/flow-field.dat'
    #data_file ="E:/Python/AirGPT/cfl3d/flow-field.dat"
    dataset = tecplot.data.load_tecplot(data_file)

    # Activate a 2D field plot (likely appropriate for flow field data)
    frame = tecplot.active_frame()
    plot = frame.plot()

    def export_cloud_map(variable_name, output_path):
        contour = plot.contour(0)
        contour.variable = dataset.variable(variable_name)
        contour.levels.reset_to_nice()
        # Set the contour fill mode to continuous
        contour_filter = contour.colormap_filter
        contour_filter.distribution = tecplot.constant.ColorMapDistribution.Continuous
        # Export the image
        tecplot.export.save_png(output_path, 600, supersample=3)
        # print(f'{variable_name} cloud map saved as {output_path}')

    # Set the plot type to Field Plot (XYLine, Contour, or Vector)
    frame.plot_type = PlotType.Cartesian2D  # Setting plot type for a 2D plot (appropriate for contour/vector)
    plot = frame.plot(PlotType.Cartesian2D)

    frame.plot().show_contour = True
    frame.plot().contour(0).levels.reset_to_nice()
    plot.axes.x_axis.min = -0.5
    plot.axes.x_axis.max = 1.5
    plot.axes.y_axis.min = -1.0
    plot.axes.y_axis.max = 1.0
    # for variable in dataset.variables():
    #    print(variable.name)

    export_cloud_map('p', './cfl3d/img/pressure_cloud.png')
    export_cloud_map('density', './cfl3d/img/density_cloud.png')
    export_cloud_map('T', './cfl3d/img/temperature_cloud.png')
    export_cloud_map('velocity', './cfl3d/img/velocity_cloud.png')

    #export_cloud_map('p', 'E:/Python/AirGPT/cfl3d/img/pressure_cloud.png')
    #export_cloud_map('density', 'E:/Python/AirGPT/cfl3d/img/density_cloud.png')
    #export_cloud_map('T', 'E:/Python/AirGPT/cfl3d/img/temperature_cloud.png')
    #export_cloud_map('velocity', 'E:/Python/AirGPT/cfl3d/img/velocity_cloud.png')
    output_image_path = './cfl3d/img/flow_field_image.png'
    density_img = Image.open('./cfl3d/img/density_cloud.png')
    pressure_img = Image.open('./cfl3d/img/pressure_cloud.png')
    temperature_img = Image.open('./cfl3d/img/temperature_cloud.png')
    velocity_img = Image.open('./cfl3d/img/velocity_cloud.png')
    fig, axs = plt.subplots(2, 2, figsize=(10, 10))

    # Remove axes for a cleaner appearance
    for ax in axs.flat:
        ax.axis('off')

    # Add the images to the 2x2 grid
    axs[0, 0].imshow(density_img)
    axs[0, 0].set_title('Density Cloud Map')

    axs[0, 1].imshow(pressure_img)
    axs[0, 1].set_title('Pressure Cloud Map')

    axs[1, 0].imshow(temperature_img)
    axs[1, 0].set_title('Temperature Cloud Map')

    axs[1, 1].imshow(velocity_img)
    axs[1, 1].set_title('Velocity Cloud Map')

    # Save the combined figure
    plt.tight_layout()
    plt.savefig(f'./cfl3d/img/combined_cloud_maps_{foil}_mach={mach}_alpha={alpha}.png', dpi=1000)
    plt.show(block=False)
    plt.pause(10)
    #plt.close()


'''contour = plot.contour(0)
contour.variable = tecplot.active_frame().dataset.variable('Pressure')  # Change to any valid variable like 'Velocity Magnitude'
contour.colormap_name = 'Rainbow'  # Set the colormap for visualization

# Optional: Add streamlines (if velocity data is available)
plot.show_streamtraces = True
plot.streamtraces.add_rake(start=(-1, -1), end=(1, 1), num_seed_points=20)
'''

# foil processing
def reconstruct_curve(input_filepath, output_filepath, num_points=201):
    '''
    Reconstruct a curve
    '''
    from cst_modeling.basic import reconstruct_curve_by_length

    with open(input_filepath, 'r') as f:
        data = f.readlines()

    header = "Variables= X Y \n"

    # Find the boundary between upper and lower surfaces
    half_point = len(data) // 2
    zone_line = f"\n zone i= {num_points}\n"

    x_upper, y_upper = [], []
    x_lower, y_lower = [], []

    for i, line in enumerate(data):
        values = line.split()
        if len(values) == 2:
            if i < half_point:
                x_lower.append(float(values[0]))
                y_lower.append(float(values[1]))
            else:
                x_upper.append(float(values[0]))
                y_upper.append(float(values[1]))

    x_lower = np.array(x_lower)
    y_lower = np.array(y_lower)
    x_upper = np.array(x_upper)
    y_upper = np.array(y_upper)

    # Reconstruct curves for upper and lower surfaces
    lower_curve = np.concatenate((x_lower[:, None], y_lower[:, None], np.zeros([x_lower.shape[0], 1])), axis=1)
    upper_curve = np.concatenate((x_upper[:, None], y_upper[:, None], np.zeros([x_upper.shape[0], 1])), axis=1)

    new_lower_curve = reconstruct_curve_by_length(lower_curve, n=num_points)
    new_upper_curve = reconstruct_curve_by_length(upper_curve, n=num_points)

    plt.figure()
    plt.plot(x_lower, y_lower, 'k.-', label='Lower Surface')
    plt.plot(new_lower_curve[:, 0], new_lower_curve[:, 1], 'r.-', label='Reconstructed Lower Surface')
    plt.plot(x_upper, y_upper, 'b.-', label='Upper Surface')
    plt.plot(new_upper_curve[:, 0], new_upper_curve[:, 1] + 0.5, 'g.-', label='Reconstructed Upper Surface')

    plt.legend()
    plt.savefig('reconstruct_curve.jpg', dpi=300)
    plt.close()

    # Save new curves to output DAT file
    with open(output_filepath, 'w') as f:
        f.write(header)
        f.write(zone_line)
        np.savetxt(f, new_lower_curve[:, :2], fmt='%.6f', comments='')
        f.write(zone_line)
        np.savetxt(f, new_upper_curve[:, :2], fmt='%.6f', comments='')

def foil_preprocessing(foilfile, ):
    # path = os.path.dirname(__file__)  # or your specific path
    with open(os.path.join(foilfile), 'r') as f:
        lines = f.readlines()

    # Read the data points from the file
    # data = np.array([list(map(float, line.split())) for line in lines[:]])
    data = []

    # Read the data points from the file
    for line in lines:
        line = line.strip()  # Remove leading/trailing whitespace
        if not line or line.startswith('#'):  # Skip blank lines or headers
            continue
        try:
            x, y = map(float, line.split())
            data.append([x, y])
        except ValueError:
            continue  # Skip lines that don't have two float values

    # Convert to a NumPy array
    data = np.array(data)
    # Initialize lists to hold upper and lower surfaces
    upper_surface = []
    lower_surface = []

    # Identify partition points
    partition_indices = np.where((data[:, 0] == 0) | (data[:, 0] == 1))[0]
    print(partition_indices)
    overall_avg_y = np.mean(data[:, 1])

    # Group segments based on pairs of indices
    for i in range(len(partition_indices) - 1):
        start = partition_indices[i]
        end = partition_indices[i + 1]

        # Ensure we skip segments that don't have enough data
        if end - start <= 1:
            continue
        print(start, end)
        group_data = data[start:end + 1]

        # Calculate the average y for the group
        avg_y = np.mean(group_data[:, 1])

        # Determine if it's upper or lower surface
        if avg_y > overall_avg_y:
            upper_surface.extend(group_data)
        else:
            lower_surface.extend(group_data)

    # Convert to arrays for easier handling
    upper_surface = np.array(upper_surface)
    lower_surface = np.array(lower_surface)
    upper_surface = upper_surface[np.argsort(upper_surface[:, 0])]
    lower_surface = lower_surface[np.argsort(lower_surface[:, 0])]

    return upper_surface, lower_surface

# BLWF
def BLWF(
    mach, alpha, re, cax=5, file_path='BLWF/input.058', 
    foil_paths=[],  
    z=[], xle=[], yle=[], chord=[], thick=[], epsil=[],
    #user_prompt,
):
    """
    Modify the input.058 file for BLWF with new aerodynamic parameters and WING/BODY data, including foil data (XU, YU, XL, YL).
    
    Parameters:
    - mach: Free stream Mach number (e.g., 0.74).
    - alpha: Angle of attack in degrees (e.g., 2.0).
    - beta: Yaw angle in degrees (e.g., 0.0).
    - re: Reynolds number (e.g., 3000000.0).
    - cax: Reference length for Reynolds number (e.g., 0.25).
    - sw: Half of the reference wing area (e.g., 0.17).
    - foil_paths: List of file paths to the foil data for each wing section (e.g., 'foil_section1.txt').
    - z, xle, yle, chord, thick, epsil, ysym: Lists of independent parameters for each wing section.
    - file_path: Path to the input.058 file (default is 'BLWF/input.058').
    
    Constants:
    - fsec: Always set to 1 (new profile is supplied).
    - xsing, ysing: Always set to 0 (transformation singular line).
    - trail: Always set to 10 (trailing edge angle in degrees).
    - slopt: Always set to -0.125 (slope of the mean camber line at the trailing edge).
    """

    '''parameters = design_parameters_from_llm(user_prompt)

    # Extract individual parameters from the returned dictionary
    z = parameters['z']
    xle = parameters['xle']
    yle = parameters['yle']
    chord = parameters['chord']
    thick = parameters['thick']
    epsil = parameters['epsil']'''    
    #print(z, len(z))
    # Automatically calculate NSW based on the number of sections
    nsw = len(foil_paths)
    #print(foil_paths)

    # Ensure the lists for wing sections have the same length and foil paths match the number of sections
    if not all(len(lst) == nsw for lst in [z, xle, yle, chord, thick, epsil]):
        raise ValueError("All wing section parameter lists must have the same length.")
    if len(foil_paths) != nsw:
        raise ValueError("The number of foil data files must match the number of wing sections.")

    # Constants
    beta=0
    sw = 30
    fsec = 1
    xsing = 0.008
    ysing = 0
    trail = 10
    slopt = -0.125
    ysym = 0
    def export_tecplot():
        logging.basicConfig(stream=sys.stdout, level=logging.INFO)

        tecplot.session.connect(port=7600)
        '''if '-c' in sys.argv:
            tecplot.session.connect()'''

        #data_file = './cfl3d/flow-field.dat'
        data_file = "BLWF/tp1.plt"
        dataset = tecplot.data.load_tecplot(data_file)

        # Activate a 2D field plot (likely appropriate for flow field data)
        frame = tecplot.active_frame()
        frame.plot_type = PlotType.Cartesian3D
        #plot = frame.plot()
        plot = frame.plot(PlotType.Cartesian3D)
        def export_cloud_map(variable_name, output_path):
            contour = plot.contour(0)
            contour.variable = dataset.variable(variable_name)
            contour.levels.reset_to_nice()

            # Export the image
            tecplot.export.save_png(output_path, 600, supersample=3)
            # print(f'{variable_name} cloud map saved as {output_path}')

        # Set the plot type to Field Plot (XYLine, Contour, or Vector)
        #frame.plot_type = PlotType.Cartesian3D  # Setting plot type for a 2D plot (appropriate for contour/vector)
        frame.plot().show_contour = True
        frame.plot().contour(0).levels.reset_to_nice()
        plot.view.fit()   
        plot.view.width = 27.53
        plot.view.alpha = -135.07
        plot.view.theta = 138.1
        plot.view.psi = 47.78
        plot.view.position = (-118.88, 146.70, 180.66)


        export_cloud_map('CP', 'BLWF/CP_cloud.png')

        # 添加显示图片的代码（用Image.open打开）
        img = Image.open('./BLWF/CP_cloud.png')
        plt.imshow(img)
        plt.axis('off')  # 不显示坐标轴
        plt.show()
    # Function to read foil data from a file and get XU, YU, XL, YL, along with nu and nl
    def read_foil_data(filepath):
        xu, yu, xl, yl = [], [], [], []
        with open(filepath, 'r') as f:
            lines = f.readlines()
            # Read XU and YU first
            xu_start = lines.index('<   XU   ><   YU   >\n') + 1
            xl_start = lines.index('<   XL   ><   YL   >\n') + 1
            for line in lines[xu_start:xl_start - 1]:
                values = line.split()
                #print(values)
                if len(values) == 2:
                    x, y = map(float, values)
                    xu.append(x)
                    yu.append(y)
            # Read XL and YL
            for line in lines[xl_start:]:
                values = line.split()
                if len(values) == 2:
                    x, y = map(float, values)
                    xl.append(x)
                    yl.append(y)
        nu = len(xu)  # Number of upper surface coordinates
        nl = len(xl)  # Number of lower surface coordinates
        #print(nu, nl)
        return xu, yu, xl, yl, nu, nl
    '''files_to_delete = ['./BLWF/input.total', './BLWF/BLWF58.OUT']
    
    for file in files_to_delete:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                #print(f"已删除文件: {file_path}")
            except OSError as e:
                print(f"无法删除文件 {file_path}: {e}")'''
    # Read the original file
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # Modify the flow parameters (MACH, ALPHA, RE, etc.)
    for i, line in enumerate(lines):
        if "<  MACH  >" in line or "<MACH>" in line:
            lines[i + 1] = f"   {mach:<5.2f}    {alpha:<5.1f}     {beta:<5.1f}     {re:<10.1f}   {cax:<5.3f}    {sw:<5.1f}\n"
            break

    # Find the section to clear
    start_idx, end_idx = None, None
    for i, line in enumerate(lines):
        if "WING/BODY DATA" in line:
            start_idx = i
        elif "<  XLEF  ><  YLEF  ><  XTEF  ><  YTEF  ><  XTEF0 ><   NSF  >" in line:
            end_idx = i
            break
    
    if start_idx is not None and end_idx is not None:
        # Clear the section between start_idx and end_idx
        del lines[start_idx + 1:end_idx]

    # Re-insert the headers and data after clearing the section
    section_start_idx = start_idx + 1  # Insert after 'WING/BODY DATA'

    # Insert the NSW value
    lines.insert(section_start_idx, '<   NSW  >\n')
    lines.insert(section_start_idx + 1, f"    {nsw:<6.3f}\n")
    section_start_idx += 2

    for idx in range(nsw):
        # Read foil data for the current section
        xu, yu, xl, yl, nu, nl = read_foil_data(foil_paths[idx])

        # Section header 1: Z, XLE, YLE, CHORD, THICK, EPSIL, FSEC (on one line)
        lines.insert(section_start_idx, '<    Z   ><   XLE  ><   YLE  ><  CHORD ><  THICK ><  EPSIL ><  FSEC  >\n')
        section_start_idx += 1
        lines.insert(
            section_start_idx,
            f"  {z[idx]:<7.2f}   {xle[idx]:<7.4f}   {yle[idx]:<7.4f}   {chord[idx]:<8.2f}   {thick[idx]:<7.4f}   {epsil[idx]:<7.4f}   {fsec:<6.2f}\n"
        )
        section_start_idx += 1

        # Section header 2: YSYM, NU, NL (on one line)
        lines.insert(section_start_idx, '<  YSYM  ><   NU   ><   NL   >\n')
        section_start_idx += 1
        lines.insert(
            section_start_idx,
            f"   {ysym:<8.5f}   {nu:<6.1f}   {nl:<6.1f}\n"
        )
        section_start_idx += 1

        # Section header 3: XSING, YSING, TRAIL, SLOPT (on one line)
        lines.insert(section_start_idx, '<  XSING ><  YSING ><  TRAIL ><  SLOPT >\n')
        section_start_idx += 1
        lines.insert(
            section_start_idx,
            f"   {xsing:<6.4f}   {ysing:<6.4f}   {trail:<8.4f}   {slopt:<6.4f}\n"
        )
        section_start_idx += 1

        # Append XU and YU data
        lines.insert(section_start_idx, '<   XU   ><   YU   >\n')
        section_start_idx += 1
        for x, y in zip(xu, yu):
            lines.insert(section_start_idx, f"{x:<8.6f}  {y:<8.6f}\n")
            section_start_idx += 1
        
        # Append XL and YL data
        lines.insert(section_start_idx, '<   XL   ><   YL   >\n')
        section_start_idx += 1
        for x, y in zip(xl, yl):
            lines.insert(section_start_idx, f"{x:<8.6f}  {y:<8.6f}\n")
            section_start_idx += 1

    
    # Write the updated lines back to the file
    with open(file_path, 'w') as file:
        file.writelines(lines)
        
    cmd = 'input.058\n'

    try:
        # 使用 Popen 来运行 cfl3d_Seq_LHR.exe，并通过 stdin 传递 y 以自动确认提示
        p = subprocess.Popen(
            ['./blwf58.exe'],
            cwd="./BLWF",
            stdin=subprocess.PIPE,  # 允许传递输入
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # 通过 stdin 传递 'y' 并继续运行
        #stdout = p.communicate(input=cmd.encode())[0]
        stdout, stderr = p.communicate(input=cmd.encode(), timeout=150)
        if stdout:
            try:
                print("Standard Output (binary):")
                print(stdout)
            except UnicodeDecodeError:
                print("Standard Output: Binary data received, unable to decode.")
        else :
            print("no stdout")
        # Print standard error if available
        if stderr:
            try:
                print("Standard Error: ", stderr.decode('utf-8'))
            except UnicodeDecodeError:
                print("Standard Error: Binary error message received, unable to decode.")
        else :
            print("no stderr")        
    except Exception as e:
        print(f"运行过程中发生错误: {str(e)}")
    print(f"Modified {file_path} with MACH={mach}, ALPHA={alpha}, RE={re}, CAX={cax}, SW={sw}, and WING/BODY DATA with NSW={nsw} sections and foil data.")
    
    total_file = './BLWF/input.total'
    output_file = './BLWF/BLWF58.OUT'
    if os.path.exists(total_file):
        with open(total_file, 'r') as f:
            export_tecplot()
            return f.read()
    else:
        print(f"找不到 {total_file} 文件")

    # If input.total does not exist, try to read BLWF58.OUT file
    if os.path.exists(output_file):
        with open(output_file, 'r') as f:
            content = f.readlines()

        # Return the last 20 lines of BLWF58.OUT
        last_20_lines = ''.join(content[-20:]) if len(content) >= 20 else ''.join(content)
        return f"未找到 {total_file} 文件，以下是 {output_file} 文件的最后二十行：\n{last_20_lines}"
    else:
        print(f"找不到输出文件 {output_file}")
        return "找不到任何输出文件，可能未成功调用BLWF，请检查输入"
    '''if os.path.exists(output_file):
        with open(output_file, 'r') as f:
            content = f.readlines()

        # Find the line containing the specific marker
        start_indices = [i + 1 for i, line in enumerate(content) 
                        if "Business jet geometry (example G_T)" in line]
        
        if not start_indices:
            print("未找到指定的标记行")
            last_20_lines = ''.join(content[-20:]) if len(content) >= 20 else ''.join(content)
            return f"未找到指定的标记行，可能计算未收敛，以下是输出文件最后二十行：\n{last_20_lines}"

        # Get the last occurrence index
        start_index = start_indices[-1]

        # Extract lines after the marker
        extracted_lines = content[start_index:]

        # Join the extracted lines into a single string
        extracted_text = ''.join(extracted_lines)
        return extracted_text
    else:
        print(f"找不到输出文件 {output_file}")
        return "找不到输出文件，可能未成功调用BLWF，请检查输入"
'''
    #print(f"Modified {file_path} with new values: MACH={mach}, ALPHA={alpha}, BETA={beta}, RE={re}, CAX={cax}, SW={sw}")
#Z, Xle, Yle, chord, thick, epsil can be linearly interpolated by LLM
# Example usage

def design_parameters_from_llm(original_prompt):
    # Constructing the full prompt with original dialogue and example
    '''prompt = f"""
    用户输入: {original_prompt}
    如用户提供参数不完整，可自行设置。优先考虑用户设置。
    在自行设置参数时，请结合物理实际给出合理的数值，下给出各参数含义：
    (z): 参考翼型截面在机翼展向上的位置
    (xle): 参考翼型截面的前缘流向坐标（以第一个前缘点为0坐标零点），决定机翼后掠角
    (yle): 参考翼型截面的前缘高度坐标（以第一个前缘点为0坐标零点），决定机翼上反角
    (chord): 参考翼型截面的当地弦长， 后缘坐标xte=xle+chord **注意，弦长取值需要满足两个约束：1.根梢比/梢根比，2.后缘坐标xte要求
    (thick): 参考翼型截面的厚度，翼根部为1，出于结构考虑翼尖一般大于0.5
    (epsil): 参考翼型截面的扭转角度，单位为度，从根部到翼尖由正变负，翼尖取值为负，绝对值一般小于3度

    ### 其他几何参数：（[le]代指前缘坐标，[te]代指后缘坐标）
    **前缘后掠角**:
    前缘后掠角（lambda）是指机翼前缘线相对于机身垂直线方向的角度，常用来减小翼尖的空气阻力。计算公式为：
    lambda_i = arctan(Delta_xle/ Delta_z) 注意：后掠角一定要用当地的z差值与xle差值来计算
    lambda_i = arctan((xle[i] - xle[i-1]) / (z[i] - z[i-1]))
    给定前缘后掠角时xle[i] = xle[i-1] + (z[i] - z[i-1]) * tan(lambda_i)
    当机翼前后段前缘后掠角不同时，保证连续性；当前缘后掠角为零时，xle保持不变

    **上反角**:
    上反角（gamma）是指机翼相对于水平面的倾斜角度，通常用于提高飞行稳定性。计算公式为：
    gamma_i = arctan((y[le][i+1] - y[le][i]) / (z[i+1] - z[i]))

    **后缘坐标**:
    后缘坐标通过前缘坐标和弦长计算得到。对于每个翼段，后缘坐标 `xte[i]` 可由公式计算：
    xte[i] = xle[i] + chord[i]
    为了保证后缘坐标的线性分布，需根据后缘的掠角（beta）进行调整，即beta_i = arctan(xte[i]-xte[i-1])/(z[i] - z[i-1])
    给定后缘掠角（beta）时xte[i] = xte[i-1] + (z[i] - z[i-1]) * tan(beta_i)，由此确定当地弦长chord[i]
    *注意区分前缘后掠角（lambda）与后缘掠角（beta）

    **展弦比**:
    展弦比是机翼的展长与平均弦长的比值，用于衡量机翼的长宽比。其计算公式为：
    Aspect Ratio = (展长)^2 / 总翼面积
    展长（span）即机翼的最大展向长度。总翼面积可以通过计算各翼段的面积并相加得到。

    **根梢比**:
    根尖比是翼根弦长与尖部弦长之比。它可以描述机翼的形状特征。其计算公式为：
    Root-to-Tip Ratio = chord[0] / chord[-1]
    其中 `chord[0]` 是翼根的弦长，`chord[-1]` 是翼尖的弦长。

    **尖根比**:
    梢根比是翼尖弦长与根部弦长之比。它是根尖比的倒数。其计算公式为：
    Tip-to-Root Ratio = chord[-1] / chord[0]
    设置弦长时优先保证根梢比/梢根比！

    设计的几何参数需满足上述参数要求。
    机翼示例：
    [
    参考长度为2.5， 7个截面，带后掠角，无上反角：
    z = [0.00000, 1.30000, 2.60000, 3.90000, 5.20000, 10.40000, 13.00000]
    xle = [0.0, 0.84423, 1.68846, 2.53269, 3.376919, 6.753839, 8.442299]
    yle = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    chord = [4.68, 4.316, 3.952, 3.588, 3.224, 1.768, 1.04]
    thick = [1.0000, 1.0000, 0.8800, 0.766667, 0.666667, 0.6200, 0.6000]
    epsil = [1.0, 1.0, 2.5, 2.0, 1.5, 0.5, -1.5]
    ]，
    [
    参考长度为0.25， 4个截面，带后掠角与上反角：
    z = [0.00000, 0.15, 0.30, 0.83906]
    xle = [0.00000, 0.07599, 0.15199, 0.42218]
    yle = [0.00000, 0.01484, 0.02969, 0.08246]
    chord = [0.38, 0.30, 0.22, 0.070]
    thick = [1.00000, 1.00000, 1.00, 0.83400]
    epsil = [3.0, 3.0, 0.00, -1.00]
    ],

    请根据用户需求，参照所给例子，给出各参数
    请严格遵循编程计算得到结果，不要随意更改！
    返回格式如下：
    z = <list>
    xle = <list>
    yle = <list>
    chord = <list>
    thick = <list>
    epsil = <list>
    """
    #仅提供最终的结果，无需展示推理过程'''
    prompt = f"""
    用户输入: {original_prompt}
    ## 任务要求
    根据用户提供的参数(若不完整，可自行设置合理的数值)，计算并返回以下机翼几何参数：
    - z: 参考翼型截面在机翼展向上的位置 (单位: 米)，由展长确定
    - xle: 参考翼型截面的前缘流向坐标 (单位: 米，以第一个前缘点为0坐标零点)，决定前缘后掠角
    - yle: 参考翼型截面的前缘高度坐标 (单位: 米，以第一个前缘点为0坐标零点)，决定上反角
    - chord: 参考翼型截面的当地弦长 (单位: 米)，决定后缘后掠角 
    #注：注意区分前缘后掠角与后缘后掠角，后缘后掠角可以小于零，此时为后缘前掠角
    - thick: 参考翼型截面的厚度 (无量纲，翼根部为1)
    - epsil: 参考翼型截面的扭转角度 (单位: 度)

    ## 步骤框架 (ReAct Approach)

    ### 1. 理解用户输入
    - 检查用户输入的完整性，并总结已提供的参数。如果缺失或不完整，标记所需补充的参数。
    - **思考**: 用户提供的参数是否齐全？需要补充哪些？
    - **动作**: 1.标记缺失的参数并准备推理计算。
                2.确定机翼的截面数，确保所有列表长度与截面数一致。例如，如果截面数为4，则所有参数列表应包含4个元素。

    ### 2. 确定展向位置参数 (z)
    - **思考**: 是否需要自行设置z参数，或者使用用户提供的值？关注展长与参考长度
    - **动作**: 确定z的列表，确保参数符合物理实际和设计要求。
    - **结果**: z = <list>
    - **例子**: z = [0.00000, 1.30000, 2.60000, 3.90000, 5.20000, 10.40000, 13.00000]（参考长度2.5m，展长13m）

    ### 3. 计算前缘流向坐标 (xle)
    - **思考**: 是否需要自行设置xle参数?如需自行设置，往往需要根据前缘后掠角计算xle，是否给定了前缘后掠角？若未给定是否可以从其他参数推出？
    - **动作**: 根据前缘后掠角lambda或给定约束计算xle。
    - **公式**: xle[i] = xle[i-1] + (z[i] - z[i-1]) * tan(lambda)
    - **结果**: xle = <list>

    ### 4. 确定前缘高度坐标 (yle)
    - **思考**: 是否需要根据上反角gamma来计算yle？
    - **动作**: 使用公式gamma = arctan((yle[i+1] - yle[i]) / (z[i+1] - z[i]))来计算或调整yle。
    - **结果**: yle = <list>

    ### 5. 计算弦长 (chord)
    - **思考**: 给定条件有几个约束？是否可以唯一确定chord？
    - **公式**: 
        ((xle[i] + chord[i]) - (chord[i-1] + xle[i-1])) / (z[i] - z[i-1]) = tan(beta)  （后缘掠角beta）
        chord[0] / chord[-1] = lambda （lambda根梢比,而梢根比 = 1/lambda）
    - **动作**: 
    1. 确定chord是否唯一确定。如果提供了后缘掠角 beta、根梢比/梢根比与 xle，则chord是唯一确定的，此时不可以自行赋值。
    2. 将给定的参数代入公式，根据 beta、xle、根梢比，列出方程组并使用 `scipy` 库中的 `fsolve` 求解。
    3. 若chord不唯一确定，需考虑自由度的数量，并根据参考长度与翼展，为翼根或翼尖弦长合理赋值，再利用约束条件确定其余chord。
    4. 注意区分前缘后掠角（lambda）与后缘后掠角（beta）。后缘后掠角可以小于零，此时为后缘前掠角，例如后缘前掠角15度即为后缘后掠角beta = -15°

    - **编程示例**:
        ```python
        import math
        from scipy.optimize import fsolve

        # 给定的 xle 和 z 值（示例）
        xle = [0.0, 0.8, 1.6, 2.4, 3.2, 4.0, 4.8]  # 示例 xle 数据
        z = [0.0, 2.5, 5.0, 7.5, 10.0, 12.5, 13.0]  # 示例 z 数据
        root_to_tip_ratio = 1.2  # 根梢比

        def equations(chord):
            eqs = []
            # 根梢比约束: chord[0] = 1.2 * chord[-1]
            eqs.append(chord[0] - root_to_tip_ratio * chord[-1])
            
            # 后缘线垂直约束: (xle[i] + chord[i]) = (xle[i-1] + chord[i-1])
            for i in range(1, len(z)):
                eqs.append((xle[i] + chord[i]) - (xle[i-1] + chord[i-1]))
            
            return eqs

        chord_initial_guess = [1.0] * len(z)
        chord_solution = fsolve(equations, chord_initial_guess)
        chord_solution = [round(c, 3) for c in chord_solution]
        print("chord:",chord_solution)
    - **结果**: chord = <list>

    ### 6. 确定厚度参数 (thick)
    - **思考**: 用户提供的厚度是否合理？需要调整或设置默认值吗？
    - **动作**: 检查和调整thick值，使其在合理范围内，翼根处为1，翼尖处一般大于等于0.6（出于结构考虑）
    - **示例**：thick = [1.0000, 1.0000, 0.8800, 0.766667, 0.666667, 0.6200, 0.6000]
    - **结果**: thick = <list>

    ### 7. 计算扭转角度 (epsil)
    - **思考**: 扭转角度是否符合设计原则？绝对值是否在3度以内？翼尖扭转角是否小于0？
    - **动作**: 确定epsil参数，若缺失则设置合理默认值。示例：epsil = [1.0, 1.0, 2.5, 2.0, 1.5, 0.5, -1.5]
    - **结果**: epsil = <list>

    ## 思维步骤与返回格式
    逐步执行并打印每一步的推理和计算代码，确保每个步骤均有详细解释和验证。最后返回以下格式的计算结果：
    """
    print("original",original_prompt)
    messages = [
        {
            "role": "system",
            "content": """
            您是一名气动设计助手，旨在帮助翼身融合体的机翼参数设计.
            每个步骤都请严格通过编程计算，给出完整的python运算代码(附带print()语句输出结果)
            """
        },
        {
            "role": "user",
            "content": prompt
        }
    ]
    # API call to LLM2 to generate the design parameters
    # Step 2: glm4-plus generates the code
    response_code = client.chat.completions.create(
            model="glm-4-plus",
            messages=messages,
            temperature=0.5,
)
    generated_code_answer = response_code.choices[0].message.content
    print("Designer:",generated_code_answer)
    execution_messages = [
    {
        "role": "system",
        "content": """
        您是一名编程助手，请编译用户输入的代码并返回运行结果.（保留三位有效数字）
        返回格式如下：
            z = <list>
            xle = <list>
            yle = <list>
            chord = <list>
            thick = <list>
            epsil = <list>
            """
    },
    {"role": "user", "content": f"这是AI助手给出的推理过程与代码，请编译并返回结果:\n{generated_code_answer}"}
    ]   
    response_glm_all_tools = client.chat.completions.create(
    model="glm-4-alltools",
    messages=execution_messages,
    stream=True,
     tools=[{
        "type": "code_interpreter"
      }],
    temperature=0.2,
)
    execution_result = ""

    # Process each chunk as it arrives
    for chunk in response_glm_all_tools:
   # 检查chunk中的choices是否有内容
        if chunk.choices:
            choice = chunk.choices[0]
            delta = choice.delta
            # 检查delta中是否有content
            if delta and delta.content:
                execution_result += delta.content
        
        # 打印完整的回复内容
    # Step 3: Send generated code to glm-all-tools for interpretation and execution
    #execution_result = response_glm_all_tools.choices[0].message.content  # Result of code execution
    print("interpreter:", execution_result)

    # Step 4: Send the execution result back to glm4-plus to format the final response
    final_deresponsive_messages = [
    {"role": "user", "content": original_prompt},
    {"role": "assistant", "content": f"推理与编程流程{generated_code_answer}"},  # Code generated by `glm4-plus`
    {"role": "assistant", "content": f"编译运行结果:\n{execution_result}"},  # Result from `glm-all-tools`
    {"role": "user", "content":"""
            您是该问题的总结助手，请根据用户输入与其余assistant的计算结果输出最终回答。
            ##思考：
            各assistant生成的最终答案是否满足用户要求？根据最终结果，用户的要求又是否合理？
            ##动作：
            1. 检查其余assistant生成的最终答案是否满足用户要求，若不满足请及时修正；
            2. 根据最终结果，判断用户设置要求是否合理
            (如，弦长、展长、后掠角之间相对大小是否相容合理，举例：弦长远远大于展长，则可能是前后掠角与根梢比不兼容)；
            3. 生成最终的参数总结，输出格式要求如下：
            z = <list>
            xle = <list>
            yle = <list>
            chord = <list>
            thick = <list>
            epsil = <list>
            ##输出要求：
            若答案与要求合理，则隐藏推理过程，只给出最终输出参数；若有不合理处，则和参数一起输出。
            """}
    ]
    final_response = client.chat.completions.create(
    model="glm-4-plus",
    messages=final_deresponsive_messages,
    temperature=0.1,
)
    return final_response.choices[0].message.content

'''full_reply = ""

# Process each chunk as it arrives
for chunk in response:
# 检查chunk中的choices是否有内容
    if chunk.choices:
        choice = chunk.choices[0]
        delta = choice.delta
        # 检查delta中是否有content
        if delta and delta.content:
            full_reply += delta.content
    
    # 打印完整的回复内容
print("Assistant's full reply:", full_reply)'''
#content = response.choices[0].message.content
#print(content)
'''content = """
z = [0.0, 2.0, 4.0, 6.0, 8.0, 10.0]
xle = [0.0, 1.401, 2.802, 4.203, 5.604, 7.005]
yle = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
chord = [4.0, 3.598, 3.196, 2.794, 2.392, 1.99]
thick = [1.000, 0.900, 0.800, 0.700, 0.600, 0.600]
epsil = [3.0, 2.0, 1.0, 0.0, -1.0, -1.0]
"""'''
# Extracting the parameters from the LLM2 output
'''z_pattern = r"z = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"
xle_pattern = r"xle = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"
yle_pattern = r"yle = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"
chord_pattern = r"chord = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"
thick_pattern = r"thick = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"
epsil_pattern = r"epsil = \[([-\d\.]+(?:,\s*[-\d\.]+)*)\]"

def extract_list(pattern, content):
    match = rre.search(pattern, content)
    if match:
        # 提取匹配内容并去除空格，然后分割成列表
        numbers = match.group(1).strip('[]').replace(' ', '').split(',')
        return [float(x) for x in numbers if x]  # 确保非空字符串
    return []

z = extract_list(z_pattern, content)
xle = extract_list(xle_pattern, content)
yle = extract_list(yle_pattern, content)
chord = extract_list(chord_pattern, content)
thick = extract_list(thick_pattern, content)
epsil = extract_list(epsil_pattern, content)
generated_parameters = {
    'z': z,
    'xle': xle,
    'yle': yle,
    'chord': chord,
    'thick': thick,
    'epsil': epsil
}
return generated_parameters'''
"""
z = [0.0, 2.0, 4.0, 6.0, 8.0, 10.0]
    xle = [0.0, 1.401, 2.802, 4.203, 5.604, 7.005]
    yle = [0.0, 0.353, 0.706, 1.058, 1.411, 1.763]
    chord = [25.936, 25.071, 24.207, 23.342, 22.477, 21.613]
    thick = [1.0, 0.92, 0.84, 0.76, 0.68, 0.6]
    epsil = [1.0, 0.5, 0.0, -0.5, -1.0, -1.5]
    """

from cst_modeling.section import cst_foil_fit


def read_coordinates(coord_path):
    """
    读取坐标文件内容，并分离上下表面坐标。
    """
    with open(coord_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    x_upper = []
    y_upper = []
    x_lower = []
    y_lower = []

    # 标志位，用于判断当前读取的是上表面还是下表面
    reading_upper = True

    for line in lines[0:]:  
        if not line.strip():  # 遇到空行，切换到下表面
            reading_upper = False
            print("reading_upper:", reading_upper)
            continue

        x_, y_ = re.split(r'[,\s]+', line.strip())
        if reading_upper:
            x_upper.append(float(x_))
            y_upper.append(float(y_))
        else:
            x_lower.append(float(x_))
            y_lower.append(float(y_))
    #print(x_upper, y_upper, x_lower, y_lower)
    # 将列表转换为 numpy 数组
    # 对上表面坐标按x从小到大排序
    sorted_upper = sorted(zip(x_upper, y_upper), key=lambda x: x[0])
    xu0 = np.array([x for x, y in sorted_upper])
    yu0 = np.array([y for x, y in sorted_upper])
    
    # 对下表面坐标按x从小到大排序
    sorted_lower = sorted(zip(x_lower, y_lower), key=lambda x: x[0])
    xl0 = np.array([x for x, y in sorted_lower])
    yl0 = np.array([y for x, y in sorted_lower])
    
    # 检查并插入 (0, 0) 到 xu0 或 xl0
    if xu0[0] != 0:
        xu0 = np.insert(xu0, 0, 0)
        yu0 = np.insert(yu0, 0, 0)
    if xl0[0] != 0:
        xl0 = np.insert(xl0, 0, 0)
        yl0 = np.insert(yl0, 0, 0)
    #print(xu0, yu0, xl0, yl0)
    return xu0, yu0, xl0, yl0

def optimize_airfoil_rl(
    s0=None,
    initial_airfoil_file=None,
    idx_to_change=None,
    max_iterations=1,
    a_scaling=1/1000,
    valid_states_file_path=None,
    use_delta_r=True,
    parallelize=False,
    num_cpu=10,
    render_freq=1,
    use_custom_policy=True,
    network_arch=[1024, 256, 64],
    checkpoint_timesteps=1000,
    epochs=5,
    device='cpu',
    aerodynamic_method='xfoil',
    alpha=0,
    re=1000000,
    only_optimize=True  # 新增参数：仅优化（不训练）模式
):
    """
    这是一个用于翼型优化强化学习的封装函数，支持两种模式：
      1. 仅优化模式（only_optimize=True）：首先尝试加载训练保存的模型，然后直接进行优化评估。
      2. 训练模式（only_optimize=False）：在训练过程中保存模型，训练结束后加载最终模型评估，并提取优化后的翼型参数。

    参数说明：
      s0: 初始翼型参数数组。如果为 None，则默认使用上下表面的 CST 参数（或者根据初始翼型文件转换）。
      initial_airfoil_file: 初始翼型文件路径。若提供该文件路径，则读取文件内容并转换为 CST 参数。
      idx_to_change: 指定可修改的参数索引列表。如果为 None，则默认为所有参数可变。
      max_iterations: 环境的最大迭代次数（默认 100）。
      a_scaling: 动作缩放因子（默认 1/1000）。
      valid_states_file_path: 有效状态文件路径。若为 None，则默认使用当前文件所在目录下的 '/Dataset/Arrays_as_rows.txt'。
      use_delta_r: 是否启用奖励差值（默认 True）。
      parallelize: 是否使用并行环境（默认 False）。
      num_cpu: 当 parallelize 为 True 时使用的 CPU 核心数量（默认 10）。
      render_freq: 环境渲染频率（每隔多少步渲染一次，默认 5）。
      use_custom_policy: 是否使用自定义策略网络结构（默认 True）。
      network_arch: 自定义策略网络的层结构（默认 [1024, 256, 64]）。
      checkpoint_timesteps: 每个检查点训练的步数（默认 1000）。
      epochs: 总训练周期数（默认 5）。
      device: PPO 模型运行的设备（默认 'cpu'）。
      aerodynamic_method: 使用的气动计算方法，默认为 'xfoil'。
      only_optimize: 布尔值，是否仅进行优化（不重新训练），默认为 True。如果为 True，则尝试加载已保存的最终模型进行优化评估。

    返回：
      返回一个字典，包含：
        - "best_airfoil": 最优翼型参数（即 CST 参数数组）
        - "best_reward": 最优回报
        - "step_count": 评价时所经历的步数
        - "optimized_coordinates": 优化后翼型的坐标
        - "model_path": 最终模型保存的路径
    """
    import os
    import numpy as np
    import gymnasium as gym
    import glob
    from stable_baselines3 import PPO
    from stable_baselines3.common.env_util import make_vec_env
    from stable_baselines3.common.vec_env import SubprocVecEnv
    from stable_baselines3.common.callbacks import BaseCallback
    from CFD_Gym_Env_cst import CFD_Env

    # 如果提供初始翼型文件，则使用该文件转换为 CST 参数
    if initial_airfoil_file is not None:
        xu0, yu0, xl0, yl0 = read_coordinates(initial_airfoil_file)
        cst_u, cst_l = cst_foil_fit(xu0, yu0, xl0, yl0, n_cst=10)
        print("cst_u, cst_l",cst_u, cst_l)
        s0 = np.concatenate([np.array(cst_u), np.array(cst_l)])
    elif s0 is None:
        '''cst_u = np.array([0.17378267829122868, 0.15073785610630724, 0.18357732792518197, 0.11446859861572967,
                           0.19839269734964182, 0.09906728888357696, 0.1740958329569974, 0.12351445878696438,
                           0.1456680916679622, 0.14020738372975486])
        cst_l = np.array([-0.17378267829122868, -0.15073785610630724, -0.18357732792518197, -0.11446859861572967,
                           -0.19839269734964182, -0.09906728888357696, -0.1740958329569974, -0.12351445878696438,
                           -0.1456680916679622, -0.14020738372975486])'''
        cst_u = np.array([0.12829643177391645, 0.12670862525344279, 0.1606589807886445, 0.14942386357241327, 0.1510288421715979, 0.22416928196725572, 0.16078174754459032, 0.20998555159715607, 0.18608794869412873, 0.21052324151140955])
        cst_l = np.array([-0.12927128221479178, -0.1317606101646666, -0.1704496358631683, -0.07045476431407044, -0.3388806447571235, 0.009919233004564054, -0.20070721037035344, -0.03536712956528351, -0.0439749614611046, 0.06436195194554621])
        s0 = np.concatenate([cst_u, cst_l])
    
    # 如果没有指定可修改参数索引，则默认所有参数都可改变
    if idx_to_change is None:
        idx_to_change = list(range(len(s0)))
    
    # 如果未指定有效状态文件路径，则默认使用当前文件目录下的 Dataset 文件夹中的文件
    if valid_states_file_path is None:
        valid_states_file_path = os.path.join(os.path.dirname(__file__), 'Dataset', 'Arrays_as_rows.txt')
    
    # 根据参数设置算法名称
    algorithm_name = 'PPO'
    if use_delta_r:
        algorithm_name += '_DeltaR'
    if parallelize:
        algorithm_name += '_Parallel'
    if use_custom_policy:
        algorithm_name += '_PolicyArch_' + '_'.join(map(str, network_arch))
        policy_kwargs = dict(net_arch=dict(pi=network_arch, vf=network_arch))
    else:
        policy_kwargs = None

    # 设置保存模型和 Tensorboard 日志的目录
    models_dir = os.path.join(os.path.dirname(__file__), 'Models_cst', algorithm_name)
    log_dir = os.path.join(os.path.dirname(__file__), 'Logs_cst')
    os.makedirs(models_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)

    final_model_path = os.path.join(models_dir, f"{checkpoint_timesteps * epochs}")

    # 自定义渲染回调，每隔 render_freq 步进行渲染
    class RenderCallback(BaseCallback):
        def __init__(self, render_freq, verbose=0):
            super(RenderCallback, self).__init__(verbose)
            self.render_freq = render_freq

        def _on_step(self) -> bool:
            if self.n_calls % self.render_freq == 0:
                if hasattr(self.training_env, 'envs'):
                    for env in self.training_env.envs:
                        if hasattr(env, 'env'):
                            env.env.render(mode='human')
                        else:
                            env.render(mode='human')
                else:
                    self.training_env.render(mode='human')
            return True

    # 无论是否仅优化，先创建训练环境
    if parallelize:
        training_env = make_vec_env(
            lambda: CFD_Env(s0, idx_to_change, max_iterations, a_scaling, valid_states_file_path, use_delta_r,
                            aerodynamic_method=aerodynamic_method),
            n_envs=num_cpu,
            vec_env_cls=SubprocVecEnv
        )
    else:
        training_env = CFD_Env(s0, idx_to_change, max_iterations, a_scaling, valid_states_file_path, use_delta_r,
                               aerodynamic_method=aerodynamic_method)
        training_env.reset()

    # 仅优化模式：加载保存的模型
    if only_optimize:
        checkpoint_files = glob.glob(os.path.join(models_dir, '*.zip'))
        if checkpoint_files:
            checkpoint_files.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0].split('_')[-1]))
            saved_model_path = checkpoint_files[-1]
            print(f"检测到已保存模型，加载模型: {saved_model_path}")
            model = PPO.load(saved_model_path, env=training_env, tensorboard_log=log_dir,
                             policy_kwargs=policy_kwargs, device=device)
        else:
            print("未找到保存的模型，请先训练模型或者将 only_optimize 设置为 False。")
            training_env.close()
            return None
    # 训练模式：训练并保存模型
    else:
        model = PPO(
            'MlpPolicy',
            training_env,
            verbose=1,
            n_steps=50,
            tensorboard_log=log_dir,
            policy_kwargs=policy_kwargs,
            device=device
        )
    
        render_callback = RenderCallback(render_freq=render_freq)
    
        for i in range(1, epochs + 1):
            print(f"开始第 {i} 个训练周期，总步数: {checkpoint_timesteps}...")
            model.learn(
                total_timesteps=checkpoint_timesteps,
                reset_num_timesteps=False,
                tb_log_name=algorithm_name,
                progress_bar=True,
                callback=render_callback
            )
            save_path = os.path.join(models_dir, str(checkpoint_timesteps * i))
            model.save(save_path)
            print(f"模型已保存至 {save_path}")
    
        training_env.close()
    
        print(f"加载最终模型: {final_model_path}")
        model = PPO.load(final_model_path, device=device)
    
    # ----------------- 混合策略优化评估阶段 -----------------
    # 在仅优化模式下，采用混合策略进行评估：创建评价环境时设置 hybrid_mode=True
    eval_env = CFD_Env(s0, idx_to_change, max_iterations, a_scaling, valid_states_file_path, use_delta_r,
                       aerodynamic_method=aerodynamic_method, hybrid_mode=True)
    # 必须设置策略，以便 hybrid_step 中调用 model.predict() 生成候选状态
    eval_env.set_policy(model)
    obs, info = eval_env.reset()
    
    from tqdm import tqdm

    best_reward = -float("inf")
    best_airfoil = obs.copy()  # 用 copy 防止后续更新 obs 导致最佳状态被覆盖
    step_count = 0

    for _ in tqdm(range(max_iterations), desc="Evaluation Progress"):
        # 直接调用 hybrid_step 进行一步混合策略评估（候选数量与挑选个数可根据需要调整）
        obs, reward, terminated, truncated, info = eval_env.hybrid_step(model, num_candidates=50, top_k=3)
        step_count += 1
        if reward > best_reward:
            best_reward = reward
            best_airfoil = obs.copy()
        # 每个 iteration 都调用 render
        eval_env.render(mode='human')
        if terminated or truncated:
            break
    eval_env.close()
    
    print(f"优化训练完成！最佳回报: {best_reward}")
    print("最终优化得到的翼型参数为：", best_airfoil)
    
    # 将最佳翼型参数拆分为上表面和下表面的 CST 参数（假设参数个数相等）
    half_len = len(best_airfoil) // 2
    cst_u_opt = best_airfoil[:half_len]
    cst_l_opt = best_airfoil[half_len:]
    
    # 保存优化后的翼型坐标并绘制图形
    try:
        import matplotlib.pyplot as plt
        from Aerodynamics import Airfoil
    except ImportError as e:
        print("Matplotlib 或 Aerodynamics 模块导入失败:", e)
    
    # 获取原始翼型坐标（使用初始传入的 s0）
    original_airfoil = Airfoil.from_cst_parameters(s0[:len(s0)//2], s0[len(s0)//2:], airfoil_name="Original_Airfoil")
    original_coordinates = original_airfoil.coordinates

    optimized_airfoil = Airfoil.from_cst_parameters(cst_u_opt, cst_l_opt, airfoil_name="Optimized_Airfoil")
    coordinates = optimized_airfoil.coordinates
    coord_save_path = os.path.join(os.path.dirname(__file__), 'Optimized_Airfoil_coordinates.txt')
    np.savetxt(coord_save_path, coordinates, fmt='%.6f', delimiter=',', comments='')#header='x,y'
    print(f"优化后的翼型坐标已保存至: {coord_save_path}")
    
    plt.figure(figsize=(8, 4))
    plt.plot(original_coordinates[:, 0], original_coordinates[:, 1], 'k--', label='Original Airfoil')
    plt.plot(coordinates[:, 0], coordinates[:, 1], 'b-', label='Optimized Airfoil')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Original vs Optimized Airfoil Shape')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')
    plt.show()
    plot_save_path = os.path.join(os.path.dirname(__file__), 'Optimized_Airfoil.png')
    plt.savefig(plot_save_path)
    plt.close()
    print(f"优化后的翼型图已保存至: {plot_save_path}")
    
    # 新增：使用 CFL3D 重新评估最优翼型
    def evaluate_airfoil_with_cfl3d(cst_u, cst_l,  alpha, re):
        """
        使用 CFL3D 评估给定 CST 参数的翼型，并返回升阻比。
        """
        # 1. 从 CST 参数生成翼型坐标文件
        airfoil = Airfoil.from_cst_parameters(cst_u, cst_l, airfoil_name="temp_airfoil")

        # 2. 调用 cfl3d_simulation 函数进行计算
        result = airfoil.get_L_by_D(Re=re, angle_of_attack=alpha, method='cfl3d')
        # 3. 从结果中提取升阻比 (假设 result 包含 'lift_to_drag_ratio')
        if result is not None:
            return result
        else:
            print("CFL3D 计算未返回有效的升阻比。")
            return None

    # 假设在优化时使用的 Mach, Alpha, Re 与此处相同
    final_reward = evaluate_airfoil_with_cfl3d(cst_u_opt, cst_l_opt, alpha, re) # 替换为你实际使用的参数
    if final_reward is not None:
        print(f"使用 CFL3D 重新评估的最终升阻比（Reward）: {final_reward}")
    else:
        print("CFL3D 计算未返回有效的升阻比。")
    # 返回最佳翼型及其他相关信息的字典
    return {
        "best_airfoil_file": coord_save_path,
        #"best_reward": best_reward,  # 这里的 best_reward 仍然是强化学习优化过程中的最佳值
        "final_reward": final_reward,  # 新增：CFL3D 重新计算的 reward
        "step_count": step_count,
        "optimized_coordinates": coordinates,
        "model_path": final_model_path
    }

def generate_airfoil_from_cst(cst_u, cst_l, num_points=201, airfoil_name="cst_airfoil", output_path=None, tail_thickness=0.0):
    """
    从CST参数生成翼型坐标文件
    
    参数:
        cst_u (numpy.ndarray): 上表面CST参数
        cst_l (numpy.ndarray): 下表面CST参数
        num_points (int): 生成的坐标点数量
        airfoil_name (str): 翼型名称
        output_path (str): 输出文件路径，如果为None则使用airfoil_name.dat作为文件名
        tail_thickness (float): 尾缘厚度，默认为0
        
    返回:
        str: 生成的翼型坐标文件路径
        numpy.ndarray: 翼型坐标数组 [[x, y], ...]
    """
    from cst_modeling.section import cst_foil
    import numpy as np
    import os
    
    # 使用cst_foil函数生成翼型坐标
    x, yu, yl, tmax, rLE = cst_foil(num_points, cst_u, cst_l, x=None, t=None, tail=tail_thickness)
    
    # 创建坐标数组
    coordinates = np.zeros((2*num_points-1, 2))
    
    # 上表面坐标（从后缘到前缘）
    for i in range(num_points):
        coordinates[i, 0] = x[num_points-1-i]
        coordinates[i, 1] = yu[num_points-1-i]
    
    # 下表面坐标（从前缘到后缘，不包括前缘点，因为已经包含在上表面中）
    for i in range(1, num_points):
        coordinates[num_points-1+i, 0] = x[i]
        coordinates[num_points-1+i, 1] = yl[i]
    
    # 确定输出文件路径
    if output_path is None:
        output_path = os.path.join(os.path.dirname(__file__), f'{airfoil_name}.dat')
    
    # 写入坐标文件
    try:
        with open(output_path, 'w') as f:
            f.write(f'{airfoil_name}\n')
            for point in coordinates:
                f.write(f'{point[0]:.6f} {point[1]:.6f}\n')
    except Exception as e:
        print(f"保存翼型坐标文件失败: {e}")
        return None
    
    print(f"翼型坐标已保存至: {output_path}")
    print(f"最大厚度: {tmax:.4f}, 前缘半径: {rLE:.4f}")
    
    return output_path

if __name__ == '__main__':
    output_result = cfl3d_simulation(input_foil_file = "RAE2822.dat", mach=0.8, alpha=0, re=16.3e6)
    print(output_result)
    '''from CFD_Gym_Env_cst import CFD_Env
    from Aerodynamics import Airfoil
    cst_u = np.array([0.17378267829122868, 0.15073785610630724, 0.18357732792518197, 0.11446859861572967,
                           0.19839269734964182, 0.09906728888357696, 0.1740958329569974, 0.12351445878696438,
                           0.1456680916679622, 0.14020738372975486])
    cst_l = np.array([-0.17378267829122868, -0.15073785610630724, -0.18357732792518197, -0.11446859861572967,
                           -0.19839269734964182, -0.09906728888357696, -0.1740958329569974, -0.12351445878696438,
                           -0.1456680916679622, -0.14020738372975486])
    optimized_airfoil = Airfoil.from_cst_parameters(cst_u, cst_l, airfoil_name="naca0012")
    coordinates = optimized_airfoil.coordinates
    coord_save_path = os.path.join(os.path.dirname(__file__), 'naca0012.dat')
    np.savetxt(coord_save_path, coordinates, fmt='%.6f', delimiter=',', comments='')#header='x,y'''
    #optimize_airfoil_rl(initial_airfoil_file='RAE2822(1).dat',max_iterations=3)
    """output_result = cfl3d_simulation(input_foil_file = "NACA 64206.dat", mach=0.6, alpha=2, re=2000000)
    print(output_result)
    output_result = cfl3d_simulation(input_foil_file = "NACA 64206.dat", mach=2, alpha=2, re=10000000)
    print(output_result)"""
    #results = VLM3D_calculation(airspeed=100)
    #print(results)
    '''input_foil_file = 'RAE2822(1).dat'  # 输入翼型文件 (相对于 ./cfl3d 目录)
    mach_value = 0.75  # Mach 数
    alpha_value = 2.50  # 攻角
    re_value = 16.3e6  # 雷诺数
    output_folder = 'output'  # 输出文件夹
    cmd = 'y\n'
    logging.basicConfig(stream=sys.stdout, level=logging.INFO)

    tecplot.session.connect(port=7600)
    if '-c' in sys.argv:
        tecplot.session.connect()

    #data_file = './cfl3d/flow-field.dat'
    data_file ="E:/Python/AirGPT/BLWF/tp1.plt"
    dataset = tecplot.data.load_tecplot(data_file)

    # Activate a 2D field plot (likely appropriate for flow field data)
    frame = tecplot.active_frame()
    #plot = frame.plot()
    plot = frame.plot(PlotType.Cartesian3D)
    def export_cloud_map(variable_name, output_path):
        contour = plot.contour(0)
        contour.variable = dataset.variable(variable_name)
        contour.levels.reset_to_nice()

        # Export the image
        tecplot.export.save_png(output_path, 600, supersample=3)
        # print(f'{variable_name} cloud map saved as {output_path}')

    # Set the plot type to Field Plot (XYLine, Contour, or Vector)
    #frame.plot_type = PlotType.Cartesian3D  # Setting plot type for a 2D plot (appropriate for contour/vector)
    frame.plot().show_contour = True
    frame.plot().contour(0).levels.reset_to_nice()
    plot.view.fit()   
    #plot.view.alpha += 45.0  # 设置方位角为45度
    #plot.view.theta += 30.0  # 设置仰角为30度
    plot.view.psi += -65.0
    plot.view.alpha += 135.0
    #plot.view.theta += 30.0
    plot.view.fit()   

    # for variable in dataset.variables():
    #    print(variable.name)

    export_cloud_map('CP', 'E:/Python/AirGPT/BLWF/CP_cloud.png')'''

    '''input_foil_file = 'NACA 64206.dat' 
    result = BLWF(
    mach=0.8, 
    alpha=2.0, 
    re=3000000.0, 
    cax=2.5, 
    z = [0.0, 3.0, 6.0, 9.0, 12.0, 15.0],
    xle = [-4.61009657e-25, 1.39892297e+00, 2.79784595e+00, 4.19676892e+00, 5.59569190e+00, 6.99461487e+00],
    chord = [5.95075397, 5.35567857, 4.76060318, 4.16552778, 3.57045238, 2.97537699],
    yle = [0.0, 0.2625, 0.5249, 1.0539, 1.5829, 2.1119],
    thick = [1.0, 0.92, 0.84, 0.76, 0.68, 0.6],
    epsil = [1, 1, 2.5, 2, 1.5, -1.5],
    foil_paths=['foil_section1.dat','foil_section1.dat','foil_section1.dat', 'foil_section2.dat','foil_section2.dat','foil_section2.dat']  # Foil data files for each section
    #foil_paths=['NACA 64206.dat','NACA 64206.dat','NACA 64206.dat', 'NACA 64206.dat','NACA 64206.dat','NACA 64206.dat'] 
)'''
    '''result = BLWF(
    mach=0.8, 
    alpha=2.0, 
    re=3000000.0, 
    cax=2.5, 
    z = [0.0, 0.7, 4.4],
    xle = [0.0, 2.45, 6.45],
    yle = [0.0, 0.0, 0.0],
    chord = [8.51, 6.06, 1.1],
    thick = [1.0, 0.712, 0.129],
    epsil = [1.0, 1.0, -2.2],
    #foil_paths=['foil_section1.dat','foil_section1.dat','foil_section1.dat', 'foil_section2.dat','foil_section2.dat','foil_section2.dat']  # Foil data files for each section
    foil_paths=['NACA 64206.dat','NACA 64206.dat','NACA 64206.dat'] 
)
    print(result)'''
    '''result = subprocess.run(
    ['./cgrid.exe'],
    cwd="./cfl3d",
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE
    ) '''
    '''p = subprocess.Popen(
                    ['./cfl3d_Seq_LHR.exe'],
                    #['./cgrid'],
                    #['wine','./CFL3D_woker'],
                    cwd='./cfl3d/output',
                    stdin=subprocess.PIPE,  # 允许传递输入
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                )

                # 通过 stdin 传递 'y' 并继续运行
    stdout, stderr = p.communicate(input=cmd.encode(), timeout=150)

    # 打印输出和错误信息
    if stdout:
        try:
            print("Standard Output (binary):")
            print(stdout)
        except UnicodeDecodeError:
            print("Standard Output: Binary data received, unable to decode.")
    else :
        print("no stdout")
# Print standard error if available
    if stderr:
        try:
            print("Standard Error: ", stderr.decode('utf-8'))
        except UnicodeDecodeError:
            print("Standard Error: Binary error message received, unable to decode.")
    else :
        print("no stderr")'''
    #stdout = p.communicate(input=cmd.encode())[0]
    #export_cloud_maps()
    # 运行CFL3D仿真并获取输出结果'''

    #export_cloud_maps()
    '''if output_result:
        print("仿真结果输出：")
        print(output_result)'''
    '''prompt = "设置翼身融合体，机翼考虑六个截面，截面翼型前半段为foil_section1.dat, 后半段为foil_section2.dat，翼展为10 ，机翼前缘后掠角25度，后缘后掠角15，根梢比为2.5，设置一定扭转角，采用U型双折上反角"
    par = design_parameters_from_llm(prompt)
    print("final:", par)'''
    '''BLWF(
    mach=0.8, 
    alpha=2.0, 
    re=3000000.0, 
    cax=2.5, 
    z = [0.0, 2.0, 4.0, 6.0, 8.0, 10.0],
    xle = [0.0, 0.933, 1.867, 2.798, 3.73, 4.663],
    yle = [0.0, 0.353, 0.706, 1.058, 1.411, 1.763],
    chord = [3.306, 2.91, 2.513, 2.116, 1.719, 1.322],
    thick = [1.0, 0.92, 0.84, 0.76, 0.68, 0.6],
    epsil = [0.0, -0.3, -0.6, -0.9, -1.2, -1.5],
    foil_paths=['foil_section1.dat','foil_section1.dat','foil_section1.dat', 'foil_section2.dat','foil_section2.dat','foil_section2.dat']  # Foil data files for each section
)'''
    # reconstruct_curve('RAE2822(1).dat', 'foil.dat')
    #result = xfoil_calculation(foil='RAE2822.dat', re=460000, alfa=1.5)
    #print(result)
    # 调用示例
    '''input_foil_file = 'RAE2822(1).dat'  # 输入翼型文件 (相对于 ./cfl3d 目录)
    mach_value = 0.75  # Mach 数
    alpha_value = 2.50  # 攻角
    re_value = 16.3e6  # 雷诺数
    output_folder = 'output'  # 输出文件夹

    # 运行CFL3D仿真并获取输出结果
    output_result = cfl3d_simulation(input_foil_file, mach_value, alpha_value, re_value)

    if output_result:
        print("仿真结果输出：")
        print(output_result)
    # result = xfoil_calculation(foil='RAE2822.dat', re = 46000000, alfa = 2.5)
    # print(result)'''
    '''results = VLM3D_calculation(airspeed=10, alpha=5)
    print(results)'''

    #filepath = "RAE2822.dat"
    #results = xfoil_calculation(filepath, 200000, 2.5)
    #print(results)

    '''def write_dat_file(filename, foil_data, header=None):
        with open(filename, 'w') as f:
            if header:
                f.write(header + '\n')
            for x, z_lower, z_upper in foil_data:
                f.write(f"{x:.5f} {z_lower:.5f} {z_upper:.5f}\n")'''
'''### 任务要求
    根据用户提供的参数（若不完整，可自行设置合理的数值），计算并返回以下机翼几何参数：
    - z
    - xle
    - yle
    - chord
    - thick
    - epsil

    ### 参数定义及单位
    - **z**: 参考翼型截面在机翼展向上的位置 (单位: 米)，由展长确定
    - **xle**: 参考翼型截面的前缘流向坐标 (单位: 米，以第一个前缘点为0坐标零点)，根据后掠角与z得出
    - **yle**: 参考翼型截面的前缘高度坐标 (单位: 米，以第一个前缘点为0坐标零点)，根据上反角得出
    - **chord**: 参考翼型截面的当地弦长 (单位: 米)，需根据根梢比/梢根比以及后缘坐标要求求出
    *注意，在给定后缘坐标要求（即后缘掠角要求）与根梢比时，chord需要解方程得出，不能随意假设；给定后缘后掠角时则按公式自行计算；
    - **thick**: 参考翼型截面的厚度 (无量纲，翼根部为1)
    - **epsil**: 参考翼型截面的扭转角度 (单位: 度)
    
    ### 几何参数计算公式
    1. **前缘后掠角 (lambda)**:
    - 公式: lambda_i = arctan((xle[i] - xle[i-1]) / (z[i] - z[i-1]))
    - 给定后掠角时: xle[i] = xle[i-1] + (z[i] - z[i-1]) * tan(lambda_i)
    *注意：后掠角一定要用当地的z差值与xle差值来计算

    2. **上反角 (gamma)**:
    - 公式: gamma_i = arctan((yle[i+1] - yle[i]) / (z[i+1] - z[i]))

    3. **后缘坐标 (xte)**:
    - 公式: xte[i] = xle[i] + chord[i]
    - 给定后缘掠角 (beta) 时: xte[i] = xte[i-1] + (z[i] - z[i-1]) * tan(beta_i)， beta可正可负，后缘后掠时beta>0 ，后缘前掠时beta<0，后缘线垂直机身，beta=0
    - 弦长需满足根梢比/梢根比和后缘坐标要求，同时给定后缘坐标要求与根梢比时，需要解方程组来得到弦长chord，例如：
    ((xle[0] + chord[0])-(chord[-1] + xle[-1]))/(z[i] - z[i-1]) = tan(beta_i)
    chord[0] / chord[-1] = Root-to-Tip Ratio 或 chord[-1] / chord[0] = Tip-to-Root Ratio 

    4. **展弦比**:
    - 公式: Aspect Ratio = (展长)^2 / 总翼面积
    
    5. **根梢比**:
    - 公式: Root-to-Tip Ratio = chord[0] / chord[-1]
    
    6. **梢根比**:
    - 公式: Tip-to-Root Ratio = chord[-1] / chord[0]
    
    ### 示例
    **示例1**:
    - 参考长度: 2.5米
    - 截面数: 7
    - 后掠角: 有
    - 上反角: 无
    - 参数:
    - z = [0.00000, 1.30000, 2.60000, 3.90000, 5.20000, 10.40000, 13.00000]
    - xle = [0.0, 0.84423, 1.68846, 2.53269, 3.376919, 6.753839, 8.442299]
    - yle = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    - chord = [4.68, 4.316, 3.952, 3.588, 3.224, 1.768, 1.04]
    - thick = [1.0000, 1.0000, 0.8800, 0.766667, 0.666667, 0.6200, 0.6000]
    - epsil = [1.0, 1.0, 2.5, 2.0, 1.5, 0.5, -1.5]
    
    **示例2**:
    - 参考长度: 0.25米
    - 截面数: 4
    - 后掠角: 有
    - 上反角: 有
    - 参数:
    - z = [0.00000, 0.15, 0.30, 0.83906]
    - xle = [0.00000, 0.07599, 0.15199, 0.42218]
    - yle = [0.00000, 0.01484, 0.02969, 0.08246]
    - chord = [0.38, 0.30, 0.22, 0.070]
    - thick = [1.00000, 1.00000, 1.00, 0.83400]
    - epsil = [3.0, 3.0, 0.00, -1.00]

    
    ### 参数设置原则
    1. **优先考虑用户设置**：若用户提供参数，优先使用。
    2. **结合物理实际**：自行设置参数时，需结合物理实际给出合理数值。
    3. **满足约束条件**：
    - 弦长需满足根梢比/梢根比和后缘坐标要求，同时给定后缘坐标要求与根梢比时，需要解方程来得到弦长chord
    - 扭转角度绝对值一般小于3度。
    - 翼尖厚度一般大于0.5。
    - 上反角一般小于10度

    ### 注意事项
    - 确保前后缘后掠角和上反角的连续性。
    - 后缘坐标需根据后缘掠角进行调整。后缘掠角可正可负。
    - 总翼面积和展弦比需符合设计要求。

    ### 开始计算
    请根据上述要求和示例，理解各参数的实际应用和计算方法，进行参数计算并返回结果。

    ### 返回格式
    请按照以下格式返回计算结果：
    z = <list>
    xle = <list>
    yle = <list>
    chord = <list>
    thick = <list>
    epsil = <list>'''

'''def print_plot_view_parameters():
    import tecplot
    from tecplot.constant import PlotType
    tecplot.session.connect(port=7600)

    # 获取当前活动帧和绘图
    frame = tecplot.active_frame()
    plot = frame.plot(PlotType.Cartesian3D)
    view = plot.view

    # 输出当前视图参数
    print("当前视图参数：")
    print("宽度（width）：", view.width)
    print("alpha：", view.alpha)
    print("theta：", view.theta)
    print("psi：", view.psi)
    print("位置（position）：", view.position)

# 示例调用函数（确保在 Tecplot 会话初始化后调用）
if __name__ == '__main__':
    print_plot_view_parameters()'''

